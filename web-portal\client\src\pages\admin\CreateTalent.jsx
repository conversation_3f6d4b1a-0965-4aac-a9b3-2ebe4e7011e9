import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import { getUserRole } from '../../utils/rolePermissions';
import AdminLayout from '../../components/admin/AdminLayout';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

export default function CreateTalent() {
  const navigate = useNavigate();
  const { accessToken, user, isInitializing } = useAuthStore();
  const [batches, setBatches] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    companyEmail: '',
    batchId: '',
    group: '',
    designation: '',
    employmentType: 'Internship Ongoing',
    joinedAt: ''
  });

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;
    
    const userRole = getUserRole(user);
    if (!userRole) {
      navigate('/talent/dashboard');
    }
  }, [user, navigate, isInitializing]);

  // Fetch batches
  useEffect(() => {
    fetchBatches();
  }, []);

  async function fetchBatches() {
    try {
      const response = await fetch(`${API_URL}/admin/batches`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setBatches(data.batches);
      }
    } catch (err) {
      console.error('Failed to fetch batches:', err);
    }
  }

  async function handleSubmit(e) {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`${API_URL}/admin/talents`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(`Talent created successfully! Talent ID: ${data.talent.talentId}. Invite has been sent to ${data.talent.email}.`);
        setFormData({ name: '', email: '', companyEmail: '', batchId: '', group: '', designation: '', employmentType: 'Internship Ongoing', joinedAt: '' });
        
        // Redirect after 3 seconds
        setTimeout(() => {
          navigate('/admin/talents');
        }, 3000);
      } else {
        setError(data.error || 'Failed to create talent');
      }
    } catch (err) {
      setError('Failed to create talent. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  }

  const userRole = getUserRole(user);
  if (isInitializing || !userRole) {
    return null;
  }

  return (
    <AdminLayout>
      <div className="max-w-4xl mx-auto space-y-4">
        {/* Back Button */}
        <button
          onClick={() => navigate('/admin/talents')}
          className="text-blue-400 hover:text-blue-300 flex items-center gap-2 transition"
        >
          ← Back to Talents
        </button>

        {/* Form */}
        <div className="bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 backdrop-blur-sm rounded-lg p-4 border border-border/40">
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  id="name"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-4 py-3 bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 border border-border/40 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition"
                  placeholder="John Doe"
                />
              </div>

              {/* Personal Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                  Personal Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  required
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="w-full px-4 py-3 bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 border border-border/40 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition"
                  placeholder="<EMAIL>"
                />
                <p className="mt-2 text-sm text-gray-400">
                  Personal email for login and notifications
                </p>
              </div>

              {/* Company Email */}
              <div>
                <label htmlFor="companyEmail" className="block text-sm font-medium text-gray-300 mb-2">
                  Company Email Address (Jibble)
                </label>
                <input
                  type="email"
                  id="companyEmail"
                  value={formData.companyEmail}
                  onChange={(e) => setFormData({ ...formData, companyEmail: e.target.value })}
                  className="w-full px-4 py-3 bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 border border-border/40 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition"
                  placeholder="<EMAIL>"
                />
                <p className="mt-2 text-sm text-gray-400">
                  Company email used in Jibble for time tracking (optional but recommended)
                </p>
              </div>

              {/* Batch */}
              <div>
                <label htmlFor="batchId" className="block text-sm font-medium text-gray-300 mb-2">
                  Batch (Optional)
                </label>
                <select
                  id="batchId"
                  value={formData.batchId}
                  onChange={(e) => setFormData({ ...formData, batchId: e.target.value })}
                  className="w-full px-4 py-3 bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 border border-border/40 rounded-lg text-white focus:outline-none focus:border-blue-500 transition"
                >
                  <option value="">No Batch Assigned</option>
                  {batches.map((batch) => (
                    <option key={batch._id} value={batch.batchId}>
                      {batch.name} ({batch.batchId}) - {batch.status}
                    </option>
                  ))}
                </select>
                <p className="mt-2 text-sm text-gray-400">
                  You can assign a batch later if needed
                </p>
              </div>

              {/* Group */}
              <div>
                <label htmlFor="group" className="block text-sm font-medium text-gray-300 mb-2">
                  Group
                </label>
                <select
                  id="group"
                  value={formData.group}
                  onChange={(e) => setFormData({ ...formData, group: e.target.value })}
                  className="w-full px-4 py-3 bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 border border-border/40 rounded-lg text-white focus:outline-none focus:border-blue-500 transition"
                >
                  <option value="">Select Group</option>
                  <option value="core">Core</option>
                  <option value="growth">Growth</option>
                </select>
              </div>

              {/* Designation */}
              <div>
                <label htmlFor="designation" className="block text-sm font-medium text-gray-300 mb-2">
                  Designation
                </label>
                <select
                  id="designation"
                  value={formData.designation}
                  onChange={(e) => setFormData({ ...formData, designation: e.target.value })}
                  className="w-full px-4 py-3 bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 border border-border/40 rounded-lg text-white focus:outline-none focus:border-blue-500 transition"
                >
                  <option value="">Select Designation</option>
                  <option value="Frontend Developer">Frontend Developer</option>
                  <option value="Backend Developer">Backend Developer</option>
                  <option value="Full Stack Developer">Full Stack Developer</option>
                  <option value="Software Engineer">Software Engineer</option>
                  <option value="UI/UX Designer">UI/UX Designer</option>
                  <option value="Project Manager">Project Manager</option>
                  <option value="CEO">CEO</option>
                  <option value="CTO">CTO</option>
                  <option value="Product Manager">Product Manager</option>
                  <option value="DevOps Engineer">DevOps Engineer</option>
                  <option value="QA Engineer">QA Engineer</option>
                  <option value="Data Scientist">Data Scientist</option>
                  <option value="Mobile Developer">Mobile Developer</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              {/* Employment Type */}
              <div>
                <label htmlFor="employmentType" className="block text-sm font-medium text-gray-300 mb-2">
                  Employment Type *
                </label>
                <select
                  id="employmentType"
                  required
                  value={formData.employmentType}
                  onChange={(e) => setFormData({ ...formData, employmentType: e.target.value })}
                  className="w-full px-4 py-3 bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 border border-border/40 rounded-lg text-white focus:outline-none focus:border-blue-500 transition"
                >
                  <option value="Internship Ongoing">Internship Ongoing</option>
                  <option value="Internship Completed">Internship Completed</option>
                  <option value="Full Time Developer">Full Time Developer</option>
                  <option value="Project Manager">Project Manager</option>
                  <option value="Recruiting">Recruiting</option>
                </select>
              </div>

              {/* Join Date */}
              <div>
                <label htmlFor="joinedAt" className="block text-sm font-medium text-gray-300 mb-2">
                  Join Date
                </label>
                <input
                  id="joinedAt"
                  type="date"
                  value={formData.joinedAt}
                  onChange={(e) => setFormData({ ...formData, joinedAt: e.target.value })}
                  className="w-full px-4 py-3 bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 border border-border/40 rounded-lg text-white focus:outline-none focus:border-blue-500 transition"
                />
              </div>

              {/* Error */}
              {error && (
                <div className="bg-red-500/20 border border-red-500 text-red-400 px-4 py-3 rounded-lg">
                  {error}
                </div>
              )}

              {/* Success */}
              {success && (
                <div className="bg-green-500/20 border border-green-500 text-green-400 px-4 py-3 rounded-lg">
                  {success}
                </div>
              )}

              {/* Submit */}
              <div className="flex gap-4">
                <button
                  type="submit"
                  disabled={loading}
                  className="flex-1 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105 disabled:scale-100"
                >
                  {loading ? (
                    <span className="flex items-center justify-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-solid border-white border-r-transparent"></div>
                      Creating...
                    </span>
                  ) : (
                    'Create Talent & Send Invite'
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/admin/talents')}
                  disabled={loading}
                  className="px-3 py-1.5 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 hover:bg-card/80 disabled:bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-white rounded-lg transition"
                >
                  Cancel
                </button>
              </div>
            </form>

            {/* Info */}
            <div className="mt-6 pt-6 border-t border-border/40">
              <p className="text-sm text-gray-400">
                ℹ️ <strong>What happens next:</strong>
              </p>
              <ul className="mt-2 text-sm text-gray-400 space-y-1 ml-6">
                <li>• A unique Talent ID will be automatically generated</li>
                <li>• An invitation email will be sent to the provided email address</li>
                <li>• The talent will have 7 days to accept the invitation</li>
                <li>• Their status will be "Invited" until they complete onboarding</li>
              </ul>
            </div>
          </div>
        </div>
    </AdminLayout>
  );
}
