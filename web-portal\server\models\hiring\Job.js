import mongoose from "mongoose";
import slugify from "slugify";

const jobSchema = new mongoose.Schema(
  {
    // Basic Information
    title: {
      type: String,
      required: [true, "Job title is required"],
      trim: true,
      maxlength: [200, "Title cannot exceed 200 characters"],
      index: true,
    },

    description: {
      type: String,
      required: [true, "Job description is required"],
      maxlength: [5000, "Description cannot exceed 5000 characters"],
    },

    companyInfo: {
      type: String,
      required: [true, "Company information is required"],
      maxlength: [2000, "Company info cannot exceed 2000 characters"],
    },

    responsibilities: {
      type: String,
      required: [true, "Responsibilities are required"],
      maxlength: [3000, "Responsibilities cannot exceed 3000 characters"],
    },

    // Job Details
    experienceLevel: {
      type: String,
      required: [true, "Experience level is required"],
      enum: {
        values: ["fresher", "0-1", "1-2", "2-3", "3-5", "5+"],
        message: "{VALUE} is not a valid experience level",
      },
    },

    jobType: {
      type: String,
      required: [true, "Job type is required"],
      enum: {
        values: ["full-time", "part-time", "contract", "internship"],
        message: "{VALUE} is not a valid job type",
      },
    },

    location: {
      type: String,
      default: "Remote",
      immutable: true, // Cannot be changed after creation
    },

    compensationType: {
      type: String,
      required: [true, "Compensation type is required"],
      enum: {
        values: ["paid", "unpaid"],
        message: "{VALUE} is not a valid compensation type",
      },
    },

    // Status Management
    status: {
      type: String,
      enum: {
        values: ["draft", "published", "closed"],
        message: "{VALUE} is not a valid status",
      },
      default: "draft",
      index: true,
    },

    publishedAt: {
      type: Date,
      default: null,
    },

    closedAt: {
      type: Date,
      default: null,
    },

    // Archive Status (for soft delete)
    isArchived: {
      type: Boolean,
      default: false,
      index: true,
    },

    archivedAt: {
      type: Date,
      default: null,
    },

    // URL & SEO
    slug: {
      type: String,
      unique: true,
      lowercase: true,
      index: true,
    },

    // Statistics
    applicationsCount: {
      type: Number,
      default: 0,
      min: 0,
    },

    viewsCount: {
      type: Number,
      default: 0,
      min: 0,
    },

    // Visual Branding
    backgroundImage: {
      url: {
        type: String,
        trim: true,
      },
      publicId: {
        type: String,
        trim: true,
      },
    },

    bannerImage: {
      url: {
        type: String,
        trim: true,
      },
      publicId: {
        type: String,
        trim: true,
      },
    },

    // Admin Tracking
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Admin",
      required: true,
      index: true,
    },

    lastEditedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Admin",
      default: null,
    },

    lastEditedAt: {
      type: Date,
      default: null,
    },

    // Edit History
    editHistory: [
      {
        editedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Admin",
        },
        editedAt: {
          type: Date,
          default: Date.now,
        },
        changes: {
          type: String, // JSON string of changed fields
          maxlength: 1000,
        },
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Indexes for performance
jobSchema.index({ status: 1, createdAt: -1 });
jobSchema.index({ createdBy: 1, status: 1 });

// Pre-save hook to generate slug
jobSchema.pre("save", async function (next) {
  if (this.isModified("title") && !this.slug) {
    const baseSlug = slugify(this.title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });

    // Add month-year suffix for uniqueness
    const date = new Date();
    const monthYear = date
      .toLocaleString("en-US", { month: "short", year: "numeric" })
      .toLowerCase()
      .replace(" ", "-");

    this.slug = `${baseSlug}-${monthYear}`;

    // Ensure uniqueness
    let counter = 1;
    let uniqueSlug = this.slug;
    while (await mongoose.model("Job").exists({ slug: uniqueSlug })) {
      uniqueSlug = `${this.slug}-${counter}`;
      counter++;
    }
    this.slug = uniqueSlug;
  }

  // Prevent slug modification after creation
  if (this.isModified("slug") && !this.isNew) {
    const error = new Error("Slug cannot be modified after creation");
    return next(error);
  }

  next();
});

// Virtual for application link
jobSchema.virtual("applicationLink").get(function () {
  return `https://modelsuite.ai/apply/${this.slug}`;
});

// Ensure virtuals are included in JSON
jobSchema.set("toJSON", { virtuals: true });
jobSchema.set("toObject", { virtuals: true });

export default mongoose.model("Job", jobSchema);
