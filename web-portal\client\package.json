{"name": "web-portal-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ai-sdk/react": "^2.0.81", "@heroicons/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.8", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.4", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/vite": "^4.1.14", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "ai": "^5.0.81", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "emoji-picker-react": "^4.14.2", "firebase": "^12.4.0", "framer-motion": "^12.23.24", "lucide-react": "^0.545.0", "motion": "^12.23.24", "nanoid": "^5.1.6", "next-themes": "^0.4.6", "react": "^18.2.0", "react-day-picker": "^9.11.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-phone-number-input": "^3.4.13", "react-quill": "^2.0.0", "react-router-dom": "^6.21.1", "recharts": "^3.2.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "streamdown": "^1.4.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tui-image-editor": "^3.15.3", "use-stick-to-bottom": "^1.1.1", "zustand": "^5.0.8"}, "devDependencies": {"@types/node": "^24.7.1", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.6", "shadcn": "^3.4.0", "tailwindcss": "^4.1.14", "tw-animate-css": "^1.4.0", "vite": "^5.4.2"}}