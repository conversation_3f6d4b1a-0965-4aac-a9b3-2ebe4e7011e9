import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../store/authStore";
import { getUserRole } from "../../utils/rolePermissions";
import AdminLayout from "../../components/admin/AdminLayout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import {
  HiChat,
  HiClock,
  HiCube,
  HiUsers,
  HiEye,
  HiRefresh,
  HiChevronLeft,
  HiChevronRight,
} from "react-icons/hi";

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";

export default function AIFeedbackMonitor() {
  const navigate = useNavigate();
  const { user, accessToken, isInitializing } = useAuthStore();
  const [stats, setStats] = useState(null);
  const [sessions, setSessions] = useState([]);
  const [selectedSession, setSelectedSession] = useState(null);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterUserId, setFilterUserId] = useState("");
  const [users, setUsers] = useState([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showModal, setShowModal] = useState(false);

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;
    
    const userRole = getUserRole(user);
    if (!userRole) {
      navigate("/talent/dashboard");
    }
  }, [user, navigate, isInitializing]);

  // Fetch stats and sessions
  useEffect(() => {
    const userRole = getUserRole(user);
    if (userRole) {
      fetchStats();
      fetchSessions();
      fetchUsers();
    }
  }, [user, filterStatus, filterUserId, page]);

  async function fetchStats() {
    try {
      const response = await fetch(`${API_URL}/api/admin/ai-feedback/stats`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (err) {
      console.error("Error fetching stats:", err);
    }
  }

  async function fetchSessions() {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "10",
        ...(filterStatus !== "all" && { status: filterStatus }),
        ...(filterUserId && { userId: filterUserId }),
      });

      const response = await fetch(
        `${API_URL}/api/admin/ai-feedback/sessions?${params}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
        }
      );

      if (response.ok) {
        const data = await response.json();
        setSessions(data.sessions);
        setTotalPages(data.pagination.totalPages);
      } else {
        setError("Failed to fetch sessions");
      }
    } catch (err) {
      console.error("Error fetching sessions:", err);
      setError("Failed to fetch sessions");
    } finally {
      setLoading(false);
    }
  }

  async function fetchUsers() {
    try {
      // Fetch all talents from admin API
      const response = await fetch(`${API_URL}/admin/talents`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        // Map talents to user format
        const talentUsers = data.talents.map((talent) => ({
          userId: talent._id,
          name: talent.name,
          email: talent.email,
          talentId: talent.talentId,
        }));
        setUsers(talentUsers);
      }
    } catch (err) {
      console.error("Error fetching users:", err);
    }
  }

  async function viewSessionDetails(sessionId) {
    try {
      const response = await fetch(
        `${API_URL}/api/admin/ai-feedback/session/${sessionId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
        }
      );

      if (response.ok) {
        const data = await response.json();
        setSelectedSession(data.session);
        setMessages(data.messages);
        setShowModal(true);
      }
    } catch (err) {
      console.error("Error fetching session details:", err);
    }
  }

  async function cleanupStaleSessions() {
    if (
      !window.confirm(
        "Mark all sessions with no activity in 24+ hours as abandoned?"
      )
    ) {
      return;
    }

    try {
      const response = await fetch(
        `${API_URL}/api/admin/ai-feedback/cleanup-stale`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
        }
      );

      if (response.ok) {
        const data = await response.json();
        alert(`✅ ${data.message}`);
        // Refresh data
        fetchStats();
        fetchSessions();
      } else {
        alert("❌ Failed to cleanup stale sessions");
      }
    } catch (err) {
      console.error("Error cleaning up stale sessions:", err);
      alert("❌ Failed to cleanup stale sessions");
    }
  }

  function formatDuration(seconds) {
    if (!seconds) return "0s";
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ${Math.round(seconds % 60)}s`;
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  }

  function formatNumber(num) {
    if (!num && num !== 0) return "0";
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  }

  function getStatusBadge(status) {
    // Neutral tokenized badge for consistent dark-mode styling
    return "bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-muted-foreground border border-border/40";
  }

  if (loading && !stats) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-slate-400">Loading...</div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-4 p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between">
          <div className="flex-1 min-w-0 relative z-10">
            <h1 className="text-2xl font-bold truncate tracking-tight" style={{
              color: '#F1F5F9',
              textShadow: '0 2px 8px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 0, 0, 0.6)'
            }}>
              AI Feedback Monitor
            </h1>
            <p className="text-sm truncate mt-1" style={{
              color: '#CBD5E1',
              textShadow: '0 1px 4px rgba(0, 0, 0, 0.8), 0 0 12px rgba(0, 0, 0, 0.5)'
            }}>
              Track and analyze AI chat usage
            </p>
          </div>
          <div className="mt-3 sm:mt-0 ml-3 flex-shrink-0 flex items-center gap-2">
            <Button
              onClick={cleanupStaleSessions}
              variant="outline"
              className="flex items-center gap-2 border-border/40 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-muted-foreground hover:bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 hover:text-foreground whitespace-nowrap"
            >
              <HiClock className="w-4 h-4" />
              <span className="hidden sm:inline">Cleanup Stale</span>
            </Button>
            <Button
              onClick={() => {
                fetchStats();
                fetchSessions();
              }}
              className="flex items-center gap-2 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-foreground border border-border/40 hover:bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 whitespace-nowrap"
            >
              <HiRefresh className="w-4 h-4" />
              <span className="hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-2 gap-4 lg:grid-cols-4">
            {/* Total Sessions */}
            <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg rounded-xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Sessions
                </CardTitle>
                <div className="h-10 w-10 rounded-lg bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center">
                  <HiChat className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-white">
                  {formatNumber(stats.totalSessions || 0)}
                </div>
                <p className="text-xs text-slate-300 mt-1">
                  {stats.activeSessions || 0} active now
                </p>
              </CardContent>
            </Card>

            {/* Total Messages */}
            <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg rounded-xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Messages
                </CardTitle>
                <div className="h-10 w-10 rounded-lg bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center">
                  <HiCube className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-white">
                  {formatNumber(stats.totalMessages || 0)}
                </div>
                <p className="text-xs text-slate-300 mt-1">
                  {(stats.avgMessagesPerSession || 0).toFixed(1)} avg per
                  session
                </p>
              </CardContent>
            </Card>

            {/* Total Tokens */}
            <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg rounded-xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Tokens
                </CardTitle>
                <div className="h-10 w-10 rounded-lg bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center">
                  <HiClock className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-white">
                  {formatNumber(stats.totalTokensUsed || 0)}
                </div>
                <p className="text-xs text-slate-300 mt-1">
                  {formatNumber(stats.avgTokensPerSession || 0)} avg per session
                </p>
              </CardContent>
            </Card>

            {/* Unique Users */}
            <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg rounded-xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Unique Users
                </CardTitle>
                <div className="h-10 w-10 rounded-lg bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center">
                  <HiUsers className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-white">
                  {stats.uniqueUsers || 0}
                </div>
                <p className="text-xs text-slate-300 mt-1">
                  {stats.totalSessions > 0
                    ? (stats.totalSessions / stats.uniqueUsers).toFixed(1)
                    : 0}{" "}
                  sessions per user
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Top Users */}
        {stats?.topUsers && stats.topUsers.length > 0 && (
          <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg rounded-xl">
            <CardHeader>
              <CardTitle className="text-white">Top Users by Usage</CardTitle>
              <CardDescription className="text-muted-foreground">
                Most active users in the last 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.topUsers.map((user, index) => (
                  <div
                    key={user.userId}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-muted-foreground font-semibold border border-border/40">
                        {index + 1}
                      </div>
                      <div>
                        <p className="text-foreground font-medium">
                          {user.userName}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {user.userEmail}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-foreground font-semibold">
                        {user.sessionCount} sessions
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatNumber(user.totalTokens)} tokens
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Sessions List */}
        <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg rounded-xl">
          <CardHeader>
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between w-full">
              <div className="flex-1 min-w-0">
                <CardTitle className="text-white truncate">All Sessions</CardTitle>
                <CardDescription className="text-muted-foreground truncate">
                  View and monitor all AI chat sessions
                </CardDescription>
              </div>

              <div className="mt-3 sm:mt-0 ml-0 sm:ml-4 flex-shrink-0 flex items-center gap-3 w-full sm:w-auto px-4 sm:px-0">
                {/* User Filter */}
                <select
                  value={filterUserId}
                  onChange={(e) => {
                    setFilterUserId(e.target.value);
                    setPage(1);
                  }}
                  className="w-full sm:w-auto bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-foreground border border-border/40 rounded px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-ring/20 whitespace-nowrap"
                >
                  <option value="">All Talents</option>
                  {users.map((user) => (
                    <option key={user.userId} value={user.userId}>
                      {user.name} {user.talentId ? `(${user.talentId})` : ""}
                    </option>
                  ))}
                </select>

                {/* Status Filter */}
                <div className="flex gap-2">
                  <Button
                    variant={filterStatus === "all" ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      setFilterStatus("all");
                      setPage(1);
                    }}
                  >
                    All
                  </Button>
                  <Button
                    variant={filterStatus === "active" ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      setFilterStatus("active");
                      setPage(1);
                    }}
                  >
                    Active
                  </Button>
                  <Button
                    variant={
                      filterStatus === "completed" ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => {
                      setFilterStatus("completed");
                      setPage(1);
                    }}
                  >
                    Completed
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-4 text-slate-400">
                Loading sessions...
              </div>
            ) : sessions.length === 0 ? (
              <div className="text-center py-4 text-slate-400">
                No sessions found
              </div>
            ) : (
              <>
                <Table>
                  <TableHeader>
                    <TableRow className="border-border/40">
                      <TableHead className="text-muted-foreground">User</TableHead>
                      <TableHead className="text-muted-foreground">Started</TableHead>
                      <TableHead className="text-muted-foreground">Status</TableHead>
                      <TableHead className="text-muted-foreground">Messages</TableHead>
                      <TableHead className="text-muted-foreground">Tokens</TableHead>
                      <TableHead className="text-muted-foreground">Duration</TableHead>
                      <TableHead className="text-muted-foreground">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sessions.map((session) => (
                      <TableRow key={session._id} className="border-border/40">
                        <TableCell>
                          <div>
                            <p className="text-foreground font-medium">
                              {session.userId?.name || "Unknown"}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {session.userId?.email || "N/A"}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          {new Date(session.startedAt).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusBadge(session.status)}>
                            {session.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          {session.messageCount}
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          {formatNumber(session.totalTokens)}
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          {session.duration
                            ? formatDuration(session.duration)
                            : "N/A"}
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() =>
                              viewSessionDetails(session.sessionId)
                            }
                            className="text-muted-foreground hover:text-foreground"
                          >
                            <HiEye className="w-4 h-4 mr-1" />
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between mt-4">
                    <p className="text-sm text-slate-400">
                      Page {page} of {totalPages}
                    </p>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setPage((p) => Math.max(1, p - 1))}
                        disabled={page === 1}
                      >
                        <HiChevronLeft className="w-4 h-4" />
                        Previous
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          setPage((p) => Math.min(totalPages, p + 1))
                        }
                        disabled={page === totalPages}
                      >
                        Next
                        <HiChevronRight className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Session Details Modal */}
      {showModal && selectedSession && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-slate-700">
              <div>
                <h2 className="text-xl font-bold text-white">
                  Session Details
                </h2>
                <p className="text-slate-300 text-sm mt-1">
                  {selectedSession.userId?.name} •{" "}
                  {new Date(selectedSession.startedAt).toLocaleString()}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setShowModal(false);
                  setSelectedSession(null);
                  setMessages([]);
                }}
                className="text-muted-foreground hover:text-foreground"
              >
                ✕
              </Button>
            </div>

            {/* Session Stats */}
            <div className="grid grid-cols-4 gap-4 p-6 border-b border-border/40">
              <div>
                <p className="text-slate-300 text-sm">Messages</p>
                <p className="text-foreground font-semibold text-lg">
                  {selectedSession.messageCount}
                </p>
              </div>
              <div>
                <p className="text-slate-300 text-sm">Tokens Used</p>
                <p className="text-foreground font-semibold text-lg">
                  {formatNumber(selectedSession.totalTokens)}
                </p>
              </div>
              <div>
                <p className="text-slate-300 text-sm">Duration</p>
                <p className="text-foreground font-semibold text-lg">
                  {selectedSession.duration
                    ? formatDuration(selectedSession.duration)
                    : "N/A"}
                </p>
              </div>
              <div>
                <p className="text-slate-300 text-sm">Status</p>
                <Badge className={getStatusBadge(selectedSession.status)}>
                  {selectedSession.status}
                </Badge>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-6 space-y-4">
                {messages.length === 0 ? (
                <p className="text-slate-300 text-center py-4">
                  No messages in this session
                </p>
              ) : (
                messages.map((message) => (
                  <div
                    key={message._id}
                    className={`p-4 rounded-lg ${
                      message.role === "user"
                        ? "bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-border/40 ml-8"
                        : "bg-card/95 border border-border/40 mr-8"
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-semibold text-foreground capitalize">
                        {message.role}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(message.createdAt).toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="text-slate-300 whitespace-pre-wrap">
                      {message.content?.text || message.content}
                    </p>
                    {message.content?.images &&
                      message.content.images.length > 0 && (
                        <div className="mt-3 space-y-2">
                          {message.content.images.map((img, idx) => (
                            <div
                              key={idx}
                              className="relative rounded overflow-hidden"
                            >
                              <img
                                src={img.url}
                                alt={img.fileName || `Image ${idx + 1}`}
                                className="max-w-full h-auto rounded border border-border/40"
                                onError={(e) => {
                                  e.target.style.display = "none";
                                }}
                              />
                              <p className="text-xs text-slate-300 mt-1">
                                {img.fileName} • {(img.size / 1024).toFixed(1)}
                                KB
                              </p>
                            </div>
                          ))}
                        </div>
                      )}
                    {message.aiResponse?.tokensUsed && (
                      <p className="text-xs text-slate-300 mt-2">
                        Tokens: {message.aiResponse.tokensUsed} • Response time: {" "}
                        {message.aiResponse.responseTime}ms
                      </p>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
