import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { useAuthStore } from '../../store/authStore';
import { hasPermission } from '../../utils/rolePermissions';
import AdminLayout from '../../components/admin/AdminLayout';
import { 
  Euro, 
  Users, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  DollarSign,
  Calendar,
  Download,
  Search,
  Filter,
  RefreshCw,
  Eye,
  Check,
  X,
  Lock
} from 'lucide-react';

const AdminPaymentDashboard = () => {
  const { accessToken, user } = useAuthStore();
  const [payments, setPayments] = useState([]);
  const [summary, setSummary] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [talents, setTalents] = useState([]);
  const [createFormData, setCreateFormData] = useState({
    talentId: '',
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    baseAmount: '',
    status: 'pending',
    ruulInvoiceNumber: '',
    notes: ''
  });
  const [filters, setFilters] = useState({
    status: 'all',
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    search: ''
  });

  useEffect(() => {
    fetchPaymentData();
    fetchTalents();
  }, [filters]);

  const fetchTalents = async () => {
    try {
      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';
      const response = await fetch(`${API_URL}/admin/talents`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTalents(data.talents || []);
      }
    } catch (error) {
      console.error('Error fetching talents:', error);
    }
  };

  const handleCreatePayment = async (e) => {
    e.preventDefault();
    
    try {
      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';
      const response = await fetch(`${API_URL}/api/payments/admin/create`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(createFormData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create payment');
      }

      // Reset form and close modal
      setCreateFormData({
        talentId: '',
        month: new Date().getMonth() + 1,
        year: new Date().getFullYear(),
        baseAmount: '',
        status: 'pending',
        ruulInvoiceNumber: '',
        notes: ''
      });
      setShowCreateModal(false);
      
      // Refresh payment data
      await fetchPaymentData();
      
      alert('Payment created successfully!');
    } catch (error) {
      console.error('Error creating payment:', error);
      alert(error.message);
    }
  };

  const fetchPaymentData = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        status: filters.status !== 'all' ? filters.status : '',
        month: filters.month,
        year: filters.year,
        search: filters.search
      });

      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';
      const response = await fetch(`${API_URL}/api/payments/admin/overview?${params}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment data');
      }

      const data = await response.json();
      setPayments(data.data.payments || []);
      setSummary(data.data.summary || {});
    } catch (error) {
      console.error('Error fetching payment data:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentAction = async (paymentId, action) => {
    try {
      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';
      const response = await fetch(`${API_URL}/api/payments/admin/${paymentId}/${action}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to ${action} payment`);
      }

      // Refresh payment data
      await fetchPaymentData();
    } catch (error) {
      console.error(`Error ${action} payment:`, error);
      // You might want to show a toast notification here
    }
  };

  const getStatusColor = (status) => {
    return 'bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-muted-foreground border-border/40';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="w-4 h-4" />;
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'accumulated':
        return <TrendingUp className="w-4 h-4" />;
      case 'below_threshold':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <DollarSign className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Check if user has permission to view payments
  if (!canViewPayments) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <Card className="border-red-500/20 bg-red-500/5">
              <CardContent className="p-4 text-center">
                <Lock className="w-16 h-16 text-red-400 mx-auto mb-4" />
                <h1 className="text-xl font-bold text-red-400 mb-2">Access Denied</h1>
                <p className="text-gray-300 mb-4">
                  You don't have permission to access the Payment Management section.
                </p>
                <p className="text-gray-400 text-sm">
                  This section is restricted to Super Admins only. Please contact your system administrator if you need access.
                </p>
                <div className="mt-6 p-4 bg-gray-800/50 rounded-lg">
                  <p className="text-gray-300 text-sm">
                    <strong>Your Role:</strong> {useAuthStore.getState().user?.role || 'admin'}
                  </p>
                  <p className="text-gray-300 text-sm">
                    <strong>Payment Access:</strong> {canViewPayments ? 'Yes' : 'No'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="p-6">
          <Card className="bg-red-500/10 border-red-500/20">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2 text-red-400">
                <AlertCircle className="w-4 h-4" />
                <span>Error loading payment data: {error}</span>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={fetchPaymentData}
                  className="ml-auto"
                >
                  Retry
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="max-w-7xl mx-auto space-y-4">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="relative z-10">
            <h1 className="text-2xl font-bold mb-2 tracking-tight" style={{
              color: '#F1F5F9',
              textShadow: '0 2px 8px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 0, 0, 0.6)'
            }}>
              Payment Management
            </h1>
            <p className="text-sm" style={{
              color: '#CBD5E1',
              textShadow: '0 1px 4px rgba(0, 0, 0, 0.8), 0 0 12px rgba(0, 0, 0, 0.5)'
            }}>
              Manage talent payments and track financial overview
            </p>
          </div>
          <div className="flex space-x-2 mt-4 sm:mt-0">
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white border-0"
            >
              <DollarSign className="w-4 h-4 mr-2" />
              Create Payment
            </Button>
            <Button
              onClick={fetchPaymentData}
              disabled={isLoading}
              className="bg-slate-700 hover:bg-slate-600 text-white border-0"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button className="bg-slate-700 hover:bg-slate-600 text-white border-0">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 lg:gap-4">
          <Card className="border-border/40 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg hover:shadow-xl transition-all duration-200 overflow-hidden rounded-xl">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center">
                <div className="p-2 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 rounded-lg mr-2">
                  <Euro className="w-4 h-4 text-muted-foreground" />
                </div>
                Total Paid
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xl font-bold text-white">
                {formatCurrency(summary.totalPaid || 0)}
              </div>
              <p className="text-sm text-slate-300 mt-1">
                This period
              </p>
            </CardContent>
          </Card>

          <Card className="border-border/40 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg hover:shadow-xl transition-all duration-200 overflow-hidden rounded-xl">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center">
                <div className="p-2 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 rounded-lg mr-2">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                </div>
                Pending
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xl font-bold text-white">
                {formatCurrency(summary.totalPending || 0)}
              </div>
              <p className="text-sm text-slate-300 mt-1">
                {summary.pendingCount || 0} payments
              </p>
            </CardContent>
          </Card>

          <Card className="border-border/40 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg hover:shadow-xl transition-all duration-200 overflow-hidden rounded-xl">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center">
                <div className="p-2 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 rounded-lg mr-2">
                  <TrendingUp className="w-4 h-4 text-muted-foreground" />
                </div>
                Accumulated
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xl font-bold text-white">
                {formatCurrency(summary.totalAccumulated || 0)}
              </div>
              <p className="text-sm text-slate-300 mt-1">
                Below threshold
              </p>
            </CardContent>
          </Card>

          <Card className="border-border/40 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg hover:shadow-xl transition-all duration-200 overflow-hidden rounded-xl">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center">
                <div className="p-2 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 rounded-lg mr-2">
                  <Users className="w-4 h-4 text-muted-foreground" />
                </div>
                Total Talents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xl font-bold text-white">
                {summary.totalTalents || 0}
              </div>
              <p className="text-sm text-slate-300 mt-1">
                Active participants
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Filter className="w-4 h-4 mr-2 text-muted-foreground" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm text-muted-foreground block mb-2">Status</label>
                <select
                  value={filters.status}
                  onChange={(e) => setFilters({...filters, status: e.target.value})}
                  className="w-full bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70/50 border border-border/40 text-foreground px-3 py-2 rounded-md focus:ring-2 focus:ring-muted focus:border-border"
                >
                  <option value="all">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="paid">Paid</option>
                  <option value="failed">Failed</option>
                  <option value="unpaid">Unpaid</option>
                </select>
              </div>
              <div>
                <label className="text-sm text-muted-foreground block mb-2">Month</label>
                <select
                  value={filters.month}
                  onChange={(e) => setFilters({...filters, month: parseInt(e.target.value)})}
                  className="w-full bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70/50 border border-border/40 text-foreground px-3 py-2 rounded-md focus:ring-2 focus:ring-muted focus:border-border"
                >
                  {Array.from({length: 12}, (_, i) => (
                    <option key={i + 1} value={i + 1}>
                      {new Date(2023, i).toLocaleDateString('en-US', { month: 'long' })}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="text-sm text-muted-foreground block mb-2">Year</label>
                <select
                  value={filters.year}
                  onChange={(e) => setFilters({...filters, year: parseInt(e.target.value)})}
                  className="w-full bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70/50 border border-border/40 text-foreground px-3 py-2 rounded-md focus:ring-2 focus:ring-muted focus:border-border"
                >
                  {Array.from({length: 5}, (_, i) => {
                    const year = new Date().getFullYear() - i;
                    return (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    );
                  })}
                </select>
              </div>
              <div>
                <label className="text-sm text-muted-foreground block mb-2">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search talents..."
                    value={filters.search}
                    onChange={(e) => setFilters({...filters, search: e.target.value})}
                    className="w-full bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70/50 border border-border/40 text-foreground pl-10 pr-3 py-2 rounded-md placeholder-muted-foreground focus:ring-2 focus:ring-muted focus:border-border"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment List */}
        <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
          <CardHeader>
            <CardTitle className="text-white">Payment Records</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <RefreshCw className="w-4 h-4 animate-spin text-muted-foreground mr-2" />
                <span className="text-muted-foreground">Loading payments...</span>
              </div>
            ) : payments.length === 0 ? (
              <div className="text-center py-4">
                <DollarSign className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-slate-300">No payments found for the selected criteria</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border/40">
                      <th className="text-left py-3 px-4 text-muted-foreground">Talent</th>
                      <th className="text-left py-3 px-4 text-muted-foreground">Period</th>
                      <th className="text-left py-3 px-4 text-muted-foreground">Earned</th>
                      <th className="text-left py-3 px-4 text-muted-foreground">Net Amount</th>
                      <th className="text-left py-3 px-4 text-muted-foreground">Status</th>
                      <th className="text-left py-3 px-4 text-muted-foreground">Invoice #</th>
                      <th className="text-left py-3 px-4 text-muted-foreground">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {payments.map((payment, index) => (
                      <tr key={payment._id || index} className="border-b border-border/40 hover:bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70">
                        <td className="py-3 px-4">
                          <div className="text-foreground font-medium">{payment.talentName}</div>
                          <div className="text-sm text-muted-foreground">{payment.talentEmail}</div>
                        </td>
                        <td className="py-3 px-4 text-white">
                          {payment.month}/{payment.year}
                        </td>
                        <td className="py-3 px-4 text-white">
                          {formatCurrency(payment.earnedAmount)}
                          {payment.bonusAmount > 0 && (
                            <div className="text-xs text-muted-foreground">
                              +{formatCurrency(payment.bonusAmount)} bonus
                            </div>
                          )}
                        </td>
                        <td className="py-3 px-4 text-white">
                          {formatCurrency(payment.netAmount)}
                        </td>
                        <td className="py-3 px-4">
                          <Badge className="bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-muted-foreground border border-border/40 flex items-center space-x-1 w-fit">
                            {getStatusIcon(payment.status)}
                            <span className="capitalize">{payment.status.replace('_', ' ')}</span>
                          </Badge>
                        </td>
                        <td className="py-3 px-4 text-slate-300 text-sm">
                          {payment.ruulInvoiceNumber || '-'}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            {payment.status === 'pending' && (
                              <>
                                <Button 
                                  size="sm" 
                                  onClick={() => handlePaymentAction(payment._id, 'approve')}
                                  className="bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-foreground border border-border/40 hover:bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70"
                                >
                                  <Check className="w-4 h-4" />
                                </Button>
                                <Button 
                                  size="sm" 
                                  onClick={() => handlePaymentAction(payment._id, 'reject')}
                                  className="bg-card/40 text-muted-foreground border border-border/20 hover:bg-card/60"
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
        </div>

        {/* Create Payment Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <Card className="w-full max-w-2xl bg-slate-900 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center justify-between">
                  <span>Create Payment Manually</span>
                  <button 
                    onClick={() => setShowCreateModal(false)}
                    className="text-slate-400 hover:text-white"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleCreatePayment} className="space-y-4">
                  {/* Talent Selection */}
                  <div>
                    <label className="block text-sm text-slate-300 mb-2">
                      Talent / Employee *
                    </label>
                    <select
                      required
                      value={createFormData.talentId}
                      onChange={(e) => setCreateFormData({...createFormData, talentId: e.target.value})}
                      className="w-full bg-slate-800 border border-slate-600 text-white px-3 py-2 rounded-md focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select a talent...</option>
                      {talents.map(talent => (
                        <option key={talent._id} value={talent._id}>
                          {talent.name} ({talent.email})
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Month and Year */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm text-slate-300 mb-2">
                        Month *
                      </label>
                      <select
                        required
                        value={createFormData.month}
                        onChange={(e) => setCreateFormData({...createFormData, month: parseInt(e.target.value)})}
                        className="w-full bg-slate-800 border border-slate-600 text-white px-3 py-2 rounded-md focus:ring-2 focus:ring-blue-500"
                      >
                        {Array.from({length: 12}, (_, i) => i + 1).map(month => (
                          <option key={month} value={month}>
                            {new Date(2025, month - 1, 1).toLocaleDateString('en-US', { month: 'long' })}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm text-slate-300 mb-2">
                        Year *
                      </label>
                      <select
                        required
                        value={createFormData.year}
                        onChange={(e) => setCreateFormData({...createFormData, year: parseInt(e.target.value)})}
                        className="w-full bg-slate-800 border border-slate-600 text-white px-3 py-2 rounded-md focus:ring-2 focus:ring-blue-500"
                      >
                        {[2024, 2025, 2026].map(year => (
                          <option key={year} value={year}>{year}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Base Amount */}
                  <div>
                    <label className="block text-sm text-slate-300 mb-2">
                      Base Amount (€) *
                    </label>
                    <input
                      type="number"
                      required
                      min="0"
                      step="0.01"
                      value={createFormData.baseAmount}
                      onChange={(e) => setCreateFormData({...createFormData, baseAmount: e.target.value})}
                      placeholder="e.g., 1000"
                      className="w-full bg-slate-800 border border-slate-600 text-white px-3 py-2 rounded-md focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* Payment Status */}
                  <div>
                    <label className="block text-sm text-slate-300 mb-2">
                      Payment Status *
                    </label>
                    
                    <select
                      value={createFormData.status}
                      onChange={(e) => setCreateFormData({...createFormData, status: e.target.value})}
                      className="w-full bg-slate-800 border border-slate-600 text-white px-3 py-2 rounded-md focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="pending">Pending</option>
                      <option value="paid">Paid</option>
                      <option value="failed">Failed</option>
                      <option value="unpaid">Unpaid</option>
                    </select>
                  </div>

                  {/* Ruul Invoice Number */}
                  <div>
                    <label className="block text-sm text-slate-300 mb-2">
                      Ruul Invoice Number
                    </label>
                    <input
                      type="text"
                      value={createFormData.ruulInvoiceNumber}
                      onChange={(e) => setCreateFormData({...createFormData, ruulInvoiceNumber: e.target.value})}
                      placeholder="e.g., INV-2025-001"
                      className="w-full bg-slate-800 border border-slate-600 text-white px-3 py-2 rounded-md focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* Notes */}
                  <div>
                    <label className="block text-sm text-slate-300 mb-2">
                      Notes
                    </label>
                    <textarea
                      value={createFormData.notes}
                      onChange={(e) => setCreateFormData({...createFormData, notes: e.target.value})}
                      placeholder="Additional notes..."
                      rows={3}
                      className="w-full bg-slate-800 border border-slate-600 text-white px-3 py-2 rounded-md focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end space-x-3 pt-4">
                    <Button
                      type="button"
                      onClick={() => setShowCreateModal(false)}
                      className="bg-slate-700 hover:bg-slate-600 text-white border-0"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="bg-blue-600 hover:bg-blue-700 text-white border-0"
                    >
                      <DollarSign className="w-4 h-4 mr-2" />
                      Create Payment
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminPaymentDashboard;