import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../store/authStore';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import { 
  Users, 
  UserPlus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Power, 
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Shield,
  UserCheck,
  Briefcase,
  Award,
  Mail,
  Calendar,
  Clock,
  Settings
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import CreateAdminDialog from './CreateAdminDialog';
import EditAdminDialog from './EditAdminDialog';
import ConfirmDialog from './ConfirmDialog';
import AdminLayout from './AdminLayout';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

export default function AdminManagementPage() {
  const { accessToken } = useAuthStore();
  
  // State management
  const [admins, setAdmins] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [availableRoles, setAvailableRoles] = useState({});
  
  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  
  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedAdmin, setSelectedAdmin] = useState(null);
  const [confirmAction, setConfirmAction] = useState(null);
  
  // Statistics
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    byRole: {}
  });

  // Fetch available roles
  const fetchAvailableRoles = async () => {
    try {
      const response = await fetch(`${API_URL}/api/admin/management/roles`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch roles');
      }

      const data = await response.json();
      setAvailableRoles(data.roles || {});
    } catch (error) {
      console.error('Error fetching roles:', error);
      setError('Failed to load available roles');
    }
  };

  // Fetch admins with pagination and filters
  const fetchAdmins = async (page = 1) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(searchTerm && { search: searchTerm }),
        ...(roleFilter !== 'all' && { roleType: roleFilter }),
        ...(statusFilter !== 'all' && { status: statusFilter })
      });

      const response = await fetch(`${API_URL}/api/admin/management/admins?${params}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch admins');
      }

      const data = await response.json();
      setAdmins(data.data.admins || []);
      setCurrentPage(data.data.pagination.page);
      setTotalPages(data.data.pagination.totalPages);
      
      // Calculate stats
      const totalAdmins = data.data.pagination.total;
      const activeAdmins = data.data.admins.filter(admin => admin.isActive).length;
      const roleStats = {};
      
      data.data.admins.forEach(admin => {
        if (admin.roleType) {
          roleStats[admin.roleType] = (roleStats[admin.roleType] || 0) + 1;
        }
      });

      setStats({
        total: totalAdmins,
        active: activeAdmins,
        inactive: totalAdmins - activeAdmins,
        byRole: roleStats
      });
    } catch (error) {
      console.error('Error fetching admins:', error);
      setError('Failed to load admin users');
    } finally {
      setLoading(false);
    }
  };

  // Create new admin
  const handleCreateAdmin = async (formData) => {
    try {
      const response = await fetch(`${API_URL}/api/admin/management/admins`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create admin');
      }

      setCreateDialogOpen(false);
      fetchAdmins(currentPage);
      
      // Show success message (you might want to add a toast system)
      alert(`Admin created successfully! ${data.message}`);
    } catch (error) {
      console.error('Error creating admin:', error);
      alert(error.message);
    }
  };

  // Edit admin
  const handleEditAdmin = async (adminId, formData) => {
    try {
      const response = await fetch(`${API_URL}/api/admin/management/admins/${adminId}/role`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update admin');
      }

      setEditDialogOpen(false);
      setSelectedAdmin(null);
      fetchAdmins(currentPage);
      
      alert('Admin updated successfully!');
    } catch (error) {
      console.error('Error updating admin:', error);
      alert(error.message);
    }
  };

  // Toggle admin status
  const handleToggleStatus = async (admin) => {
    try {
      const response = await fetch(`${API_URL}/api/admin/management/admins/${admin._id}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isActive: !admin.isActive
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update admin status');
      }

      fetchAdmins(currentPage);
      alert(`Admin ${admin.isActive ? 'deactivated' : 'activated'} successfully!`);
    } catch (error) {
      console.error('Error updating admin status:', error);
      alert(error.message);
    }
  };

  // Delete admin
  const handleDeleteAdmin = async (admin) => {
    try {
      const response = await fetch(`${API_URL}/api/admin/management/admins/${admin._id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete admin');
      }

      setConfirmDialogOpen(false);
      setSelectedAdmin(null);
      setConfirmAction(null);
      fetchAdmins(currentPage);
      
      alert('Admin deleted successfully!');
    } catch (error) {
      console.error('Error deleting admin:', error);
      alert(error.message);
    }
  };

  // Open edit dialog
  const openEditDialog = (admin) => {
    setSelectedAdmin(admin);
    setEditDialogOpen(true);
  };

  // Open confirm dialog
  const openConfirmDialog = (admin, action) => {
    setSelectedAdmin(admin);
    setConfirmAction(action);
    setConfirmDialogOpen(true);
  };

  // Handle search
  const handleSearchChange = (value) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle filter changes
  const handleFilterChange = (type, value) => {
    if (type === 'role') {
      setRoleFilter(value);
    } else if (type === 'status') {
      setStatusFilter(value);
    }
    setCurrentPage(1);
  };

  // Get role icon
  const getRoleIcon = (roleType) => {
    const icons = {
      super_admin: Shield,
      full_admin: UserCheck,
      hiring_manager: Briefcase,
      employee_manager: Users,
      team_lead: Award
    };
    
    return icons[roleType] || Users;
  };

  // Get role color for theme consistency
  const getRoleColor = (roleType) => {
    const colors = {
      super_admin: 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
      full_admin: 'text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800',
      hiring_manager: 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',
      employee_manager: 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
      team_lead: 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800'
    };
    
    return colors[roleType] || 'text-muted-foreground bg-muted border-border';
  };

  // Role badge component
  const RoleBadge = ({ roleType, roleInfo, permissionMode, customPermissions }) => {
    // Handle custom permissions
    if (permissionMode === 'custom') {
      const Icon = Settings;
      return (
        <div className="flex flex-col gap-1">
          <Badge className="text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/20 border-indigo-200 dark:border-indigo-800 border flex items-center gap-1">
            <Icon size={12} />
            Custom Permissions
          </Badge>
          <div className="text-xs text-muted-foreground">
            {customPermissions?.length || 0} features
          </div>
        </div>
      );
    }
    
    // Handle role-based permissions
    const Icon = getRoleIcon(roleType);
    return (
      <Badge className={`${getRoleColor(roleType)} border flex items-center gap-1`}>
        <Icon size={12} />
        {roleInfo?.name || roleType}
      </Badge>
    );
  };

  // Status badge component
  const StatusBadge = ({ isActive }) => {
    return isActive ? (
      <Badge className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 border-green-200 dark:border-green-800 border">
        <CheckCircle size={12} className="mr-1" />
        Active
      </Badge>
    ) : (
      <Badge className="bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400 border-gray-200 dark:border-gray-800 border">
        <XCircle size={12} className="mr-1" />
        Inactive
      </Badge>
    );
  };

  // Handle confirm dialog actions
  const handleConfirmAction = () => {
    if (!selectedAdmin || !confirmAction) return;

    switch (confirmAction.type) {
      case 'toggle_status':
        handleToggleStatus(selectedAdmin);
        break;
      case 'delete':
        handleDeleteAdmin(selectedAdmin);
        break;
      default:
        break;
    }
  };

  // Initialize component
  useEffect(() => {
    fetchAvailableRoles();
    fetchAdmins();
  }, []);

  // Re-fetch when filters change
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      fetchAdmins(currentPage);
    }, 300); // Debounce search

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, roleFilter, statusFilter, currentPage]);

  // Loading and Error states with AdminLayout
  if (loading && admins.length === 0) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading admin management...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <AlertCircle className="mx-auto h-12 w-12 text-destructive mb-4" />
            <h2 className="text-lg font-semibold text-foreground mb-2">Error Loading Admin Management</h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => fetchAdmins()} variant="outline">Try Again</Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex-1 space-y-4 p-4 md:p-6 lg:p-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-foreground">Team Management</h1>
            <p className="text-muted-foreground mt-1">Manage admin users and their permissions</p>
          </div>
          <Button 
            onClick={() => setCreateDialogOpen(true)}
            className="flex items-center gap-2"
          >
            <UserPlus size={16} />
            Create Admin
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardContent className="p-4 md:p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 md:p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Users className="text-blue-600 dark:text-blue-400" size={20} />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Admins</p>
                  <p className="text-2xl font-bold text-foreground">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardContent className="p-4 md:p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 md:p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <CheckCircle className="text-green-600 dark:text-green-400" size={20} />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Active</p>
                  <p className="text-2xl font-bold text-foreground">{stats.active}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardContent className="p-4 md:p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 md:p-3 bg-gray-100 dark:bg-gray-900/20 rounded-lg">
                  <XCircle className="text-gray-600 dark:text-gray-400" size={20} />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Inactive</p>
                  <p className="text-2xl font-bold text-foreground">{stats.inactive}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardContent className="p-4 md:p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 md:p-3 bg-red-100 dark:bg-red-900/20 rounded-lg">
                  <Shield className="text-red-600 dark:text-red-400" size={20} />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Super Admins</p>
                  <p className="text-2xl font-bold text-foreground">{stats.byRole.super_admin || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="p-4 md:p-6">
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="relative flex-1 w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={16} />
                <Input
                  placeholder="Search admins by name or email..."
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-10 bg-background/50"
                />
              </div>

              <div className="flex gap-2 w-full sm:w-auto">
                <Select value={roleFilter} onValueChange={(value) => handleFilterChange('role', value)}>
                  <SelectTrigger className="w-full sm:w-40">
                    <SelectValue placeholder="Filter by role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Roles</SelectItem>
                    {Object.entries(availableRoles).map(([key, role]) => (
                      <SelectItem key={key} value={key}>{role.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={(value) => handleFilterChange('status', value)}>
                  <SelectTrigger className="w-full sm:w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Admins Table */}
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardHeader>
            <CardTitle className="text-foreground">Admin Users</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : admins.length === 0 ? (
              <div className="text-center py-8 px-6">
                <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold text-foreground mb-2">No admins found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || roleFilter !== 'all' || statusFilter !== 'all' 
                    ? 'No admins match your current filters.'
                    : 'Get started by creating your first admin user.'
                  }
                </p>
                {!(searchTerm || roleFilter !== 'all' || statusFilter !== 'all') && (
                  <Button onClick={() => setCreateDialogOpen(true)}>
                    <UserPlus size={16} className="mr-2" />
                    Create First Admin
                  </Button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-border/50">
                      <TableHead className="text-muted-foreground">Admin Details</TableHead>
                      <TableHead className="text-muted-foreground">Role</TableHead>
                      <TableHead className="text-muted-foreground">Status</TableHead>
                      <TableHead className="text-muted-foreground">Last Login</TableHead>
                      <TableHead className="text-muted-foreground">Created</TableHead>
                      <TableHead className="text-muted-foreground text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {admins.map((admin) => (
                      <TableRow key={admin._id} className="border-border/50">
                        <TableCell>
                          <div className="space-y-1">
                            <p className="font-medium text-foreground">{admin.name}</p>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <Mail size={12} />
                              {admin.email}
                            </div>
                            {admin.mustChangePassword && (
                              <Badge className="bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-400 border-orange-200 dark:border-orange-800 text-xs">
                                Must change password
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <RoleBadge 
                            roleType={admin.roleType} 
                            roleInfo={admin.roleInfo}
                            permissionMode={admin.permissionMode}
                            customPermissions={admin.customPermissions}
                          />
                        </TableCell>
                        
                        <TableCell>
                          <StatusBadge isActive={admin.isActive} />
                        </TableCell>
                        
                        <TableCell>
                          <div className="text-sm">
                            {admin.lastLogin ? (
                              <div className="flex items-center gap-1 text-muted-foreground">
                                <Clock size={12} />
                                {new Date(admin.lastLogin).toLocaleDateString()}
                              </div>
                            ) : (
                              <span className="text-muted-foreground/60">Never</span>
                            )}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <Calendar size={12} />
                            {new Date(admin.createdAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreHorizontal size={16} />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              
                              <DropdownMenuItem onClick={() => openEditDialog(admin)}>
                                <Edit size={14} className="mr-2" />
                                Edit Role
                              </DropdownMenuItem>
                              
                              <DropdownMenuItem 
                                onClick={() => openConfirmDialog(admin, {
                                  type: 'toggle_status',
                                  title: `${admin.isActive ? 'Deactivate' : 'Activate'} Admin`,
                                  description: `Are you sure you want to ${admin.isActive ? 'deactivate' : 'activate'} ${admin.name}?`
                                })}
                              >
                                <Power size={14} className="mr-2" />
                                {admin.isActive ? 'Deactivate' : 'Activate'}
                              </DropdownMenuItem>
                              
                              <DropdownMenuSeparator />
                              
                              <DropdownMenuItem 
                                className="text-red-600 focus:text-red-600"
                                onClick={() => openConfirmDialog(admin, {
                                  type: 'delete',
                                  title: 'Delete Admin',
                                  description: `Are you sure you want to delete ${admin.name}? This action cannot be undone.`
                                })}
                              >
                                <Trash2 size={14} className="mr-2" />
                                Delete Admin
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-between items-center mt-4 p-4 border-t border-border/50">
                <p className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </p>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Dialogs */}
        <CreateAdminDialog
          open={createDialogOpen}
          onOpenChange={setCreateDialogOpen}
          onSubmit={handleCreateAdmin}
          availableRoles={availableRoles}
        />

        <EditAdminDialog
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          admin={selectedAdmin}
          onSubmit={handleEditAdmin}
          availableRoles={availableRoles}
        />

        <ConfirmDialog
          open={confirmDialogOpen}
          onOpenChange={setConfirmDialogOpen}
          title={confirmAction?.title}
          description={confirmAction?.description}
          onConfirm={handleConfirmAction}
          destructive={confirmAction?.type === 'delete'}
          confirmText={confirmAction?.type === 'delete' ? 'Delete' : 'Confirm'}
        />
      </div>
    </AdminLayout>
  );
}