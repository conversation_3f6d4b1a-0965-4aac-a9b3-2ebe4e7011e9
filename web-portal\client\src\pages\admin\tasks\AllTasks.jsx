import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import AdminLayout from "../../../components/admin/AdminLayout";
import { useAuthStore } from "../../../store/authStore";
import { useAdminTaskStore } from "../../../store/adminTaskStore";
import { hasAdminAccess } from "../../../utils/adminUtils";
import { Card, CardContent } from "../../../components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../../components/ui/table";
import { Badge } from "../../../components/ui/badge";
import {
  HiEye,
  HiPencil,
  HiTrash,
  HiSearch,
  HiX,
  HiCheckCircle,
} from "react-icons/hi";
import ConfirmationModal from "../../../components/ui/ConfirmationModal";
import { toast } from "sonner";

export default function AllTasks() {
  const navigate = useNavigate();
  const { user, isInitializing, accessToken } = useAuthStore();
  const {
    stats,
    tasks,
    loading,
    fetchStats,
    fetchAllTasks,
    filters,
    setFilters,
    pagination,
    setPage,
    deleteTask,
    submitLoading,
    selectedTaskIds,
    toggleTaskSelection,
    selectAllTasks,
    clearSelection,
    bulkArchiveTasks,
    bulkReassignTasks,
    bulkDeleteTasks,
    bulkRestoreTasks,
    talents,
    fetchTalents,
    adminCompleteTask,
  } = useAdminTaskStore();
  const [selectedProject, setSelectedProject] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedEmployeeId, setSelectedEmployeeId] = useState("all");
  const [deleteModalState, setDeleteModalState] = useState({
    isOpen: false,
    taskId: null,
    taskTitle: "",
  });
  const [bulkArchiveModalOpen, setBulkArchiveModalOpen] = useState(false);
  const [bulkDeleteModalOpen, setBulkDeleteModalOpen] = useState(false);
  const [bulkRestoreModalOpen, setBulkRestoreModalOpen] = useState(false);
  const [showBulkReassignModal, setShowBulkReassignModal] = useState(false);
  const [selectedTalentForBulk, setSelectedTalentForBulk] = useState("");
  const [showArchived, setShowArchived] = useState(false);

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;
    if (!user || !hasAdminAccess(user)) {
      navigate("/talent/dashboard");
    }
  }, [user, isInitializing, navigate]);

  // Reset filters on mount to prevent inheriting from other pages
  useEffect(() => {
    // Clear all filters including any persistent ones from other pages
    setFilters({
      status: null,
      priority: null,
      complexity: null,
      category: "all",
      assignedTo: null,
      overdue: false,
      sortBy: "createdAt",
      sortOrder: "desc",
    });
  }, [setFilters]);

  // Fetch talents for employee dropdown
  useEffect(() => {
    if (hasAdminAccess(user) && accessToken) {
      fetchTalents(accessToken);
    }
  }, [user, accessToken]);

  // Fetch stats
  useEffect(() => {
    if (hasAdminAccess(user) && accessToken) {
      fetchStats(selectedProject);
    }
  }, [user, selectedProject, accessToken]);

  // Fetch tasks - don't depend on filters.status to avoid race condition
  useEffect(() => {
    if (hasAdminAccess(user) && accessToken) {
      const updatedFilters = {
        project: selectedProject === "all" ? "all" : selectedProject,
        category: filters.category || "all",
        assignedTo:
          selectedEmployeeId === "all" ? undefined : selectedEmployeeId,
        isArchived: showArchived,
        status: filters.status || null,
        priority: filters.priority || null,
        complexity: filters.complexity || null,
        sortBy: "createdAt",
        sortOrder: "desc",
      };
      fetchAllTasks(updatedFilters);
    }
  }, [
    user,
    selectedProject,
    selectedEmployeeId,
    accessToken,
    pagination.page,
    showArchived,
    filters.category,
    filters.status,
    filters.priority,
    filters.complexity,
    fetchAllTasks,
  ]);

  // Filter tasks based on search query (backend already filters by isArchived)
  const filteredTasks = tasks.filter((task) => {
    // Filter by search query
    if (!searchQuery.trim()) return true;
    const query = searchQuery.toLowerCase();

    // Search in title
    if (task.title.toLowerCase().includes(query)) return true;

    // Search in tags
    if (task.tags && Array.isArray(task.tags)) {
      return task.tags.some((tag) => tag.toLowerCase().includes(query));
    }

    return false;
  });

  // Handler functions
  const handleDeleteTask = async (taskId) => {
    try {
      // If viewing archived tasks, permanently delete. Otherwise, archive (soft delete)
      const permanent = showArchived;
      await deleteTask(taskId, permanent);
      toast.success(permanent ? "Task permanently deleted!" : "Task archived!");
      setDeleteModalState({ isOpen: false, taskId: null, taskTitle: "" });
    } catch (error) {
      console.error("Delete task error:", error);
      toast.error(`Failed to ${showArchived ? "delete" : "archive"} task`);
    }
  };

  const handleBulkArchive = async () => {
    try {
      await bulkArchiveTasks(selectedTaskIds);
      toast.success(`${selectedTaskIds.length} task(s) archived!`);
      setBulkArchiveModalOpen(false);
    } catch (error) {
      console.error("Bulk archive error:", error);
      toast.error("Failed to archive tasks");
    }
  };

  const handleBulkDelete = async () => {
    try {
      await bulkDeleteTasks(selectedTaskIds);
      toast.success(`${selectedTaskIds.length} task(s) permanently deleted!`);
      setBulkDeleteModalOpen(false);
    } catch (error) {
      console.error("Bulk delete error:", error);
      toast.error("Failed to delete tasks");
    }
  };

  const handleBulkRestore = async () => {
    try {
      await bulkRestoreTasks(selectedTaskIds);
      toast.success(`${selectedTaskIds.length} task(s) restored!`);
      setBulkRestoreModalOpen(false);
    } catch (error) {
      console.error("Bulk restore error:", error);
      toast.error("Failed to restore tasks");
    }
  };

  const handleBulkReassign = async () => {
    if (!selectedTalentForBulk) {
      toast.error("Please select a talent to reassign tasks to");
      return;
    }
    try {
      await bulkReassignTasks(selectedTaskIds, selectedTalentForBulk);
      toast.success(`${selectedTaskIds.length} task(s) reassigned!`);
      setShowBulkReassignModal(false);
      setSelectedTalentForBulk("");
    } catch (error) {
      console.error("Bulk reassign error:", error);
      toast.error("Failed to reassign tasks");
    }
  };

  const handleSelectAll = () => {
    if (selectedTaskIds.length === filteredTasks.length) {
      clearSelection();
    } else {
      selectAllTasks(filteredTasks.map((t) => t._id));
    }
  };

  // Helper functions
  const getStatusColor = (status) => {
    const colors = {
      available: "text-blue-400",
      requested: "text-yellow-400",
      "in-progress": "text-purple-400",
      "in-review": "text-orange-400",
      completed: "text-green-400",
      "needs-revision": "text-red-400",
      cancelled: "text-gray-500",
      "pending-approval": "text-amber-400",
      "not-started": "text-slate-400",
    };
    return colors[status] || "text-slate-300";
  };

  const getPriorityColor = (priority) => {
    const colors = {
      low: "text-gray-400",
      medium: "text-blue-400",
      high: "text-orange-400",
      urgent: "text-red-400",
    };
    return colors[priority] || "text-slate-300";
  };

  const formatDate = (dateString) => {
    if (!dateString) return { formatted: "No due date", isOverdue: false };
    const date = new Date(dateString);
    const now = new Date();
    const isOverdue = date < now;
    const formatted = date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: date.getFullYear() !== now.getFullYear() ? "numeric" : undefined,
    });
    return { formatted, isOverdue };
  };

  return (
    <AdminLayout>
      <div className="flex-1 p-8">
        {/* Header with Horizontal Tabs */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-3xl font-bold text-foreground">All Tasks</h2>
              <p className="text-muted-foreground mt-1">
                View and manage all tasks across projects
              </p>
            </div>
            <div className="flex items-center gap-3">
              <select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="px-4 py-2 rounded-lg border border-border bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="all">All Projects</option>
                <option value="Talent Portal">Talent Portal</option>
                <option value="ModelSuite">ModelSuite</option>
              </select>
            </div>
          </div>

          {/* Horizontal Navigation Tabs */}
          <div className="flex gap-2 border-b border-border mt-4">
            <button
              onClick={() => navigate("/admin/tasks")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Dashboard
            </button>
            <button
              onClick={() => navigate("/admin/tasks/all")}
              className="px-4 py-3 border-b-2 border-primary text-primary font-semibold transition-colors"
            >
              Active Tasks
            </button>
            <button
              onClick={() => navigate("/admin/tasks/stored")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Store
            </button>
            <button
              onClick={() => navigate("/admin/tasks/completed")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Completed
            </button>
            <button
              onClick={() => navigate("/admin/tasks/requests")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Requests
            </button>
            <button
              onClick={() => navigate("/admin/tasks/create")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Create Task
            </button>
          </div>
        </div>

        {/* Filters Bar */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-3 items-center">
              {/* Search */}
              <div className="flex-1 min-w-[240px]">
                <div className="relative">
                  <HiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search by title or tags..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 rounded-lg border border-border bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
              </div>

              {/* Employee Filter */}
              <select
                value={selectedEmployeeId}
                onChange={(e) => setSelectedEmployeeId(e.target.value)}
                className="px-3 py-2 rounded-lg border border-border bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary min-w-[200px]"
              >
                <option value="all">All Employees</option>
                {talents.map((talent) => (
                  <option key={talent._id} value={talent._id}>
                    {talent.name} ({talent.talentId})
                  </option>
                ))}
              </select>

              {/* Status Filter */}
              <select
                value={filters.status || "all"}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    status: e.target.value === "all" ? null : e.target.value,
                  })
                }
                className="px-3 py-2 rounded-lg border border-border bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="all">All Status</option>
                <option value="available">Available</option>
                <option value="requested">Requested</option>
                <option value="in-progress">In Progress</option>
                <option value="in-review">In Review</option>
                <option value="completed">Completed</option>
                <option value="needs-revision">Needs Revision</option>
              </select>

              {/* Category Filter */}
              <select
                value={filters.category || "all"}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    category: e.target.value === "all" ? null : e.target.value,
                  })
                }
                className="px-3 py-2 rounded-lg border border-border bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="all">All Categories</option>
                <option value="Currently Working on">
                  Currently Working on
                </option>
                <option value="Finished">Finished</option>
                <option value="Future">Future</option>
                <option value="Merged">Merged</option>
                <option value="Needs Fixing">Needs Fixing</option>
                <option value="Open">Open</option>
                <option value="Redo">Redo</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Tasks Table */}
        <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
          <CardContent className="p-6">
            {loading ? (
              <div className="flex items-center justify-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              </div>
            ) : (
              <>
                {/* Bulk Actions Bar */}
                {selectedTaskIds.length > 0 && (
                  <div className="mb-4 p-3 rounded-lg bg-slate-800/50 border border-slate-700">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-sm font-medium text-white">
                          {selectedTaskIds.length} task(s) selected
                        </span>
                        <button
                          onClick={clearSelection}
                          className="text-xs text-slate-400 hover:text-white transition-colors"
                        >
                          Clear
                        </button>
                      </div>
                      <div className="flex gap-2">
                        {showArchived ? (
                          // Archived tab actions: Restore and Permanently Delete
                          <>
                            <button
                              onClick={() => {
                                if (selectedTaskIds.length === 0) {
                                  toast.error("No tasks selected");
                                  return;
                                }
                                setBulkRestoreModalOpen(true);
                              }}
                              disabled={submitLoading}
                              className="px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              Restore Selected
                            </button>
                            <button
                              onClick={() => {
                                if (selectedTaskIds.length === 0) {
                                  toast.error("No tasks selected");
                                  return;
                                }
                                setBulkDeleteModalOpen(true);
                              }}
                              disabled={submitLoading}
                              className="px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              Permanently Delete
                            </button>
                          </>
                        ) : (
                          // Active tasks tab actions: Archive and Reassign
                          <>
                            <button
                              onClick={() => {
                                if (selectedTaskIds.length === 0) {
                                  toast.error("No tasks selected");
                                  return;
                                }
                                setBulkArchiveModalOpen(true);
                              }}
                              disabled={submitLoading}
                              className="px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              Archive Selected
                            </button>
                            <button
                              onClick={async () => {
                                if (talents.length === 0) {
                                  await fetchTalents(accessToken);
                                }
                                setShowBulkReassignModal(true);
                              }}
                              disabled={submitLoading}
                              className="px-3 py-1.5 bg-slate-700 hover:bg-slate-600 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              Reassign Selected
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-foreground">
                    All Tasks
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Showing {filteredTasks.length} of {tasks.length} tasks
                    {searchQuery && ` (filtered from ${tasks.length} tasks)`}
                  </p>
                </div>

                <div className="rounded-lg border border-border overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-muted/50 border-border/40">
                        <TableHead className="font-semibold w-12">
                          <input
                            type="checkbox"
                            checked={
                              filteredTasks.length > 0 &&
                              selectedTaskIds.length === filteredTasks.length
                            }
                            onChange={handleSelectAll}
                            className="w-4 h-4 rounded border-2 border-slate-600 bg-slate-800 checked:bg-rose-600 checked:border-rose-600 text-rose-600 focus:ring-2 focus:ring-rose-500 focus:ring-offset-0 cursor-pointer accent-rose-600"
                          />
                        </TableHead>
                        <TableHead className="font-semibold w-12">#</TableHead>
                        <TableHead className="font-semibold">Title</TableHead>
                        <TableHead className="font-semibold">Tags</TableHead>
                        <TableHead className="font-semibold">Project</TableHead>
                        <TableHead className="font-semibold">
                          Progress
                        </TableHead>
                        <TableHead className="font-semibold">
                          Priority
                        </TableHead>
                        <TableHead className="font-semibold">
                          Assigned To
                        </TableHead>
                        <TableHead className="font-semibold">
                          Due Date
                        </TableHead>
                        <TableHead className="font-semibold text-right">
                          Actions
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredTasks.map((task, index) => {
                        const dueDate = formatDate(task.dueDate);
                        return (
                          <TableRow
                            key={task._id}
                            className="hover:bg-muted/50 border-border/40"
                          >
                            <TableCell
                              className="py-3"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <input
                                type="checkbox"
                                checked={selectedTaskIds.includes(task._id)}
                                onChange={(e) => {
                                  e.stopPropagation();
                                  toggleTaskSelection(task._id);
                                }}
                                className="w-4 h-4 rounded border-2 border-slate-600 bg-slate-800 checked:bg-rose-600 checked:border-rose-600 text-rose-600 focus:ring-2 focus:ring-rose-500 focus:ring-offset-0 cursor-pointer accent-rose-600"
                              />
                            </TableCell>
                            <TableCell
                              className="py-3 w-12 cursor-pointer"
                              onClick={() =>
                                navigate(`/admin/tasks/${task._id}`)
                              }
                            >
                              <span className="text-sm text-foreground font-semibold">
                                {(pagination.page - 1) * pagination.limit +
                                  index +
                                  1}
                              </span>
                            </TableCell>
                            <TableCell
                              className="py-3 font-medium cursor-pointer"
                              onClick={() =>
                                navigate(`/admin/tasks/${task._id}`)
                              }
                            >
                              <div className="flex flex-col gap-0.5">
                                <span className="text-sm">{task.title}</span>
                                <span className="text-xs text-muted-foreground">
                                  {task.taskType === "section-based"
                                    ? "📋 Sections"
                                    : "✓ Checklist"}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell
                              className="py-3 cursor-pointer"
                              onClick={() =>
                                navigate(`/admin/tasks/${task._id}`)
                              }
                            >
                              {task.tags && task.tags.length > 0 ? (
                                <div className="flex flex-wrap gap-1 max-w-[200px]">
                                  {task.tags.slice(0, 3).map((tag, idx) => (
                                    <Badge
                                      key={idx}
                                      variant="secondary"
                                      className="text-xs px-2 py-0.5"
                                    >
                                      {tag}
                                    </Badge>
                                  ))}
                                  {task.tags.length > 3 && (
                                    <Badge
                                      variant="secondary"
                                      className="text-xs px-2 py-0.5"
                                    >
                                      +{task.tags.length - 3}
                                    </Badge>
                                  )}
                                </div>
                              ) : (
                                <span className="text-xs text-muted-foreground">
                                  —
                                </span>
                              )}
                            </TableCell>
                            <TableCell
                              className="py-3 cursor-pointer"
                              onClick={() =>
                                navigate(`/admin/tasks/${task._id}`)
                              }
                            >
                              <span className="text-sm font-medium text-foreground">
                                {task.project}
                              </span>
                            </TableCell>
                            <TableCell
                              className="py-3 cursor-pointer"
                              onClick={() =>
                                navigate(`/admin/tasks/${task._id}`)
                              }
                            >
                              {task.status === "in-progress" ? (
                                <div className="flex items-center gap-2">
                                  <div className="flex-1 h-2 bg-slate-700/50 rounded-full overflow-hidden">
                                    <div
                                      className="h-full bg-blue-500 transition-all duration-300"
                                      style={{
                                        width: `${task.progress || 0}%`,
                                      }}
                                    />
                                  </div>
                                  <span className="text-sm font-medium text-muted-foreground min-w-[40px]">
                                    {task.progress || 0}%
                                  </span>
                                </div>
                              ) : (
                                <span
                                  className={`text-sm font-medium ${getStatusColor(
                                    task.status
                                  )}`}
                                >
                                  {task.status}
                                </span>
                              )}
                            </TableCell>
                            <TableCell
                              className="py-3 cursor-pointer"
                              onClick={() =>
                                navigate(`/admin/tasks/${task._id}`)
                              }
                            >
                              <span
                                className={`text-sm font-medium ${getPriorityColor(
                                  task.priority
                                )}`}
                              >
                                {task.priority}
                              </span>
                            </TableCell>
                            <TableCell
                              className="py-3 cursor-pointer"
                              onClick={() =>
                                navigate(`/admin/tasks/${task._id}`)
                              }
                            >
                              {task.assignedTo ? (
                                <div className="flex items-center gap-2">
                                  <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center text-xs font-semibold">
                                    {task.assignedTo.name
                                      ?.charAt(0)
                                      .toUpperCase()}
                                  </div>
                                  <span className="text-sm">
                                    {task.assignedTo.name}
                                  </span>
                                </div>
                              ) : (
                                <span className="text-sm text-muted-foreground">
                                  Unassigned
                                </span>
                              )}
                            </TableCell>
                            <TableCell
                              className="py-3 cursor-pointer"
                              onClick={() =>
                                navigate(`/admin/tasks/${task._id}`)
                              }
                            >
                              <span
                                className={
                                  dueDate.isOverdue
                                    ? "text-sm text-red-600 dark:text-red-400 font-semibold"
                                    : "text-sm text-foreground"
                                }
                              >
                                {dueDate.formatted}
                                {dueDate.isOverdue && " ⚠️"}
                              </span>
                            </TableCell>
                            <TableCell className="py-3">
                              <div className="flex items-center justify-end gap-1">
                                {task.status === "pending-review" && (
                                  <button
                                    onClick={async (e) => {
                                      e.stopPropagation();
                                      if (
                                        window.confirm(
                                          `Mark "${task.title}" as completed?`
                                        )
                                      ) {
                                        try {
                                          await adminCompleteTask(task._id);
                                          toast.success(
                                            "Task marked as completed"
                                          );
                                          // Refresh the task list
                                          fetchAllTasks({
                                            status: null,
                                            isArchived: showArchived
                                              ? "true"
                                              : "false",
                                            category:
                                              selectedProject === "all"
                                                ? "all"
                                                : selectedProject,
                                            priority: filters.priority || "all",
                                            complexity:
                                              filters.complexity || "all",
                                            assignedTo:
                                              selectedEmployeeId === "all"
                                                ? ""
                                                : selectedEmployeeId,
                                            overdue: filters.overdue || false,
                                            sortBy:
                                              filters.sortBy || "createdAt",
                                            sortOrder:
                                              filters.sortOrder || "desc",
                                          });
                                        } catch (error) {
                                          toast.error(
                                            "Failed to complete task"
                                          );
                                        }
                                      }
                                    }}
                                    className="p-2 hover:bg-green-600/20 rounded-lg transition-all hover:scale-110 active:scale-95"
                                    title="Mark as Completed"
                                  >
                                    <HiCheckCircle className="w-5 h-5 text-green-500" />
                                  </button>
                                )}
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    navigate(`/admin/tasks/${task._id}`);
                                  }}
                                  className="p-2 hover:bg-blue-600/20 rounded-lg transition-all hover:scale-110 active:scale-95"
                                  title="View Details"
                                >
                                  <HiEye className="w-5 h-5 text-blue-500" />
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    navigate(`/admin/tasks/edit/${task._id}`);
                                  }}
                                  className="p-2 hover:bg-amber-600/20 rounded-lg transition-all hover:scale-110 active:scale-95"
                                  title="Edit Task"
                                >
                                  <HiPencil className="w-5 h-5 text-amber-500" />
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setDeleteModalState({
                                      isOpen: true,
                                      taskId: task._id,
                                      taskTitle: task.title,
                                    });
                                  }}
                                  className="p-2 hover:bg-red-600/20 rounded-lg transition-all hover:scale-110 active:scale-95"
                                  title="Delete Task"
                                >
                                  <HiTrash className="w-5 h-5 text-red-500" />
                                </button>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {pagination.pages > 1 && (
                  <div className="flex items-center justify-between mt-4">
                    <p className="text-sm text-muted-foreground">
                      Page {pagination.page} of {pagination.pages} (
                      {pagination.total} tasks)
                    </p>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setPage(pagination.page - 1)}
                        disabled={pagination.page === 1}
                        className="px-3 py-1.5 text-sm rounded-lg border border-border bg-card text-foreground hover:bg-muted transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>
                      <button
                        onClick={() => setPage(pagination.page + 1)}
                        disabled={pagination.page === pagination.pages}
                        className="px-3 py-1.5 text-sm rounded-lg border border-border bg-card text-foreground hover:bg-muted transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModalState.isOpen}
        onClose={() =>
          setDeleteModalState({ isOpen: false, taskId: null, taskTitle: "" })
        }
        onConfirm={() => handleDeleteTask(deleteModalState.taskId)}
        title={showArchived ? "Permanently Delete Task" : "Archive Task"}
        message={
          showArchived
            ? `Are you sure you want to PERMANENTLY delete "${deleteModalState.taskTitle}"? This action CANNOT be undone!`
            : `Are you sure you want to archive "${deleteModalState.taskTitle}"? You can view it later in the Archived tab.`
        }
        confirmText={showArchived ? "Permanently Delete" : "Archive"}
        cancelText="Cancel"
        variant="danger"
        loading={submitLoading}
      />

      {/* Bulk Archive Confirmation Modal */}
      <ConfirmationModal
        isOpen={bulkArchiveModalOpen}
        onClose={() => setBulkArchiveModalOpen(false)}
        onConfirm={handleBulkArchive}
        title="Archive Tasks"
        message={`Are you sure you want to archive ${selectedTaskIds.length} selected task(s)? They can be restored later.`}
        confirmText="Archive"
        cancelText="Cancel"
        variant="danger"
        loading={submitLoading}
      />

      {/* Bulk Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={bulkDeleteModalOpen}
        onClose={() => setBulkDeleteModalOpen(false)}
        onConfirm={handleBulkDelete}
        title="Permanently Delete Tasks"
        message={`Are you sure you want to PERMANENTLY delete ${selectedTaskIds.length} selected task(s)? This action CANNOT be undone!`}
        confirmText="Permanently Delete"
        cancelText="Cancel"
        variant="danger"
        loading={submitLoading}
      />

      {/* Bulk Restore Confirmation Modal */}
      <ConfirmationModal
        isOpen={bulkRestoreModalOpen}
        onClose={() => setBulkRestoreModalOpen(false)}
        onConfirm={handleBulkRestore}
        title="Restore Tasks"
        message={`Are you sure you want to restore ${selectedTaskIds.length} selected task(s)? They will be moved back to Active Tasks.`}
        confirmText="Restore"
        cancelText="Cancel"
        variant="info"
        loading={submitLoading}
      />

      {/* Bulk Reassign Modal */}
      {showBulkReassignModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div
            className="fixed inset-0 bg-black/80 backdrop-blur-md"
            onClick={() => setShowBulkReassignModal(false)}
          />
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="relative w-full max-w-md transform overflow-hidden rounded-2xl bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 border border-slate-700 shadow-2xl">
              <button
                onClick={() => setShowBulkReassignModal(false)}
                className="absolute right-4 top-4 p-1 rounded-lg hover:bg-slate-700/50 transition-colors"
              >
                <HiX className="w-5 h-5 text-slate-400" />
              </button>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4">
                  Reassign Tasks
                </h3>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Select Talent
                </label>
                <select
                  value={selectedTalentForBulk}
                  onChange={(e) => setSelectedTalentForBulk(e.target.value)}
                  className="w-full px-3 py-2 rounded-lg border border-slate-700 bg-slate-800 text-white focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500"
                >
                  <option value="">Select a talent...</option>
                  {talents.map((talent) => (
                    <option key={talent._id} value={talent._id}>
                      {talent.name} ({talent.talentId})
                    </option>
                  ))}
                </select>
                <div className="mt-6 flex gap-3">
                  <button
                    onClick={() => {
                      setShowBulkReassignModal(false);
                      setSelectedTalentForBulk("");
                    }}
                    className="flex-1 px-4 py-2.5 rounded-lg border border-slate-700 bg-slate-800 text-slate-300 hover:bg-slate-700 hover:text-white transition-colors text-sm font-medium"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleBulkReassign}
                    disabled={submitLoading || !selectedTalentForBulk}
                    className="flex-1 px-4 py-2.5 rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {submitLoading ? "Reassigning..." : "Reassign Tasks"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
