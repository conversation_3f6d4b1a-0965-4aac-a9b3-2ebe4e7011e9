import React, { useEffect, useRef, useState } from "react";
import { Hi<PERSON>, Hi<PERSON>heck, HiDownload } from "react-icons/hi";
import {
  Pencil,
  Square,
  Circle,
  Eraser,
  Undo2,
  Redo2,
  Trash2,
  Type,
  ArrowRight,
  Move,
} from "lucide-react";
import { toast } from "sonner";

const ImageAnnotationModal = ({
  isOpen,
  onClose,
  imageUrl,
  onSave,
  fileName = "image",
}) => {
  const canvasRef = useRef(null);
  const imageRef = useRef(null);
  const textInputRef = useRef(null);
  const [saving, setSaving] = useState(false);
  const [isDrawing, setIsDrawing] = useState(false);
  const [tool, setTool] = useState("draw");
  const [color, setColor] = useState("#ef4444");
  const [lineWidth, setLineWidth] = useState(3);
  const [fontSize, setFontSize] = useState(16);
  const [history, setHistory] = useState([]);
  const [historyStep, setHistoryStep] = useState(-1);
  const [startPos, setStartPos] = useState(null);
  const [textInput, setTextInput] = useState("");
  const [showTextInput, setShowTextInput] = useState(false);
  const [textPosition, setTextPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState(null);
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
  const [scale, setScale] = useState(1);

  // Load image on canvas
  useEffect(() => {
    if (!isOpen || !imageUrl || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    const img = new Image();
    img.crossOrigin = "anonymous";

    img.onload = () => {
      imageRef.current = img;

      // Set canvas size to fit container while maintaining aspect ratio
      const maxWidth = canvas.parentElement.clientWidth;
      const maxHeight = canvas.parentElement.clientHeight;
      const scale = Math.min(maxWidth / img.width, maxHeight / img.height, 1);

      canvas.width = img.width * scale;
      canvas.height = img.height * scale;

      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      saveToHistory();
    };

    img.src = imageUrl;

    // ESC key handler
    const handleEsc = (e) => {
      if (e.key === "Escape") {
        onClose();
      }
    };
    window.addEventListener("keydown", handleEsc);

    return () => {
      window.removeEventListener("keydown", handleEsc);
    };
  }, [isOpen, imageUrl, onClose]);

  const saveToHistory = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const newHistory = history.slice(0, historyStep + 1);
    newHistory.push(canvas.toDataURL());
    setHistory(newHistory);
    setHistoryStep(newHistory.length - 1);
  };

  const undo = () => {
    if (historyStep > 0) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext("2d");
      const img = new Image();
      img.onload = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0);
      };
      img.src = history[historyStep - 1];
      setHistoryStep(historyStep - 1);
    }
  };

  const redo = () => {
    if (historyStep < history.length - 1) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext("2d");
      const img = new Image();
      img.onload = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0);
      };
      img.src = history[historyStep + 1];
      setHistoryStep(historyStep + 1);
    }
  };

  const getMousePos = (e) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  };

  const handleCanvasClick = (e) => {
    if (tool === "text") {
      const pos = getMousePos(e);
      setTextPosition(pos);
      setShowTextInput(true);
      setTimeout(() => textInputRef.current?.focus(), 100);
    }
  };

  const addText = () => {
    if (!textInput.trim()) {
      setShowTextInput(false);
      return;
    }

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    ctx.font = `bold ${fontSize}px Arial`;
    ctx.fillStyle = color;
    ctx.strokeStyle = "#000000";
    ctx.lineWidth = 3;

    // Add stroke for better visibility
    ctx.strokeText(textInput, textPosition.x, textPosition.y);
    ctx.fillText(textInput, textPosition.x, textPosition.y);

    setTextInput("");
    setShowTextInput(false);
    saveToHistory();
  };

  const startDrawing = (e) => {
    if (tool === "text") return;
    if (tool === "pan") {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - canvasOffset.x,
        y: e.clientY - canvasOffset.y,
      });
      return;
    }

    const pos = getMousePos(e);
    setIsDrawing(true);
    setStartPos(pos);

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    ctx.strokeStyle = color;
    ctx.lineWidth = lineWidth;
    ctx.lineCap = "round";
    ctx.lineJoin = "round";

    if (tool === "draw") {
      ctx.beginPath();
      ctx.moveTo(pos.x, pos.y);
    }
  };

  const draw = (e) => {
    if (isDragging) {
      setCanvasOffset({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
      return;
    }

    if (!isDrawing) return;

    const pos = getMousePos(e);
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");

    if (tool === "draw") {
      ctx.lineTo(pos.x, pos.y);
      ctx.stroke();
    } else if (tool === "eraser") {
      ctx.save();
      ctx.globalCompositeOperation = "destination-out";
      ctx.beginPath();
      ctx.arc(pos.x, pos.y, lineWidth * 2, 0, Math.PI * 2);
      ctx.fill();
      ctx.restore();
    }
  };

  const stopDrawing = (e) => {
    if (isDragging) {
      setIsDragging(false);
      return;
    }

    if (!isDrawing) return;

    const pos = getMousePos(e);
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");

    if (tool === "rectangle") {
      const width = pos.x - startPos.x;
      const height = pos.y - startPos.y;
      ctx.strokeStyle = color;
      ctx.lineWidth = lineWidth;
      ctx.strokeRect(startPos.x, startPos.y, width, height);
    } else if (tool === "circle") {
      const radius = Math.sqrt(
        Math.pow(pos.x - startPos.x, 2) + Math.pow(pos.y - startPos.y, 2)
      );
      ctx.strokeStyle = color;
      ctx.lineWidth = lineWidth;
      ctx.beginPath();
      ctx.arc(startPos.x, startPos.y, radius, 0, 2 * Math.PI);
      ctx.stroke();
    } else if (tool === "arrow") {
      ctx.strokeStyle = color;
      ctx.fillStyle = color;
      ctx.lineWidth = lineWidth;

      // Draw line
      ctx.beginPath();
      ctx.moveTo(startPos.x, startPos.y);
      ctx.lineTo(pos.x, pos.y);
      ctx.stroke();

      // Draw arrowhead
      const angle = Math.atan2(pos.y - startPos.y, pos.x - startPos.x);
      const arrowLength = 15;
      ctx.beginPath();
      ctx.moveTo(pos.x, pos.y);
      ctx.lineTo(
        pos.x - arrowLength * Math.cos(angle - Math.PI / 6),
        pos.y - arrowLength * Math.sin(angle - Math.PI / 6)
      );
      ctx.lineTo(
        pos.x - arrowLength * Math.cos(angle + Math.PI / 6),
        pos.y - arrowLength * Math.sin(angle + Math.PI / 6)
      );
      ctx.closePath();
      ctx.fill();
    }

    setIsDrawing(false);
    setStartPos(null);
    saveToHistory();
  };

  const handleSave = async () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    setSaving(true);
    try {
      canvas.toBlob(async (blob) => {
        await onSave(blob);
        setSaving(false);
      }, "image/png");
    } catch (error) {
      console.error("Save error:", error);
      toast.error("Failed to save annotation");
      setSaving(false);
    }
  };

  const handleDownload = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const dataURL = canvas.toDataURL("image/png");
    const link = document.createElement("a");
    link.download = `${fileName}_annotated.png`;
    link.href = dataURL;
    link.click();
    toast.success("Image downloaded!");
  };

  const clearCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas || !imageRef.current) return;

    const ctx = canvas.getContext("2d");
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(imageRef.current, 0, 0, canvas.width, canvas.height);
    saveToHistory();
  };

  if (!isOpen) return null;

  const tools = [
    { id: "draw", icon: Pencil, label: "Draw" },
    { id: "rectangle", icon: Square, label: "Rectangle" },
    { id: "circle", icon: Circle, label: "Circle" },
    { id: "arrow", icon: ArrowRight, label: "Arrow" },
    { id: "text", icon: Type, label: "Text" },
    { id: "eraser", icon: Eraser, label: "Eraser" },
  ];

  const colors = [
    { value: "#ef4444", label: "Red" },
    { value: "#f97316", label: "Orange" },
    { value: "#eab308", label: "Yellow" },
    { value: "#22c55e", label: "Green" },
    { value: "#3b82f6", label: "Blue" },
    { value: "#a855f7", label: "Purple" },
    { value: "#ffffff", label: "White" },
    { value: "#000000", label: "Black" },
  ];

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
      onClick={onClose}
    >
      <div
        className="relative w-[90vw] h-[85vh] max-w-6xl bg-slate-900 rounded-lg shadow-2xl border border-slate-700 overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-3 bg-slate-800 border-b border-slate-700 flex-shrink-0">
          <div>
            <h2 className="text-lg font-bold text-white">Edit Image</h2>
            <p className="text-xs text-slate-400 mt-0.5">
              Mark issues and add comments
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={handleDownload}
              className="flex items-center gap-1.5 px-3 py-1.5 bg-slate-700 hover:bg-slate-600 text-white text-sm rounded-lg transition-colors"
              title="Download annotated image"
            >
              <HiDownload className="w-3.5 h-3.5" />
              <span>Download</span>
            </button>
            <button
              onClick={handleSave}
              disabled={saving}
              className="flex items-center gap-1.5 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <HiCheck className="w-4 h-4" />
              <span>{saving ? "Saving..." : "Save"}</span>
            </button>
            <button
              onClick={onClose}
              className="p-1.5 hover:bg-slate-700 rounded-lg transition-colors text-slate-400 hover:text-white"
              title="Close (ESC)"
            >
              <HiX className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex items-center gap-2 px-4 py-2 bg-slate-800/50 border-b border-slate-700/50 flex-shrink-0">
          {/* Tools */}
          <div className="flex items-center gap-1 border-r border-slate-700 pr-2">
            {tools.map((t) => (
              <button
                key={t.id}
                onClick={() => setTool(t.id)}
                className={`p-2 rounded transition-colors ${
                  tool === t.id
                    ? "bg-blue-600 text-white"
                    : "bg-slate-700 text-slate-300 hover:bg-slate-600"
                }`}
                title={t.label}
              >
                <t.icon className="w-4 h-4" />
              </button>
            ))}
          </div>

          {/* Colors */}
          <div className="flex items-center gap-1 border-r border-slate-700 pr-2">
            {colors.map((c) => (
              <button
                key={c.value}
                onClick={() => setColor(c.value)}
                className={`w-6 h-6 rounded border-2 transition-all ${
                  color === c.value
                    ? "border-white scale-110"
                    : "border-slate-600"
                }`}
                style={{ backgroundColor: c.value }}
                title={c.label}
              />
            ))}
          </div>

          {/* Line Width */}
          <div className="flex items-center gap-2 border-r border-slate-700 pr-2">
            <span className="text-xs text-slate-400">Line:</span>
            <input
              type="range"
              min="1"
              max="20"
              value={lineWidth}
              onChange={(e) => setLineWidth(Number(e.target.value))}
              className="w-20"
            />
            <span className="text-xs text-slate-300 w-6">{lineWidth}</span>
          </div>

          {/* Font Size */}
          <div className="flex items-center gap-2 border-r border-slate-700 pr-2">
            <span className="text-xs text-slate-400">Font:</span>
            <input
              type="range"
              min="12"
              max="48"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
              className="w-20"
            />
            <span className="text-xs text-slate-300 w-7">{fontSize}</span>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-1">
            <button
              onClick={undo}
              disabled={historyStep <= 0}
              className="p-2 bg-slate-700 text-slate-300 hover:bg-slate-600 rounded transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
              title="Undo (Ctrl+Z)"
            >
              <Undo2 className="w-4 h-4" />
            </button>
            <button
              onClick={redo}
              disabled={historyStep >= history.length - 1}
              className="p-2 bg-slate-700 text-slate-300 hover:bg-slate-600 rounded transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
              title="Redo (Ctrl+Y)"
            >
              <Redo2 className="w-4 h-4" />
            </button>
            <button
              onClick={clearCanvas}
              className="p-2 bg-red-600/20 text-red-400 hover:bg-red-600/30 rounded transition-colors"
              title="Clear all annotations"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Canvas Container */}
        <div className="flex-1 bg-slate-900 min-h-0 overflow-auto relative flex items-center justify-center">
          <canvas
            ref={canvasRef}
            onMouseDown={startDrawing}
            onMouseMove={draw}
            onMouseUp={stopDrawing}
            onMouseLeave={stopDrawing}
            onClick={handleCanvasClick}
            className="max-w-full max-h-full cursor-crosshair"
            style={{ imageRendering: "pixelated" }}
          />

          {/* Text Input Overlay */}
          {showTextInput && (
            <div
              className="absolute bg-slate-800 border border-slate-600 rounded-lg shadow-lg p-2"
              style={{
                left:
                  textPosition.x +
                  canvasRef.current?.getBoundingClientRect().left,
                top:
                  textPosition.y +
                  canvasRef.current?.getBoundingClientRect().top -
                  50,
              }}
            >
              <input
                ref={textInputRef}
                type="text"
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    addText();
                  } else if (e.key === "Escape") {
                    setShowTextInput(false);
                    setTextInput("");
                  }
                }}
                placeholder="Type text..."
                className="w-48 px-2 py-1 bg-slate-700 text-white text-sm border border-slate-600 rounded outline-none focus:border-blue-500"
                autoFocus
              />
              <div className="flex gap-1 mt-1">
                <button
                  onClick={addText}
                  className="flex-1 px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded"
                >
                  Add
                </button>
                <button
                  onClick={() => {
                    setShowTextInput(false);
                    setTextInput("");
                  }}
                  className="flex-1 px-2 py-1 bg-slate-600 hover:bg-slate-500 text-white text-xs rounded"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer Tips */}
        <div className="px-4 py-2 bg-slate-800 border-t border-slate-700 flex-shrink-0">
          <div className="flex items-center justify-between text-xs text-slate-400">
            <span>
              💡{" "}
              {tool === "text"
                ? "Click to add text"
                : "Click and drag to draw. Use tools to annotate screenshots."}
            </span>
            <span className="text-slate-500">Press ESC to close</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageAnnotationModal;
