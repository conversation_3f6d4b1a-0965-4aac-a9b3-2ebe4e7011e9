import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../store/authStore";
import { getUserRole } from "../../utils/rolePermissions";
import AdminLayout from "../../components/admin/AdminLayout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";
import { Badge } from "../../components/ui/badge";
import { HiTrendingUp, HiFire, HiStar } from "react-icons/hi";
import UserProfilePopup from "../../components/UserProfilePopup";

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";

export default function AdminLeaderboard() {
  const navigate = useNavigate();
  const { user, accessToken, isInitializing } = useAuthStore();
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showProfilePopup, setShowProfilePopup] = useState(false);
  const [selectedUserProfile, setSelectedUserProfile] = useState(null);
  const [profileLoading, setProfileLoading] = useState(false);

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;
    
    const userRole = getUserRole(user);
    if (!userRole) {
      navigate("/talent/dashboard");
    }
  }, [user, navigate, isInitializing]);

  // Fetch leaderboard data
  useEffect(() => {
    const userRole = getUserRole(user);
    if (userRole) {
      fetchLeaderboard();
    }
  }, [user]);

  async function fetchLeaderboard() {
    try {
      setLoading(true);
      const response = await fetch(`${API_URL}/api/getBatchRanking?page=1&limit=50`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to fetch leaderboard");
      }

      const data = await response.json();
      setLeaderboard(data.data || []);
    } catch (err) {
      console.error("Error fetching leaderboard:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }

  const handleProfileClick = async (talentId) => {
    try {
      setProfileLoading(true);
      const response = await fetch(`${API_URL}/api/user/profile/${talentId}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to fetch user profile");
      }

      const data = await response.json();
      setSelectedUserProfile(data.user);
      setShowProfilePopup(true);
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
    } finally {
      setProfileLoading(false);
    }
  };

  const handleCloseProfile = () => {
    setShowProfilePopup(false);
    setSelectedUserProfile(null);
  };

  // Show loading during initialization
  if (isInitializing) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  // Show error if user doesn't have admin access
  const userRole = getUserRole(user);
  if (!userRole) {
    return null;
  }

  return (
    <AdminLayout>
      <div className="min-h-screen p-6">
        <div className="max-w-7xl mx-auto space-y-5">
          {/* Header (with inline stats on the right) */}
          <div className="flex items-start justify-between">
            <div className="relative z-20">
              <h1 className="text-2xl font-bold tracking-tight" style={{
                color: '#F1F5F9',
                textShadow: '0 2px 8px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 0, 0, 0.6)'
              }}>
                Leaderboard
              </h1>
              <p className="text-sm mt-1" style={{
                color: '#CBD5E1',
                textShadow: '0 1px 4px rgba(0, 0, 0, 0.8), 0 0 12px rgba(0, 0, 0, 0.5)'
              }}>
                Track top performers and XP rankings across all talents
              </p>
            </div>

            {/* Inline stats: compact cards shown in a single non-wrapping row */}
            {!loading && leaderboard.length > 0 && (
              <div className="flex items-center gap-3 flex-nowrap overflow-x-auto">
                <Card className="relative z-20 flex-shrink-0 inline-flex items-center w-40 sm:w-44 border-border/40 bg-card/95 shadow-lg p-1">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-md bg-card/95 flex items-center justify-center">
                      <HiStar className="h-3 w-3 text-foreground/90" />
                    </div>
                    <div className="pl-1">
                      <div className="text-xs font-medium text-muted-foreground">Total Talents</div>
                      <div className="text-base font-semibold text-white">{leaderboard.length}</div>
                    </div>
                  </div>
                </Card>

                <Card className="relative z-20 flex-shrink-0 inline-flex items-center w-44 sm:w-48 border-border/40 bg-card/95 shadow-lg p-1">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-md bg-card/95 flex items-center justify-center">
                      <HiTrendingUp className="h-3 w-3 text-foreground/90" />
                    </div>
                    <div className="pl-1">
                      <div className="text-xs font-medium text-muted-foreground">Total XP Earned</div>
                      <div className="text-base font-semibold text-primary">
                        {leaderboard.reduce((sum, t) => sum + (t.xp || 0), 0).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </Card>

                <Card className="relative z-20 flex-shrink-0 inline-flex items-center w-40 sm:w-44 border-border/40 bg-card/95 shadow-lg p-1">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-md bg-card/95 flex items-center justify-center">
                      <HiFire className="h-3 w-3 text-foreground/90" />
                    </div>
                    <div className="pl-1">
                      <div className="text-xs font-medium text-muted-foreground">Active Streaks</div>
                      <div className="text-base font-semibold text-orange-500">
                        {leaderboard.filter(t => (t.streak || 0) > 0).length}
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}
          </div>

          {/* Main Leaderboard Card */}
          <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 p-1">
                Top Performers
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div
                      key={i}
                      className="flex items-center space-x-4 p-4 rounded-lg animate-pulse"
                      style={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                    >
                      <div className="w-8 h-8 rounded-full bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70"></div>
                      <div className="w-10 h-10 rounded-full bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 rounded w-1/4"></div>
                      </div>
                      <div className="h-4 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 rounded w-20"></div>
                    </div>
                  ))}
                </div>
              ) : error ? (
                <div className="text-red-400 text-center py-10">
                  Failed to load leaderboard: {error}
                </div>
              ) : leaderboard.length === 0 ? (
                <div className="text-muted-foreground text-center py-10">
                  No leaderboard data available.
                </div>
              ) : (
                <div className="rounded-md border border-border/40">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-border/40 hover:bg-transparent">
                        <TableHead className="w-16 text-center">Rank</TableHead>
                        <TableHead>Talent</TableHead>
                        <TableHead className="text-center">XP</TableHead>
                        <TableHead className="text-center">Streak</TableHead>
                        <TableHead className="text-center">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {leaderboard.map((talent, index) => (
                        <TableRow
                          key={talent._id || index}
                          className="border-border/40 hover:bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 transition-colors"
                        >
                          {/* Rank */}
                          <TableCell className="text-center font-medium">
                            <div className="flex items-center justify-center">
                              <span className="text-muted-foreground">#{talent.rank}</span>
                            </div>
                          </TableCell>

                          {/* Talent Name & Avatar */}
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <div
                                className="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center overflow-hidden text-sm font-semibold cursor-pointer transition-all duration-200 hover:ring-2 hover:ring-primary hover:scale-105"
                                style={{
                                  background: talent.profilePicture 
                                    ? 'transparent' 
                                    : 'rgba(255, 255, 255, 0.1)',
                                }}
                                onClick={() => handleProfileClick(talent._id)}
                                title="Click to view profile"
                              >
                                {talent.profilePicture ? (
                                  <img
                                    src={talent.profilePicture}
                                    alt={talent.name}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <span className="text-white">
                                    {talent.name?.charAt(0).toUpperCase()}
                                  </span>
                                )}
                              </div>
                              <div>
                                <div className="font-medium">{talent.name}</div>
                              </div>
                            </div>
                          </TableCell>

                          {/* XP */}
                          <TableCell className="text-center font-semibold text-primary">
                            {talent.xp?.toLocaleString() || 0}
                          </TableCell>

                          {/* Streak */}
                          <TableCell className="text-center">
                            <div className="flex items-center justify-center gap-1">
                              {(talent.streak || 0) > 0 && (
                                <HiFire className="h-4 w-4 text-muted-foreground" />
                              )}
                              <span className="text-foreground/80">
                                {talent.streak || 0}
                              </span>
                            </div>
                          </TableCell>

                          {/* Actions */}
                          <TableCell className="text-center">
                            <button
                              onClick={() => navigate(`/admin/leaderboard/${talent._id}`)}
                              className="relative z-20 inline-flex items-center gap-1.5 px-3 py-1 text-sm font-medium text-primary hover:text-primary/80 bg-primary/10 hover:bg-primary/20 rounded-md transition-colors"
                              style={{ boxShadow: '0 6px 18px rgba(0,0,0,0.45)', textShadow: '0 1px 6px rgba(0,0,0,0.6)' }}
                            >
                              View Details
                            </button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          
        </div>
      </div>

      {/* User Profile Popup */}
      <UserProfilePopup
        user={selectedUserProfile}
        isOpen={showProfilePopup}
        onClose={handleCloseProfile}
      />
    </AdminLayout>
  );
}
