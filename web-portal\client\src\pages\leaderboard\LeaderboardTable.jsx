import { useEffect, useState } from "react";
import { useLeaderboardStore } from "../../store/leaderboardStore";
import UserProfilePopup from "../../components/UserProfilePopup";
import SaturdayWinnersPopup from "../../components/SaturdayWinnersPopup";
import BadgeIcon from "../../components/badges/BadgeIcon";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../../components/ui/pagination";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../components/ui/tooltip";

export default function LeaderboardTable() {
  const {
    leaderboard,
    loading,
    error,
    fetchBatchRanking,
    selectedUserProfile,
    profileLoading,
    profileError,
    fetchUserProfile,
    clearSelectedProfile
  } = useLeaderboardStore();

  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [showProfilePopup, setShowProfilePopup] = useState(false);
  const [showWinnersPopup, setShowWinnersPopup] = useState(false);
  const [sortBy, setSortBy] = useState("xp");
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);

  // Fetch leaderboard with pagination + search (debounced)
  useEffect(() => {
    const delayDebounce = setTimeout(async () => {
      const result = await fetchBatchRanking({
        page,
        limit: 10,
        search: searchTerm,
        sortBy,
      });
      if (result?.pagination?.pages) setTotalPages(result.pagination.pages);
      setIsInitialLoad(false); // Mark initial load as complete
    }, 400);

    return () => clearTimeout(delayDebounce);
  }, [fetchBatchRanking, page, searchTerm, sortBy]);

  const handlePrev = () => setPage((p) => Math.max(1, p - 1));
  const handleNext = () => setPage((p) => Math.min(totalPages, p + 1));

  const handlePageChange = (newPage) => {
    setPage(newPage);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleProfileClick = async (talentId) => {
    try {
      await fetchUserProfile(talentId);
      setShowProfilePopup(true);
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
    }
  };

  const handleCloseProfile = () => {
    setShowProfilePopup(false);
    clearSelectedProfile();
  };

  const renderPaginationItems = () => {
    const items = [];
    const maxVisible = 5;

    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={page === i}
              style={{ cursor: 'pointer' }}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    } else {
      items.push(
        <PaginationItem key={1}>
          <PaginationLink
            onClick={() => handlePageChange(1)}
            isActive={page === 1}
            style={{ cursor: 'pointer' }}
          >
            1
          </PaginationLink>
        </PaginationItem>
      );

      if (page > 3) {
        items.push(<PaginationEllipsis key="ellipsis-start" />);
      }

      const start = Math.max(2, page - 1);
      const end = Math.min(totalPages - 1, page + 1);

      for (let i = start; i <= end; i++) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={page === i}
              style={{ cursor: 'pointer' }}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }

      if (page < totalPages - 2) {
        items.push(<PaginationEllipsis key="ellipsis-end" />);
      }

      items.push(
        <PaginationItem key={totalPages}>
          <PaginationLink
            onClick={() => handlePageChange(totalPages)}
            isActive={page === totalPages}
            style={{ cursor: 'pointer' }}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  return (
    <div className="w-full">
      <div className="relative w-full rounded-2xl overflow-hidden transition-all duration-500 border backdrop-blur-xl" style={{
        background: 'rgba(255, 255, 255, 0.03)',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.3)'
      }}>
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between px-3 sm:px-4 py-3 border-b gap-3 sm:gap-0" style={{ borderColor: 'rgba(255, 255, 255, 0.1)' }}>
          <h2 className="text-base sm:text-lg font-semibold" style={{ color: '#F8FAFC' }}>
            Global Leaderboard
          </h2>
          <div className="flex items-center space-x-3">
            {/* Filter Button */}
            <div className="relative">
              <button
                onClick={() => setShowFilterDropdown(!showFilterDropdown)}
                className="px-4 py-2 rounded-lg border transition-all duration-200 hover:bg-white/10 flex items-center gap-2.5 text-sm group"
                style={{
                  backgroundColor: 'rgba(26, 28, 35, 0.96)',
                  borderColor: 'rgba(255, 255, 255, 0.06)',
                  color: '#F1F5F9'
                }}
                title="Sort leaderboard"
              >
                <span className="font-medium" style={{ color: '#F1F5F9' }}>
                  {sortBy === "xp" ? "Ranked by XP" : "Ranked by Streak"}
                </span>
                <svg
                  className={`w-4 h-4 transition-transform duration-200 ${showFilterDropdown ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  style={{ color: '#94A3B8' }}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Dropdown Menu */}
              {showFilterDropdown && (
                <>
                  <div
                    className="fixed inset-0 z-40"
                    onClick={() => setShowFilterDropdown(false)}
                  />
                  <div
                    className="absolute top-full mt-2 right-0 w-56 rounded-xl border z-50 overflow-hidden"
                    style={{
                      background: 'rgba(26, 28, 35, 0.96)',
                      backdropFilter: 'blur(6px) saturate(120%)',
                      WebkitBackdropFilter: 'blur(6px) saturate(120%)',
                      borderColor: 'rgba(255, 255, 255, 0.06)',
                      boxShadow: '0 8px 32px rgba(0,0,0,0.6), inset 0 1px 1px rgba(255,255,255,0.02)'
                    }}
                  >
                    <div className="">
                      <button
                        onClick={() => {
                          setSortBy("xp");
                          setPage(1);
                          setShowFilterDropdown(false);
                        }}
                        className="w-full px-4 py-3 text-left text-sm transition-all duration-200 flex items-center gap-3"
                        style={{
                          backgroundColor: sortBy === "xp" ? 'rgba(255, 255, 255, 0.07)' : 'transparent',
                          borderBottom: '1px solid rgba(255, 255, 255, 0.06)'
                        }}
                        onMouseEnter={(e) => {
                          if (sortBy !== "xp") {
                            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.06)';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (sortBy !== "xp") {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                      >
                        <div className="w-5 h-5 flex items-center justify-center flex-shrink-0">
                          {sortBy === "xp" && (
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" style={{ color: '#F1F5F9' }}>
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className={`${sortBy === "xp" ? "font-semibold" : "font-medium"} truncate`} style={{ color: sortBy === "xp" ? '#F1F5F9' : '#CBD5E1' }}>
                            Ranked by XP
                          </div>
                          <div className="text-xs mt-0.5 truncate" style={{ color: '#94A3B8' }}>
                            Sort by experience points
                          </div>
                        </div>
                      </button>
                      <button
                        onClick={() => {
                          setSortBy("streak");
                          setPage(1);
                          setShowFilterDropdown(false);
                        }}
                        className="w-full px-4 py-3 text-left text-sm transition-all duration-200 flex items-center gap-3"
                        style={{
                          backgroundColor: sortBy === "streak" ? 'rgba(255, 255, 255, 0.07)' : 'transparent'
                        }}
                        onMouseEnter={(e) => {
                          if (sortBy !== "streak") {
                            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.06)';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (sortBy !== "streak") {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                      >
                        <div className="w-5 h-5 flex items-center justify-center flex-shrink-0">
                          {sortBy === "streak" && (
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" style={{ color: '#F1F5F9' }}>
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className={`${sortBy === "streak" ? "font-semibold" : "font-medium"} truncate`} style={{ color: sortBy === "streak" ? '#F1F5F9' : '#CBD5E1' }}>
                            Ranked by Streak
                          </div>
                          <div className="text-xs mt-0.5 truncate" style={{ color: '#94A3B8' }}>
                            Sort by consecutive days
                          </div>
                        </div>
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Saturday Winners Trophy Icon */}
            <button
              onClick={() => setShowWinnersPopup(true)}
              className="p-2 rounded-lg border transition-all hover:scale-105 hover:bg-white/5 group"
              style={{
                backgroundColor: 'rgba(26, 28, 35, 0.96)',
                borderColor: 'rgba(255, 255, 255, 0.06)',
                color: '#F1F5F9'
              }}
              title="View Saturday Spin Winners"
            >
              <span className="text-sm group-hover:animate-pulse">🏆</span>
            </button>

            {/* XP Information Tooltip */}
            <div className="relative z-50">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button className="p-2 rounded-lg hover:bg-white/5 transition-colors group">
                      <svg
                        className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="left"
                    sideOffset={10}
                    alignOffset={-40}
                    className="max-w-xs p-3 border backdrop-blur-xl z-50 rounded-xl"
                    style={{
                      background: 'rgba(26, 28, 35, 0.96)',
                      backdropFilter: 'blur(6px)',
                      borderColor: 'rgba(255, 255, 255, 0.06)',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.35)',
                      zIndex: 9999
                    }}
                  >
                    <div className="space-y-2">
                      <h4 className="font-semibold text-white text-xs mb-2">XP Calculation System</h4>
                      <div className="space-y-1.5 text-[10px] text-gray-300">
                        <div className="flex justify-between">
                          <span>Daily Check-in:</span>
                          <span className="text-white font-medium">+10 XP</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Shift Report:</span>
                          <span className="text-white font-medium">+20 XP</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Meeting Attendance:</span>
                          <span className="text-white font-medium">+10 XP</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Streak Maintenance:</span>
                          <span className="text-white font-medium">+5 XP</span>
                        </div>
                        <div className="border-t border-white/20 pt-2 mt-2">
                          <div className="text-gray-400 text-xs mb-1">Daily Work Hours Bonus:</div>
                          <div className="flex justify-between">
                            <span>6-7.9 hours:</span>
                            <span className="text-white font-medium">+10 XP</span>
                          </div>
                          <div className="flex justify-between">
                            <span>8-9.9 hours:</span>
                            <span className="text-white font-medium">+20 XP</span>
                          </div>
                          <div className="flex justify-between">
                            <span>10+ hours:</span>
                            <span className="text-white font-medium">+30 XP</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Search Input */}
            <div className="relative flex-1 sm:flex-none">
              <input
                type="text"
                placeholder="Search employee..."
                value={searchTerm}
                onChange={(e) => {
                  setPage(1);
                  setSearchTerm(e.target.value);
                }}
                className="px-3 py-2 text-xs sm:text-sm rounded-lg border placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/30 transition-all w-full sm:w-56 pr-10 backdrop-blur-xl"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  borderColor: 'rgba(255, 255, 255, 0.1)',
                  color: '#F1F5F9'
                }}
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute right-3 top-2.5 text-sm hover:text-white transition-colors"
                  style={{ color: '#94A3B8' }}
                >
                  ✕
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Loading shimmer - Show during initial load or when actively loading */}
        {(loading || isInitialLoad) && (
          <div className="space-y-0 animate-pulse">
            {[...Array(10)].map((_, i) => (
              <div
                key={i}
                className="flex items-center space-x-4 px-4 py-3 backdrop-blur-sm"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.02)',
                  borderBottom: i < 9 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none'
                }}
              >
                <div className="w-7 h-7 rounded-full" style={{ background: 'rgba(255, 255, 255, 0.05)' }}></div>
                <div className="h-3 w-32 rounded flex-1" style={{ background: 'rgba(255, 255, 255, 0.05)' }}></div>
                <div className="h-3 w-20 rounded" style={{ background: 'rgba(255, 255, 255, 0.05)' }}></div>
                <div className="h-3 w-16 rounded" style={{ background: 'rgba(255, 255, 255, 0.05)' }}></div>
              </div>
            ))}
          </div>
        )}

        {/* Error */}
        {!loading && !isInitialLoad && error && (
          <div className="text-center text-red-500 py-8 text-sm font-medium">
            ⚠️ Failed to load leaderboard: {error}
          </div>
        )}

        {/* Empty */}
        {!loading && !isInitialLoad && !error && leaderboard.length === 0 && (
          <div className="text-center text-gray-500 py-8 text-sm">
            No matching employees found.
          </div>
        )}

        {/* Table with flat UI matching dashboard */}
        {!loading && !isInitialLoad && !error && leaderboard.length > 0 && (
          <>
            <div className="overflow-x-auto overflow-y-hidden scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
              <table className="w-full text-xs sm:text-sm text-left" style={{ minWidth: '800px' }}>
                <thead>
                  <tr className="uppercase text-[9px] sm:text-[10px] tracking-wide backdrop-blur-xl" style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.02)',
                    borderBottom: '1px solid rgba(255, 255, 255, 0.05)',
                    color: '#94A3B8'
                  }}>
                    <th className="px-2 sm:px-4 py-2 sm:py-3 font-medium text-left whitespace-nowrap">Rank</th>
                    <th className="px-2 sm:px-4 py-2 sm:py-3 font-medium text-left whitespace-nowrap">Employee</th>
                    <th className="px-2 sm:px-4 py-2 sm:py-3 font-medium text-left whitespace-nowrap">Points</th>
                    <th className="px-2 sm:px-4 py-2 sm:py-3 font-medium text-left whitespace-nowrap">Streak</th>
                    <th className="px-2 sm:px-4 py-2 sm:py-3 font-medium text-left whitespace-nowrap">Talent ID</th>
                    <th className="px-2 sm:px-4 py-2 sm:py-3 font-medium text-left whitespace-nowrap">Batch ID</th>
                  </tr>
                </thead>
                <tbody>
                  {leaderboard.map((talent, idx) => (
                    <tr
                      key={idx}
                      className="group transition-all duration-200 backdrop-blur-xl hover:bg-white/5"
                      style={{
                        backgroundColor: 'rgba(255, 255, 255, 0.02)',
                        borderBottom: idx < leaderboard.length - 1 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none'
                      }}
                    >
                      <td className="px-2 sm:px-4 py-2 sm:py-3">
                        <div className="flex items-center justify-start">
                          <div
                            className="w-7 h-7 rounded-full flex items-center justify-center backdrop-blur-sm font-semibold text-xs"
                            style={{
                              backgroundColor: 'rgba(255, 255, 255, 0.05)',
                              color: '#CBD5E1'
                            }}
                          >
                            {talent.rank}
                          </div>
                        </div>
                      </td>
                      <td className="px-2 sm:px-4 py-2 sm:py-3 font-medium">
                        <div className="flex items-center gap-2 sm:gap-3">
                          <div
                            className="w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-semibold backdrop-blur-sm overflow-hidden cursor-pointer transition-all duration-200 hover:ring-2 hover:ring-white/20 hover:scale-105"
                            style={{
                              background: talent.profilePicture
                                ? 'transparent'
                                : 'rgba(255, 255, 255, 0.05)',
                              color: '#CBD5E1'
                            }}
                            onClick={() => handleProfileClick(talent.talentId)}
                            title="Click to view profile"
                          >
                            {talent.profilePicture ? (
                              <img
                                src={talent.profilePicture}
                                alt={talent.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              talent.name?.charAt(0).toUpperCase()
                            )}
                          </div>
                          <span className="truncate text-xs sm:text-sm" style={{ color: '#F1F5F9' }}>{talent.name}</span>
                          {talent.rank === 1 && (
                            <span className="hidden sm:inline ml-2 text-[10px] px-2 py-0.5 rounded-full border backdrop-blur-xl" style={{
                              backgroundColor: 'rgba(255, 255, 255, 0.05)',
                              borderColor: 'rgba(255, 255, 255, 0.1)',
                              color: '#CBD5E1'
                            }}>
                              🏆 Top
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-2 sm:px-4 py-2 sm:py-3 font-semibold text-xs sm:text-sm whitespace-nowrap" style={{ color: '#94A3B8' }}>
                        {talent.xp ?? 0} XP
                      </td>
                      <td className="px-2 sm:px-4 py-2 sm:py-3 font-medium text-xs sm:text-sm whitespace-nowrap" style={{ color: '#CBD5E1' }}>
                        {talent.streak && talent.streak > 0 ? `🔥 ${talent.streak}` : '-'}
                      </td>
                      <td className="px-2 sm:px-4 py-2 sm:py-3 font-mono text-[10px] sm:text-xs whitespace-nowrap" style={{ color: '#94A3B8' }}>
                        {talent.talentId}
                      </td>
                      <td className="px-2 sm:px-4 py-2 sm:py-3 font-mono text-[10px] sm:text-xs whitespace-nowrap" style={{ color: '#94A3B8' }}>
                        {talent.batchId}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination Controls */}
            <div className="px-2 sm:px-4 py-3 border-t backdrop-blur-xl" style={{
              backgroundColor: 'rgba(255, 255, 255, 0.02)',
              borderColor: 'rgba(255, 255, 255, 0.05)'
            }}>
              {totalPages > 1 && (
                <>
                  <div className="overflow-x-auto">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => page > 1 && handlePageChange(page - 1)}
                            style={{
                              cursor: page > 1 ? 'pointer' : 'not-allowed',
                              opacity: page > 1 ? 1 : 0.5,
                            }}
                          />
                        </PaginationItem>

                        {renderPaginationItems()}

                        <PaginationItem>
                          <PaginationNext
                            onClick={() => page < totalPages && handlePageChange(page + 1)}
                            style={{
                              cursor: page < totalPages ? 'pointer' : 'not-allowed',
                              opacity: page < totalPages ? 1 : 0.5,
                            }}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>

                  <div className="mt-2 text-center">
                    <span className="text-[10px]" style={{ color: '#64748B' }}>
                      Page {page} of {totalPages}
                    </span>
                  </div>
                </>
              )}
            </div>
          </>
        )}
      </div>

      {/* User Profile Popup */}
      <UserProfilePopup
        user={selectedUserProfile}
        isOpen={showProfilePopup}
        onClose={handleCloseProfile}
      />

      {/* Saturday Winners Popup */}
      <SaturdayWinnersPopup
        isOpen={showWinnersPopup}
        onClose={() => setShowWinnersPopup(false)}
      />
    </div>
  );
}
