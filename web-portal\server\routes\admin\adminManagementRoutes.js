import express from 'express';
import { authenticateToken } from '../../middleware/auth.js';
import { requireSuperAdmin } from '../../middleware/adminAuth.js';
import {
  getAllAdmins,
  createAdmin,
  updateAdminRole,
  deleteAdmin,
  getAdminDetails,
  toggleAdminStatus,
  getAvailableRoles
} from '../../controllers/adminManagementController.js';

const router = express.Router();

// All routes require super admin access
router.use(authenticateToken, requireSuperAdmin);

// GET /api/admin/management/roles - Get available roles and permissions
router.get('/roles', getAvailableRoles);

// GET /api/admin/management/admins - Get all admin users
router.get('/admins', getAllAdmins);

// GET /api/admin/management/admins/:id - Get specific admin details
router.get('/admins/:id', getAdminDetails);

// POST /api/admin/management/admins - Create new admin
router.post('/admins', createAdmin);

// PUT /api/admin/management/admins/:id/role - Update admin role
router.put('/admins/:id/role', updateAdminRole);

// PUT /api/admin/management/admins/:id/status - Toggle admin active status
router.put('/admins/:id/status', toggleAdminStatus);

// DELETE /api/admin/management/admins/:id - Delete admin (soft delete)
router.delete('/admins/:id', deleteAdmin);

export default router;