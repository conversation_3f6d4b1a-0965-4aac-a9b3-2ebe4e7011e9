/**
 * Production-Level Admin Permission Examples
 * Demonstrates how to solve complex permission scenarios
 */

import { PERMISSION_MATRIX, COMPREHENSIVE_ROLES, hasDetailedPermission, getModuleAccess } from '../utils/permissionMatrix';

// REAL-WORLD USE CASE EXAMPLES

/**
 * CASE 1: Julia - Employee Admin with Limited Access
 * Requirement: Admin access but only employee features, no payments, no hiring
 */
export const JuliaPermissionExample = {
  user: {
    name: '<PERSON>',
    email: '<EMAIL>',
    userType: 'admin',
    permissionMode: 'custom',
    customPermissions: [
      // Talents - View and Edit only (no create/delete)
      'employee.talents.view',
      'employee.talents.edit',
      
      // Reports - Full access
      'employee.reports.view',
      'employee.reports.generate', 
      'employee.reports.export',
      
      // Leaderboard - View only
      'employee.leaderboard.view',
      'employee.leaderboard.export',
      
      // Tasks - Full management
      'employee.tasks.view',
      'employee.tasks.create',
      'employee.tasks.assign',
      'employee.tasks.edit',
      'employee.tasks.approve',
      
      // Leave - Approval powers
      'employee.leave.view',
      'employee.leave.approve',
      'employee.leave.reject',
      'employee.leave.calendar',
      
      // Meetings - Limited to viewing and scheduling
      'employee.meetings.view',
      'employee.meetings.schedule',
      'employee.meetings.edit',
      
      // Announcements - Can create and edit
      'employee.announcements.view',
      'employee.announcements.create',
      'employee.announcements.edit',
      
      // NO PAYMENTS - Julia cannot see payment section
      // NO HIRING - Julia cannot access hiring module
      // NO ADMIN MANAGEMENT - Julia cannot manage other admins
    ],
    canAccessTalentPortal: false
  },
  
  // What Julia CAN do:
  capabilities: {
    employee_talents: 'View and edit talent profiles (cannot create/delete)',
    employee_reports: 'Full report generation and export',
    employee_leaderboard: 'View rankings and export data',
    employee_tasks: 'Complete task management',
    employee_leave: 'Approve/reject leave requests',
    employee_meetings: 'Schedule and manage meetings',
    employee_announcements: 'Create and manage announcements'
  },
  
  // What Julia CANNOT do:
  restrictions: {
    payments: 'NO ACCESS - Cannot view payment section',
    hiring: 'NO ACCESS - Cannot access hiring module',
    talent_creation: 'Cannot create new talents',
    talent_deletion: 'Cannot delete talents',
    admin_management: 'Cannot manage other admins'
  }
};

/**
 * CASE 2: HR Specialist with Cross-Module Access
 * Requirement: HR functions across both employee and hiring modules
 */
export const HRSpecialistExample = {
  user: {
    name: 'Sarah',
    email: '<EMAIL>', 
    userType: 'admin',
    permissionMode: 'role',
    roleType: 'hr_specialist',
    customPermissions: [],
    canAccessTalentPortal: false
  },
  
  capabilities: {
    employee_hr: 'Manage talent profiles, leave, compliance',
    hiring_hr: 'Review applications, schedule interviews, manage onboarding',
    cross_module: 'HR-focused features across all modules'
  }
};

/**
 * CASE 3: Hiring Manager with Employee View Access  
 * Requirement: Full hiring access + limited employee viewing
 */
export const HiringManagerPlusExample = {
  user: {
    name: 'Mike',
    email: '<EMAIL>',
    userType: 'admin', 
    permissionMode: 'custom',
    customPermissions: [
      // FULL HIRING MODULE ACCESS
      'hiring.jobs.view',
      'hiring.jobs.create',
      'hiring.jobs.edit', 
      'hiring.jobs.delete',
      'hiring.jobs.publish',
      'hiring.jobs.clone',
      
      'hiring.applications.view',
      'hiring.applications.review',
      'hiring.applications.shortlist',
      'hiring.applications.reject',
      'hiring.applications.contact',
      'hiring.applications.export',
      
      'hiring.interviews.view',
      'hiring.interviews.schedule',
      'hiring.interviews.edit',
      'hiring.interviews.cancel',
      'hiring.interviews.conduct',
      'hiring.interviews.evaluate',
      
      'hiring.onboarding.view',
      'hiring.onboarding.manage',
      'hiring.onboarding.approve',
      'hiring.onboarding.documents',
      
      // SELECTED EMPLOYEE ACCESS
      'employee.talents.view', // Can view existing talents
      'employee.reports.view', // Can see reports
      'employee.analytics.view', // Can view analytics
      'employee.leaderboard.view', // Can see performance
      
      // NO EMPLOYEE EDITING/MANAGEMENT POWERS
      // NO PAYMENT ACCESS
      // NO ADMIN MANAGEMENT
    ],
    canAccessTalentPortal: false
  },
  
  capabilities: {
    hiring_complete: 'Full hiring workflow management',
    employee_readonly: 'Read-only access to employee data for context'
  }
};

/**
 * CASE 4: Finance Admin - Payment Focus
 * Requirement: Full payment access + financial reporting + basic employee view
 */
export const FinanceAdminExample = {
  user: {
    name: 'David',
    email: '<EMAIL>',
    userType: 'admin',
    permissionMode: 'role', 
    roleType: 'finance_admin',
    customPermissions: [],
    canAccessTalentPortal: false
  },
  
  capabilities: {
    payments_full: 'Complete payment processing and management',
    reports_financial: 'Generate financial and compliance reports',
    analytics_financial: 'Access to financial analytics',
    employee_basic: 'Basic talent viewing for context'
  }
};

// PERMISSION CHECKING FUNCTIONS

/**
 * Check if user can perform specific action
 */
export const canUserPerform = (user, module, feature, action) => {
  const permission = `${module}.${feature}.${action}`;
  return hasDetailedPermission(user, permission);
};

/**
 * Get user's access level for a module
 */
export const getUserModuleAccess = (user, module) => {
  return getModuleAccess(user, module);
};

/**
 * Generate permission summary for UI display
 */
export const generatePermissionSummary = (user) => {
  const summary = {
    employee: {},
    hiring: {},
    admin: {}
  };
  
  Object.keys(PERMISSION_MATRIX).forEach(moduleKey => {
    summary[moduleKey] = getModuleAccess(user, moduleKey);
  });
  
  return summary;
};

// EXAMPLES OF HOW TO USE IN COMPONENTS

/**
 * Example: Conditional rendering in Employee Talents page
 */
export const TalentsPageExample = `
// In your TalentsPage component:
import { canUserPerform } from '../utils/permissionExamples';

const TalentsPage = () => {
  const { user } = useAuthStore();
  
  const canViewTalents = canUserPerform(user, 'employee', 'talents', 'view');
  const canCreateTalents = canUserPerform(user, 'employee', 'talents', 'create');
  const canEditTalents = canUserPerform(user, 'employee', 'talents', 'edit');
  const canDeleteTalents = canUserPerform(user, 'employee', 'talents', 'delete');
  
  if (!canViewTalents) {
    return <div>Access Denied</div>;
  }
  
  return (
    <div>
      {/* Talent list - always shown if user can view */}
      <TalentList />
      
      {/* Create button - only if user can create */}
      {canCreateTalents && (
        <Button onClick={handleCreate}>Create New Talent</Button>
      )}
      
      {/* Edit/Delete actions - conditional per talent */}
      {talents.map(talent => (
        <TalentCard 
          key={talent.id}
          talent={talent}
          canEdit={canEditTalents}
          canDelete={canDeleteTalents}
        />
      ))}
    </div>
  );
};
`;

/**
 * Example: Navigation menu filtering
 */
export const NavigationExample = `
// In your AdminLayout component:
import { getUserModuleAccess } from '../utils/permissionExamples';

const AdminLayout = () => {
  const { user } = useAuthStore();
  const employeeAccess = getUserModuleAccess(user, 'employee');
  const hiringAccess = getUserModuleAccess(user, 'hiring');
  const adminAccess = getUserModuleAccess(user, 'admin');
  
  return (
    <nav>
      {/* Employee Menu */}
      {Object.values(employeeAccess).some(feature => Object.values(feature).includes(true)) && (
        <MenuSection title="Employee">
          {employeeAccess.talents.view && <MenuItem href="/admin/talents">Talents</MenuItem>}
          {employeeAccess.payments.view && <MenuItem href="/admin/payments">Payments</MenuItem>}
          {employeeAccess.reports.view && <MenuItem href="/admin/reports">Reports</MenuItem>}
        </MenuSection>
      )}
      
      {/* Hiring Menu */}
      {Object.values(hiringAccess).some(feature => Object.values(feature).includes(true)) && (
        <MenuSection title="Hiring">
          {hiringAccess.jobs.view && <MenuItem href="/admin/hiring/jobs">Jobs</MenuItem>}
          {hiringAccess.applications.view && <MenuItem href="/admin/hiring/applications">Applications</MenuItem>}
        </MenuSection>
      )}
    </nav>
  );
};
`;

export default {
  JuliaPermissionExample,
  HRSpecialistExample, 
  HiringManagerPlusExample,
  FinanceAdminExample,
  canUserPerform,
  getUserModuleAccess,
  generatePermissionSummary,
  TalentsPageExample,
  NavigationExample
};