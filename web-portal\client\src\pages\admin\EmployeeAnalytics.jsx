import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { HiRefresh, HiExclamationCircle, HiCheckCircle } from 'react-icons/hi';
import { FiUsers } from 'react-icons/fi';
import { useAuthStore } from '../../store/authStore';
import { getUserRole, hasAdminAccess } from '../../utils/rolePermissions';
import { useJibbleStore } from '../../store/jibble/jibbleStore';
import AdminLayout from '../../components/admin/AdminLayout';
import EmployeeList from '../../components/jibble/EmployeeList';
import EmployeeAnalyticsView from '../../components/jibble/EmployeeAnalyticsView';
import ActiveUsersPopup from '../../components/ActiveUsersPopup';
import { Card } from '../../components/ui/card';

export default function EmployeeAnalytics() {
  const navigate = useNavigate();
  const { user, isInitializing } = useAuthStore();
  const {
    employees,
    selectedEmployee,
    employeeAnalytics,
    loading,
    analyticsLoading,
    error,
    fetchEmployees,
    fetchEmployeeAnalytics,
    fetchActiveUsers,
    clearError,
    setSelectedEmployee,
    clearSelectedEmployee
  } = useJibbleStore();

  const [view, setView] = useState('list'); // 'list' or 'analytics'
  const [activeUsersData, setActiveUsersData] = useState(null);
  const [activeUsersLoading, setActiveUsersLoading] = useState(false);
  const [showActiveUsersPopup, setShowActiveUsersPopup] = useState(false);

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;
    
    if (!user || !hasAdminAccess(user)) {
      navigate('/talent/dashboard');
    }
  }, [user, navigate, isInitializing]);

  // Initialize data on component mount
  useEffect(() => {
    if (hasAdminAccess(user)) {
      initializeData();
    }
  }, [user]);

  // Auto-fetch active users data on component mount
  useEffect(() => {
    if (hasAdminAccess(user)) {
      fetchActiveUsersData();
    }
  }, [user]);

  const initializeData = async () => {
    try {
      clearError();
      await fetchEmployees();
    } catch (error) {
      console.error('Failed to initialize data:', error);
    }
  };

  const handleRefresh = async () => {
    try {
      clearError();
      await fetchEmployees();
      // Also refresh active users data
      await fetchActiveUsersData();
    } catch (error) {
      console.error('Failed to refresh data:', error);
    }
  };

  const handleEmployeeSelect = async (employee) => {
    setSelectedEmployee(employee);
    setView('analytics');
    
    // Fetch analytics data for the selected employee
    try {
      await fetchEmployeeAnalytics(employee.id, 30); // Default to 30 days
    } catch (error) {
      console.error('Failed to fetch employee analytics:', error);
    }
  };

  const handleBackToList = () => {
    clearSelectedEmployee();
    setView('list');
  };

  // Fetch active users data
  const fetchActiveUsersData = async () => {
    setActiveUsersLoading(true);
    try {
      const data = await fetchActiveUsers(true);
      setActiveUsersData(data);
    } catch (error) {
      console.error('Failed to fetch active users:', error);
    } finally {
      setActiveUsersLoading(false);
    }
  };

  // Handle active users count click
  const handleActiveUsersClick = () => {
    // Open popup instantly for better UX
    setShowActiveUsersPopup(true);
    // Fetch fresh data in background
    fetchActiveUsersData();
  };

  // Get total active users count
  const getTotalActiveUsers = () => {
    if (!activeUsersData?.data) return 0;
    const { clockedInUsers = [], breakUsers = [] } = activeUsersData.data;
    return clockedInUsers.length + breakUsers.length;
  };

  if (isInitializing || !hasAdminAccess(user)) {
    return null;
  }

  return (
    <AdminLayout>
      <div className="flex-1 space-y-4 p-4">
        {/* Header with Stats */}
        <div className="flex items-start justify-between gap-6 mb-4">
          <div className="flex items-center gap-3">
            <div className="min-w-0 relative z-10">
              <h1 className="text-2xl font-bold truncate tracking-tight" style={{
                color: '#F1F5F9',
                textShadow: '0 2px 8px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 0, 0, 0.6)'
              }}>
                Employee Analytics
              </h1>
              <p className="text-sm truncate mt-1" style={{
                color: '#CBD5E1',
                textShadow: '0 1px 4px rgba(0, 0, 0, 0.8), 0 0 12px rgba(0, 0, 0, 0.5)'
              }}>
                View employee performance metrics and time tracking data
              </p>
            </div>
          </div>

          {/* Compact Stats Cards (styled as horizontal Card list) */}
          {employees.length > 0 && view === 'list' && (
            <div className="flex items-center gap-3 flex-nowrap overflow-x-auto">
              <Card className="relative z-20 flex-shrink-0 inline-flex items-center w-40 sm:w-44 border-border/40 bg-card/95 shadow-lg p-1">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-md bg-card/95 flex items-center justify-center">
                    <HiCheckCircle className="h-4 w-4 text-foreground/90" />
                  </div>
                  <div className="pl-1">
                    <div className="text-xs font-medium text-muted-foreground">Total Employees</div>
                    <div className="text-base font-semibold text-white">{employees.length}</div>
                  </div>
                </div>
              </Card>

              <Card className="relative z-20 flex-shrink-0 inline-flex items-center w-44 sm:w-48 border-border/40 bg-card/95 shadow-lg p-1">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-md bg-card/95 flex items-center justify-center">
                    <HiCheckCircle className="h-4 w-4 text-foreground/90" />
                  </div>
                  <div className="pl-1">
                    <div className="text-xs font-medium text-muted-foreground">Active Employees</div>
                    <div className="text-base font-semibold text-white">{employees.length}</div>
                  </div>
                </div>
              </Card>

              <Card className="relative z-20 flex-shrink-0 inline-flex items-center w-40 sm:w-44 border-border/40 bg-card/95 shadow-lg p-1">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-md bg-card/95 flex items-center justify-center">
                    <HiCheckCircle className="h-4 w-4 text-foreground/90" />
                  </div>
                  <div className="pl-1">
                    <div className="text-xs font-medium text-muted-foreground">Data Source</div>
                    <div className="text-sm font-bold text-white">Jibble API</div>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-500/20 border border-red-500/50 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg flex items-center">
            <HiExclamationCircle className="w-4 h-4 mr-2 flex-shrink-0" />
            <span className="flex-1">{error}</span>
            <button
              onClick={clearError}
              className="ml-2 text-red-500 hover:text-red-400 dark:text-red-400 dark:hover:text-red-300"
            >
              ×
            </button>
          </div>
        )}

        {/* Main Content */}
        <div className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg rounded-lg">
          {view === 'list' ? (
            <EmployeeList
              employees={employees}
              loading={loading}
              onEmployeeSelect={handleEmployeeSelect}
              activeUsersCount={getTotalActiveUsers()}
              activeUsersLoading={activeUsersLoading}
              onActiveUsersClick={handleActiveUsersClick}
            />
          ) : (
            <EmployeeAnalyticsView
              employee={selectedEmployee}
              onBack={handleBackToList}
            />
          )}
        </div>
          
        

        {/* Active Users Popup */}
        <ActiveUsersPopup
          isOpen={showActiveUsersPopup}
          onClose={() => setShowActiveUsersPopup(false)}
          activeUsersData={activeUsersData}
          loading={activeUsersLoading}
        />
      </div>
    </AdminLayout>
  );
}