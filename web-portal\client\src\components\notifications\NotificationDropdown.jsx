import useNotificationStore from '../../store/notificationStore';
import NotificationItem from './NotificationItem';

export default function NotificationDropdown({ onClose, onViewAll }) {
  const { notifications, unreadCount, markAllAsRead, loading } = useNotificationStore();

  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
  };

  const recentNotifications = notifications.slice(0, 7);

  return (
    <div
      className="fixed md:absolute left-1/2 top-1/2 md:top-full md:left-auto md:right-0 -translate-x-1/2 -translate-y-1/2 md:translate-x-0 md:-translate-y-0 w-[95vw] max-w-[420px] overflow-hidden rounded-2xl shadow-2xl"
      style={{
        // Use a near-solid dark panel matching dashboard cards (#1A1C23)
        background: 'rgba(26, 28, 35, 0.96)',
        // keep only a very slight blur if needed
        backdropFilter: 'blur(4px) saturate(105%)',
        border: '1px solid rgba(255, 255, 255, 0.04)',
        boxShadow: '0 20px 40px -10px rgba(2, 6, 23, 0.85)',
        zIndex: 10000,
      }}
    >
      {/* Mobile Close Icon */}
      <button
        onClick={onClose}
        className="absolute top-3 right-3 text-gray-300 hover:text-white text-2xl font-bold focus:outline-none md:hidden"
        aria-label="Close Notifications"
        style={{ lineHeight: 1, zIndex: 10001 }}
      >
        ×
      </button>
      {/* Header */}
      <div
        className="flex items-center justify-between px-5 py-4 border-b"
        style={{
          borderColor: 'rgba(255, 255, 255, 0.06)',
          // Use the same dark header to blend with the main panel
          background: 'rgba(26, 28, 35, 0.96)',
        }}
      >
        <div className="flex items-center gap-2.5">
          <h3 className="text-base font-semibold" style={{ color: '#F8FAFC', letterSpacing: '-0.01em' }}>
            Notifications
          </h3>
          {unreadCount > 0 && (
            <span
              className="px-2 py-0.5 text-[11px] font-medium rounded-md"
              style={{
                background: 'rgba(255, 255, 255, 0.06)',
                color: '#E2E8F0',
                border: '1px solid rgba(255, 255, 255, 0.08)',
              }}
            >
              {unreadCount}
            </span>
          )}
        </div>
        {unreadCount > 0 && (
          <button
            onClick={handleMarkAllAsRead}
            className="text-[13px] font-medium transition-all duration-200 hover:text-white px-2 py-1 rounded-lg hover:bg-white/5"
            style={{ color: '#94A3B8' }}
          >
            Mark all read
          </button>
        )}
      </div>

      {/* Notifications List */}
      <div className="max-h-[460px] overflow-y-auto p-3" style={{
        scrollbarWidth: 'thin',
        scrollbarColor: 'rgba(255, 255, 255, 0.2) transparent',
      }}>
        {loading ? (
          // Loading state
          <div className="space-y-2">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="rounded-xl p-3 backdrop-blur-xl border animate-pulse"
                style={{
                  // Slightly lighter than container so individual skeleton cards are readable
                  background: 'rgba(255, 255, 255, 0.03)',
                  borderColor: 'rgba(255, 255, 255, 0.045)',
                  boxShadow: '0 4px 16px 0 rgba(0, 0, 0, 0.5)',
                }}
              >
                <div className="flex items-start gap-3">
                  <div
                    className="w-10 h-10 rounded-xl flex-shrink-0"
                    style={{ background: 'rgba(255, 255, 255, 0.04)' }}
                  ></div>
                  <div className="flex-1 space-y-2 pt-0.5">
                    <div className="h-3 rounded-md" style={{ background: 'rgba(255, 255, 255, 0.08)', width: '75%' }}></div>
                    <div className="h-2 rounded-md" style={{ background: 'rgba(255, 255, 255, 0.05)', width: '55%' }}></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : recentNotifications.length === 0 ? (
          // Empty state
          <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
            <div className="w-14 h-14 rounded-2xl mb-4 flex items-center justify-center" style={{ background: 'rgba(255, 255, 255, 0.045)' }}>
              <svg
                className="w-7 h-7"
                style={{ color: '#94A3B8' }}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                />
              </svg>
            </div>
            <p className="text-sm font-medium" style={{ color: '#F8FAFC' }}>
              No notifications yet
            </p>
            <p className="text-[13px] mt-1.5" style={{ color: '#C9D6F2' }}>
              You're all caught up! 🎉
            </p>
          </div>
        ) : (
          // Notifications list
          <>
            {recentNotifications.map((notification, index) => (
              <NotificationItem
                key={notification._id}
                notification={notification}
                isLast={index === recentNotifications.length - 1}
                onClose={onClose}
              />
            ))}
          </>
        )}
      </div>

      {/* Footer */}
      {recentNotifications.length > 0 && (
        <div
          className="px-5 py-3.5 border-t text-center"
          style={{
            borderColor: 'rgba(255, 255, 255, 0.06)',
            // Bottom bar should match the main panel so it blends smoothly
            background: 'rgba(26, 28, 35, 0.96)',
          }}
        >
          <button
            onClick={onViewAll}
            className="text-[13px] font-medium transition-all duration-200 hover:text-white px-3 py-1.5 rounded-lg hover:bg-white/5"
            style={{ color: '#E6F0FF' }}
          >
            View all notifications
          </button>
        </div>
      )}
    </div>
  );
}
