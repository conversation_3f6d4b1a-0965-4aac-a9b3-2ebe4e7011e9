import Job from "../../models/hiring/Job.js";
import mongoose from "mongoose";
import Application from "../../models/hiring/Application.js"; // Section 2: Application model now available
import { sanitizeHtml, sanitizeText } from "../../utils/securityUtils.js";
import { uploadToCloudinary } from "../../utils/cloudinary.js";

/**
 * @desc    Create new job (Draft status)
 * @route   POST /api/admin/jobs
 * @access  Private (Admin only)
 */
export const createJob = async (req, res) => {
  try {
    const {
      title,
      description,
      companyInfo,
      responsibilities,
      experienceLevel,
      jobType,
      compensationType,
    } = req.body;

    // Validation
    if (!title || !description || !companyInfo || !responsibilities) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        details:
          "Title, description, company info, and responsibilities are required",
      });
    }

    // SECURITY: Sanitize HTML fields to prevent XSS attacks
    const sanitizedDescription = sanitizeHtml(description);
    const sanitizedCompanyInfo = sanitizeHtml(companyInfo);
    const sanitizedResponsibilities = sanitizeHtml(responsibilities);
    const sanitizedTitle = sanitizeText(title);

    // Prepare job data
    const jobData = {
      title: sanitizedTitle,
      description: sanitizedDescription,
      companyInfo: sanitizedCompanyInfo,
      responsibilities: sanitizedResponsibilities,
      experienceLevel,
      jobType,
      compensationType,
      status: "draft",
      createdBy: req.user.userId,
    };

    // Handle image uploads if provided
    console.log("📁 req.files:", req.files);
    if (req.files) {
      console.log("✅ Files detected in request");

      // Upload background image if provided
      if (req.files.backgroundImage && req.files.backgroundImage[0]) {
        console.log("🖼️ Uploading background image...");
        try {
          const backgroundResult = await uploadToCloudinary(
            req.files.backgroundImage[0].buffer,
            {
              folder: "hiring/job-images/backgrounds",
              resource_type: "image",
              // Don't set format - let Cloudinary detect from file
            }
          );
          jobData.backgroundImage = {
            url: backgroundResult.secure_url,
            publicId: backgroundResult.public_id,
          };
          console.log(
            "✅ Background image uploaded:",
            backgroundResult.secure_url
          );
        } catch (uploadError) {
          console.error("❌ Background image upload error:", uploadError);
          return res.status(500).json({
            success: false,
            error: "Failed to upload background image",
            details: uploadError.message,
          });
        }
      } else {
        console.log("ℹ️ No background image in request");
      }

      // Upload banner image if provided
      if (req.files.bannerImage && req.files.bannerImage[0]) {
        console.log("🎨 Uploading banner image...");
        try {
          const bannerResult = await uploadToCloudinary(
            req.files.bannerImage[0].buffer,
            {
              folder: "hiring/job-images/banners",
              resource_type: "image",
              // Don't set format - let Cloudinary detect from file
            }
          );
          jobData.bannerImage = {
            url: bannerResult.secure_url,
            publicId: bannerResult.public_id,
          };
          console.log("✅ Banner image uploaded:", bannerResult.secure_url);
        } catch (uploadError) {
          console.error("❌ Banner image upload error:", uploadError);
          return res.status(500).json({
            success: false,
            error: "Failed to upload banner image",
            details: uploadError.message,
          });
        }
      } else {
        console.log("ℹ️ No banner image in request");
      }
    } else {
      console.log("⚠️ No files in request");
    }

    console.log("💾 Final jobData:", JSON.stringify(jobData, null, 2));

    // Create job in draft status
    const job = await Job.create(jobData);

    // Populate creator info
    await job.populate("createdBy", "name email");

    // Check if reference exists
    if (!job.createdBy) {
      console.warn(`Job ${job._id} has invalid createdBy reference`);
    }

    res.status(201).json({
      success: true,
      message: "Job created successfully",
      job,
    });
  } catch (error) {
    console.error("Create job error:", error);

    // Handle validation errors
    if (error.name === "ValidationError") {
      const errors = Object.values(error.errors).map((err) => err.message);
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors,
      });
    }

    res.status(500).json({
      success: false,
      error: "Failed to create job",
      message: error.message,
    });
  }
};

/**
 * @desc    Get all jobs (Admin dashboard)
 * @route   GET /api/admin/jobs
 * @access  Private (Admin only)
 */
export const getAllJobs = async (req, res) => {
  try {
    const {
      status,
      page = 1,
      limit = 20,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
      includeArchived = "false", // Default: hide archived jobs
    } = req.query;

    // Build query
    const query = {};

    // Filter out archived jobs by default (unless explicitly requested)
    if (includeArchived !== "true") {
      query.isArchived = { $ne: true };
    }

    if (status) {
      query.status = status;
    }

    if (search) {
      query.title = { $regex: search, $options: "i" };
    }

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const limitNum = Math.min(parseInt(limit), 100); // Max 100 per page

    // Sort
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Execute query
    const [jobs, totalJobs] = await Promise.all([
      Job.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limitNum)
        .populate("createdBy", "name email")
        .populate("lastEditedBy", "name email")
        .lean(),
      Job.countDocuments(query),
    ]);

    res.json({
      success: true,
      jobs,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalJobs / limitNum),
        totalJobs,
        limit: limitNum,
        hasNextPage: skip + limitNum < totalJobs,
        hasPrevPage: parseInt(page) > 1,
      },
    });
  } catch (error) {
    console.error("Get jobs error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch jobs",
      message: error.message,
    });
  }
};

/**
 * @desc    Get job by ID (Admin)
 * @route   GET /api/admin/jobs/:id
 * @access  Private (Admin only)
 */
export const getJobById = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate MongoDB ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: "Invalid job ID format",
      });
    }

    const job = await Job.findById(id)
      .populate("createdBy", "name email")
      .populate("lastEditedBy", "name email")
      .populate({
        path: "editHistory.editedBy",
        select: "name email",
      });

    if (!job) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
      });
    }

    res.json({
      success: true,
      job,
    });
  } catch (error) {
    console.error("Get job error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch job",
      message: error.message,
    });
  }
};

/**
 * @desc    Update job
 * @route   PUT /api/admin/jobs/:id
 * @access  Private (Admin only)
 */
export const updateJob = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Validate MongoDB ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: "Invalid job ID format",
      });
    }

    // SECURITY: Sanitize HTML fields before update
    if (updateData.title) {
      updateData.title = sanitizeText(updateData.title);
    }
    if (updateData.description) {
      updateData.description = sanitizeHtml(updateData.description);
    }
    if (updateData.companyInfo) {
      updateData.companyInfo = sanitizeHtml(updateData.companyInfo);
    }
    if (updateData.responsibilities) {
      updateData.responsibilities = sanitizeHtml(updateData.responsibilities);
    }

    // Fields that cannot be updated via this endpoint
    const protectedFields = [
      "slug",
      "status",
      "location",
      "createdBy",
      "applicationsCount",
      "viewsCount",
      "publishedAt",
      "closedAt",
    ];
    protectedFields.forEach((field) => delete updateData[field]);

    const job = await Job.findById(id);

    if (!job) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
      });
    }

    // Track changes for edit history
    const changes = {};
    Object.keys(updateData).forEach((key) => {
      if (job[key] !== updateData[key]) {
        changes[key] = {
          old: job[key],
          new: updateData[key],
        };
      }
    });

    // Update job
    Object.assign(job, updateData);
    job.lastEditedBy = req.user.userId;
    job.lastEditedAt = new Date();

    // Add to edit history
    if (Object.keys(changes).length > 0) {
      job.editHistory.push({
        editedBy: req.user.userId,
        editedAt: new Date(),
        changes: JSON.stringify(changes),
      });
    }

    await job.save();
    await job.populate("createdBy lastEditedBy", "name email");

    res.json({
      success: true,
      message: "Job updated successfully",
      job,
    });
  } catch (error) {
    console.error("Update job error:", error);

    if (error.name === "ValidationError") {
      const errors = Object.values(error.errors).map((err) => err.message);
      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: errors,
      });
    }

    res.status(500).json({
      success: false,
      error: "Failed to update job",
      message: error.message,
    });
  }
};

/**
 * @desc    Publish job
 * @route   PATCH /api/admin/jobs/:id/publish
 * @access  Private (Admin only)
 */
export const publishJob = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate MongoDB ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: "Invalid job ID format",
      });
    }

    const job = await Job.findById(id);

    if (!job) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
      });
    }

    if (job.status === "published") {
      return res.status(400).json({
        success: false,
        error: "Job is already published",
      });
    }

    if (job.status === "closed") {
      return res.status(400).json({
        success: false,
        error: "Cannot publish a closed job. Create a new job posting instead.",
      });
    }

    // Validate job completeness before publishing
    const requiredFields = [
      "title",
      "description",
      "companyInfo",
      "responsibilities",
      "experienceLevel",
      "jobType",
      "compensationType",
    ];

    const missingFields = requiredFields.filter((field) => !job[field]);

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        error: "Job is incomplete",
        missingFields,
        message: "Please complete all required fields before publishing",
      });
    }

    job.status = "published";
    job.publishedAt = new Date();
    await job.save();

    res.json({
      success: true,
      message: "Job published successfully",
      job: {
        _id: job._id,
        title: job.title,
        slug: job.slug,
        status: job.status,
        publishedAt: job.publishedAt,
        applicationLink: job.applicationLink,
      },
    });
  } catch (error) {
    console.error("Publish job error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to publish job",
      message: error.message,
    });
  }
};

/**
 * @desc    Close job
 * @route   PATCH /api/admin/jobs/:id/close
 * @access  Private (Admin only)
 */
export const closeJob = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate MongoDB ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: "Invalid job ID format",
      });
    }

    const job = await Job.findById(id);

    if (!job) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
      });
    }

    if (job.status !== "published") {
      return res.status(400).json({
        success: false,
        error: "Only published jobs can be closed",
      });
    }

    job.status = "closed";
    job.closedAt = new Date();
    await job.save();

    res.json({
      success: true,
      message: "Job closed successfully",
      job: {
        _id: job._id,
        title: job.title,
        status: job.status,
        closedAt: job.closedAt,
        applicationsCount: job.applicationsCount,
      },
    });
  } catch (error) {
    console.error("Close job error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to close job",
      message: error.message,
    });
  }
};

/**
 * @desc    Archive job (soft delete - hide from default listings)
 * @route   PATCH /api/admin/jobs/:id/archive
 * @access  Private (Admin only)
 */
export const archiveJob = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate MongoDB ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: "Invalid job ID format",
      });
    }

    const job = await Job.findById(id);

    if (!job) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
      });
    }

    if (job.isArchived) {
      return res.status(400).json({
        success: false,
        error: "Job is already archived",
      });
    }

    job.isArchived = true;
    job.archivedAt = new Date();
    await job.save();

    res.json({
      success: true,
      message:
        "Job archived successfully. You can still view it in archived jobs.",
      job: {
        _id: job._id,
        title: job.title,
        isArchived: job.isArchived,
        archivedAt: job.archivedAt,
      },
    });
  } catch (error) {
    console.error("Archive job error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to archive job",
      message: error.message,
    });
  }
};

/**
 * @desc    Duplicate job (create copy in draft status)
 * @route   POST /api/admin/jobs/:id/duplicate
 * @access  Private (Admin only)
 */
export const duplicateJob = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate MongoDB ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: "Invalid job ID format",
      });
    }

    const originalJob = await Job.findById(id);

    if (!originalJob) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
      });
    }

    // Create duplicate with same content but draft status
    const newJob = await Job.create({
      title: originalJob.title,
      description: originalJob.description,
      companyInfo: originalJob.companyInfo,
      responsibilities: originalJob.responsibilities,
      experienceLevel: originalJob.experienceLevel,
      jobType: originalJob.jobType,
      compensationType: originalJob.compensationType,
      status: "draft", // Always start as draft
      createdBy: req.user.userId,
    });

    await newJob.populate("createdBy", "name email");

    res.status(201).json({
      success: true,
      message: "Job duplicated successfully. New job created in draft status.",
      job: newJob,
      originalJobId: originalJob._id,
    });
  } catch (error) {
    console.error("Duplicate job error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to duplicate job",
      message: error.message,
    });
  }
};

/**
 * @desc    Get job statistics
 * @route   GET /api/admin/jobs/stats
 * @access  Private (Admin only)
 */
export const getJobStats = async (req, res) => {
  try {
    const [totalJobs, draftJobs, publishedJobs, closedJobs, recentJobs] =
      await Promise.all([
        Job.countDocuments(),
        Job.countDocuments({ status: "draft" }),
        Job.countDocuments({ status: "published" }),
        Job.countDocuments({ status: "closed" }),
        Job.find({ status: "published" })
          .sort({ createdAt: -1 })
          .limit(5)
          .select("title applicationsCount createdAt")
          .lean(),
      ]);

    // Calculate total applications (sum of applicationsCount from all jobs)
    const allJobs = await Job.find().select("applicationsCount").lean();
    const totalApplications = allJobs.reduce(
      (sum, job) => sum + job.applicationsCount,
      0
    );
    const averageApplicationsPerJob =
      totalJobs > 0 ? (totalApplications / totalJobs).toFixed(1) : 0;

    // TODO: When Application model exists (Section 2), use this instead:
    // const totalApplications = await Application.countDocuments();

    res.json({
      success: true,
      stats: {
        total: totalJobs,
        draft: draftJobs,
        published: publishedJobs,
        closed: closedJobs,
        totalApplications,
        averageApplicationsPerJob: parseFloat(averageApplicationsPerJob),
        recentJobs,
      },
    });
  } catch (error) {
    console.error("Get job stats error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch statistics",
      message: error.message,
    });
  }
};

// ==================== PUBLIC ENDPOINTS ====================

/**
 * @desc    Get job by slug (Public application page)
 * @route   GET /api/jobs/apply/:slug
 * @access  Public
 */
export const getJobBySlug = async (req, res) => {
  try {
    const { slug } = req.params;

    const job = await Job.findOne({ slug }).select(
      "-editHistory -createdBy -lastEditedBy"
    );

    if (!job) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
        message:
          "The job posting you're looking for doesn't exist or has been removed.",
      });
    }

    // Draft jobs are not publicly accessible
    if (job.status === "draft") {
      return res.status(404).json({
        success: false,
        error: "Job not found",
        message:
          "The job posting you're looking for doesn't exist or has been removed.",
      });
    }

    // Closed jobs show special message
    if (job.status === "closed") {
      return res.json({
        success: true,
        job: {
          title: job.title,
          status: "closed",
          canApply: false,
          message:
            "This position has been filled. We'll be posting new opportunities soon. Keep an eye on our careers page!",
        },
      });
    }

    // Published jobs show full details
    res.json({
      success: true,
      job: {
        _id: job._id,
        title: job.title,
        description: job.description,
        companyInfo: job.companyInfo,
        responsibilities: job.responsibilities,
        experienceLevel: job.experienceLevel,
        jobType: job.jobType,
        location: job.location,
        compensationType: job.compensationType,
        compensationNote:
          job.compensationType === "paid"
            ? "Salary details will be mentioned in offer letter"
            : "This is an unpaid position",
        status: job.status,
        canApply: true,
        backgroundImage: job.backgroundImage,
        bannerImage: job.bannerImage,
      },
    });
  } catch (error) {
    console.error("Get job by slug error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch job",
      message: error.message,
    });
  }
};

/**
 * @desc    Increment job view count
 * @route   POST /api/jobs/:slug/view
 * @access  Public
 */
export const incrementViewCount = async (req, res) => {
  try {
    const { slug } = req.params;

    const job = await Job.findOneAndUpdate(
      { slug, status: "published" },
      { $inc: { viewsCount: 1 } },
      { new: true }
    ).select("viewsCount");

    if (!job) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
      });
    }

    res.json({
      success: true,
      viewsCount: job.viewsCount,
    });
  } catch (error) {
    console.error("Increment view count error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to update view count",
    });
  }
};
