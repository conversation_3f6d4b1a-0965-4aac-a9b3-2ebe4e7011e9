import React, { useState, useEffect } from "react";
import AdminLayout from "../../components/admin/AdminLayout";
import { useAdminTaskStore } from "../../store/adminTaskStore";
import { useAuthStore } from "../../store/authStore";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import {
  HiClipboardList as ClipboardListIcon,
  HiPlus as PlusIcon,
  HiPencil as PencilIcon,
  HiTrash as TrashIcon,
  HiEye as EyeIcon,
  HiClock as ClockIcon,
  HiUser as UserIcon,
  HiExclamation as ExclamationIcon,
  HiCheckCircle as CheckCircleIcon,
  HiXCircle as XCircleIcon,
  HiRefresh as RefreshIcon,
  HiPaperClip as PaperClipIcon,
  HiDownload as DownloadIcon,
} from "react-icons/hi";

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";

const AdminTasks = () => {
  const { user } = useAuthStore();
  const {
    tasks,
    loading,
    error,
    fetchAllTasks,
    createTask,
    updateTask,
    deleteTask,
  } = useAdminTaskStore();

  const [talents, setTalents] = useState([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    assignedTo: "",
    priority: "medium",
    dueDate: "",
  });
  const [attachmentFiles, setAttachmentFiles] = useState([]);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [previewAttachment, setPreviewAttachment] = useState(null);
  const [filters, setFilters] = useState({ status: "all" });
  const [taskStats, setTaskStats] = useState({
    total: 0,
    "not-started": 0,
    "in-progress": 0,
    completed: 0,
    overdue: 0
  });
  const { accessToken } = useAuthStore();

  // Fetch talents for assignment dropdown
  useEffect(() => {
    const fetchTalents = async () => {
      try {
        const response = await fetch(`${API_URL}/admin/talents`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          setTalents(data.talents || []);
        }
      } catch (error) {
        console.error("Failed to fetch talents:", error);
      }
    };

    if (user?.userType === "admin") {
      fetchTalents();
    }
  }, [user]);

  // Fetch task statistics
  const fetchTaskStats = async () => {
    try {
      const response = await fetch(`${API_URL}/admin/tasks/stats`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setTaskStats(data.stats || {
          total: 0,
          "not-started": 0,
          "in-progress": 0,
          completed: 0,
          overdue: 0
        });
      }
    } catch (error) {
      console.error("Failed to fetch task stats:", error);
      // Set default stats on error
      setTaskStats({
        total: 0,
        "not-started": 0,
        "in-progress": 0,
        completed: 0,
        overdue: 0
      });
    }
  };

  // Fetch tasks and stats on component mount
  useEffect(() => {
    if (user?.userType === "admin") {
      fetchAllTasks();
      fetchTaskStats();
    }
  }, [user, filters]);

  const getPriorityColor = (priority) => {
    return "bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-muted-foreground border border-border/40";
  };

  const getStatusColor = (status) => {
    return "bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-muted-foreground";
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "not-started":
        return <ClockIcon className="w-4 h-4" />;
      case "in-progress":
        return <ExclamationIcon className="w-4 h-4" />;
      case "completed":
        return <CheckCircleIcon className="w-4 h-4" />;
      case "cancelled":
        return <XCircleIcon className="w-4 h-4" />;
      default:
        return <XCircleIcon className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const isOverdue = (dueDate, status) => {
    return (
      new Date(dueDate) < new Date() &&
      status !== "completed" &&
      status !== "cancelled"
    );
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    console.log("📁 Files selected for upload:");
    files.forEach((file, index) => {
      console.log(
        `  ${index + 1}. ${file.name} (${file.type}, ${file.size} bytes)`
      );
    });
    setAttachmentFiles((prev) => [...prev, ...files]);
  };

  const removeAttachment = (index) => {
    setAttachmentFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const isImage = (fileType) => {
    return fileType?.startsWith("image/");
  };

  const isPDF = (fileType) => {
    return fileType === "application/pdf";
  };

  const canPreview = (fileType) => {
    return isImage(fileType) || isPDF(fileType);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitLoading(true);

    try {
      console.log("📝 Starting task submission...");
      console.log("📋 Form data:", formData);
      console.log("📎 Attachment files:", attachmentFiles.length);

      // Create FormData if there are attachments
      let submitData;

      if (attachmentFiles.length > 0) {
        console.log("📦 Creating FormData with attachments...");
        submitData = new FormData();
        submitData.append("title", formData.title);
        submitData.append("description", formData.description);
        submitData.append("assignedTo", formData.assignedTo);
        submitData.append("priority", formData.priority);
        submitData.append("dueDate", formData.dueDate);

        // Append all attachment files
        attachmentFiles.forEach((file, index) => {
          console.log(
            `📎 Adding file ${index + 1}: ${file.name} (${file.type})`
          );
          submitData.append("attachments", file);
        });
      } else {
        console.log("📄 No attachments, using plain object...");
        // No attachments, use plain object
        submitData = formData;
      }

      if (isEditMode && selectedTask) {
        await updateTask(selectedTask._id, submitData);
      } else {
        await createTask(submitData);
      }

      setIsCreateModalOpen(false);
      setSelectedTask(null);
      setIsEditMode(false);
      setFormData({
        title: "",
        description: "",
        assignedTo: "",
        priority: "medium",
        dueDate: "",
      });
      setAttachmentFiles([]);
    } catch (error) {
      console.error("Failed to save task:", error);
    } finally {
      setSubmitLoading(false);
    }
  };

  const handleEdit = (task) => {
    setSelectedTask(task);
    setIsEditMode(true);
    setFormData({
      title: task.title,
      description: task.description,
      assignedTo: task.assignedTo._id,
      priority: task.priority,
      dueDate: new Date(task.dueDate).toISOString().split("T")[0],
    });
    setIsCreateModalOpen(true);
  };

  const handleDelete = async (taskId) => {
    if (window.confirm("Are you sure you want to delete this task?")) {
      try {
        await deleteTask(taskId);
      } catch (error) {
        console.error("Failed to delete task:", error);
      }
    }
  };

  const handleStatusUpdate = async (taskId, newStatus) => {
    try {
      await updateTaskStatus(taskId, newStatus);
    } catch (error) {
      console.error("Failed to update task status:", error);
    }
  };

  const filteredTasks = tasks.filter((task) => {
    if (filters.status === "all") return true;
    return task.status === filters.status;
  });

  if (loading && tasks.length === 0) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex-1 space-y-4 p-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="relative z-20">
            <h1
              className="text-2xl font-bold tracking-tight"
              style={{
                color: "#F1F5F9",
                textShadow:
                  "0 2px 8px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 0, 0, 0.6)",
              }}
            >
              Task Management
            </h1>
            <p className="text-slate-300 mt-2 w-full">
              Create and manage talent tasks
            </p>
          </div>
          {/* Stats Cards (fixed-right, non-wrapping) */}
          <div className="flex items-center gap-4 flex-nowrap overflow-x-auto">
            <Card className="relative z-10 flex-shrink-0 w-28 sm:w-32 min-h-[64px] border-border/40 bg-card/90 shadow-lg hover:shadow-xl transition-all duration-200 overflow-hidden rounded-xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Tasks
                </CardTitle>
                <div className="h-8 w-8 rounded-lg bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center">
                  <ClipboardListIcon className="w-4 h-4 text-foreground/60" />
                </div>
              </CardHeader>
              <CardContent className="pb-3">
                <div className="text-xl font-bold text-white mb-0.5">
                  {taskStats.total}
                </div>
              </CardContent>
            </Card>

            <Card className="relative z-10 flex-shrink-0 w-28 sm:w-32 min-h-[64px] border-border/40 bg-card/90 shadow-lg hover:shadow-xl transition-all duration-200 overflow-hidden rounded-xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Not Started
                </CardTitle>
                <div className="h-8 w-8 rounded-lg bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center">
                  <ClockIcon className="w-4 h-4 text-foreground/60" />
                </div>
              </CardHeader>
              <CardContent className="pb-3">
                <div className="text-xl font-bold text-white mb-0.5">
                  {taskStats["not-started"]}
                </div>
              </CardContent>
            </Card>

            <Card className="relative z-10 flex-shrink-0 w-28 sm:w-32 min-h-[64px] border-border/40 bg-card/90 shadow-lg hover:shadow-xl transition-all duration-200 overflow-hidden rounded-xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  In Progress
                </CardTitle>
                <div className="h-8 w-8 rounded-lg bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center">
                  <ExclamationIcon className="w-4 h-4 text-foreground/60" />
                </div>
              </CardHeader>
              <CardContent className="pb-3">
                <div className="text-xl font-bold text-white mb-0.5">
                  {taskStats["in-progress"]}
                </div>
              </CardContent>
            </Card>

            <Card className="relative z-10 flex-shrink-0 w-28 sm:w-32 min-h-[64px] border-border/40 bg-card/90 shadow-lg hover:shadow-xl transition-all duration-200 overflow-hidden rounded-xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Completed
                </CardTitle>
                <div className="h-8 w-8 rounded-lg bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center">
                  <CheckCircleIcon className="w-4 h-4 text-foreground/60" />
                </div>
              </CardHeader>
              <CardContent className="pb-3">
                <div className="text-xl font-bold text-white mb-0.5">
                  {taskStats.completed}
                </div>
              </CardContent>
            </Card>

            <Card className="relative z-10 flex-shrink-0 w-28 sm:w-32 min-h-[64px] border-border/40 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg hover:shadow-xl transition-all duration-200 overflow-hidden rounded-xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Overdue
                </CardTitle>
                <div className="h-8 w-8 rounded-lg bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center">
                  <XCircleIcon className="w-4 h-4 text-foreground/60" />
                </div>
              </CardHeader>
              <CardContent className="pb-3">
                <div className="text-xl font-bold text-white mb-0.5">
                  {taskStats.overdue}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg">
            <div className="flex items-center justify-between">
              <span>{error}</span>
              <button
                onClick={clearError}
                className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* Filters */}
        <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex flex-wrap gap-2">
                {[
                  { key: "all", label: "All Tasks", count: taskStats.total },
                  {
                    key: "not-started",
                    label: "Not Started",
                    count: taskStats["not-started"],
                  },
                  {
                    key: "in-progress",
                    label: "In Progress",
                    count: taskStats["in-progress"],
                  },
                  {
                    key: "completed",
                    label: "Completed",
                    count: taskStats.completed,
                  },
                ].map((filterOption) => (
                  <button
                    key={filterOption.key}
                    onClick={() => setFilters({ status: filterOption.key })}
                    className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                      filters.status === filterOption.key
                        ? "bg-foreground text-background"
                        : "bg-muted text-muted-foreground hover:bg-card/80"
                    }`}
                  >
                    {filterOption.label} ({filterOption.count})
                  </button>
                ))}
              </div>

              <div className="flex items-center gap-3 mt-3 sm:mt-0">
                <button
                  onClick={() => {
                    fetchTasks();
                    fetchTaskStats();
                  }}
                  disabled={loading}
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-card/90 text-foreground transition-colors whitespace-nowrap"
                >
                  <RefreshIcon
                    className={`w-4 h-4 ${loading ? "animate-spin" : ""}`}
                  />
                  <span className="hidden sm:inline">Refresh</span>
                </button>

                <button
                  onClick={() => {
                    setIsEditMode(false);
                    setSelectedTask(null);
                    setFormData({
                      title: "",
                      description: "",
                      assignedTo: "",
                      priority: "medium",
                      dueDate: "",
                    });
                    setAttachmentFiles([]);
                    setIsCreateModalOpen(true);
                  }}
                  className="flex items-center space-x-2 bg-foreground text-background px-3 py-1.5 rounded-lg hover:bg-foreground/80 transition-all duration-200 whitespace-nowrap"
                >
                  <PlusIcon className="w-4 h-4" />
                  <span>New Task</span>
                </button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tasks Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-4">
          {filteredTasks.map((task) => (
            <Card
              key={task._id}
              className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-base font-semibold text-foreground mb-2 line-clamp-1">
                      {task.title}
                    </CardTitle>
                    <CardDescription className="text-sm line-clamp-2">
                      {task.description}
                    </CardDescription>
                  </div>
                  <div className="flex space-x-1 ml-4">
                    <button
                      onClick={() => setSelectedTask(task)}
                      className="relative z-20 text-foreground/80 hover:text-foreground transition-colors"
                      title="View Details"
                      style={{ textShadow: "0 1px 6px rgba(0,0,0,0.7)" }}
                    >
                      <EyeIcon className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleEdit(task)}
                      className="relative z-20 text-foreground/80 hover:text-foreground transition-colors"
                      title="Edit Task"
                      style={{ textShadow: "0 1px 6px rgba(0,0,0,0.7)" }}
                    >
                      <PencilIcon className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(task._id)}
                      className="relative z-20 text-foreground/80 hover:text-foreground transition-colors"
                      title="Delete Task"
                      style={{ textShadow: "0 1px 6px rgba(0,0,0,0.7)" }}
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-3">
                {/* Status and Priority */}
                <div className="flex items-center space-x-2">
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                      task.status
                    )}`}
                  >
                    {getStatusIcon(task.status)}
                    <span className="ml-1 capitalize">
                      {task.status.replace("-", " ")}
                    </span>
                  </span>
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getPriorityColor(
                      task.priority
                    )}`}
                  >
                    {task.priority}
                  </span>
                </div>

                {/* Assignee */}
                <div className="flex items-center space-x-2">
                  <UserIcon className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm text-white">
                    {task.assignedTo?.name} ({task.assignedTo?.talentId})
                  </span>
                </div>

                {/* Deadline */}
                <div className="flex items-center space-x-2">
                  <ClockIcon className="w-4 h-4 text-muted-foreground" />
                  <span
                    className={`text-sm ${
                      isOverdue(task.dueDate, task.status)
                        ? "text-red-600 dark:text-red-400"
                        : "text-foreground"
                    }`}
                  >
                    {formatDate(task.dueDate)}
                    {isOverdue(task.dueDate, task.status) && " (Overdue)"}
                  </span>
                </div>

                {/* Attachments indicator */}
                {task.attachments && task.attachments.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <PaperClipIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm text-blue-600 dark:text-blue-400">
                      {task.attachments.length} attachment
                      {task.attachments.length > 1 ? "s" : ""}
                    </span>
                  </div>
                )}

                {/* Quick Status Update */}
                <div className="pt-2">
                  <select
                    value={task.status}
                    onChange={(e) =>
                      handleStatusUpdate(task._id, e.target.value)
                    }
                    className="w-full px-3 py-1 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-input rounded text-foreground text-sm focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500"
                  >
                    <option value="not-started">Not Started</option>
                    <option value="in-progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredTasks.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="mx-auto w-24 h-24 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 rounded-full flex items-center justify-center mb-4">
              <ClipboardListIcon className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="mt-2 text-base font-semibold text-white">
              No tasks found
            </h3>
            <p className="mt-1 text-sm text-muted-foreground max-w-sm mx-auto">
              {filters.status === "all"
                ? "Get started by creating a new task."
                : `No tasks with status "${filters.status.replace("-", " ")}".`}
            </p>
            {filters.status === "all" && (
              <div className="mt-6">
                <button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-200 shadow-sm text-sm font-medium rounded-md text-gray-900 bg-white hover:bg-gray-100"
                >
                  <PlusIcon className="-ml-1 mr-2 h-4 w-4" />
                  New Task
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Create/Edit Task Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <Card className="max-w-2xl w-full max-h-[90vh] overflow-y-auto border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
            <CardHeader className="border-b border-border">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl font-bold text-white">
                  {isEditMode ? "Edit Task" : "Create New Task"}
                </CardTitle>
                <button
                  onClick={() => {
                    setIsCreateModalOpen(false);
                    setSelectedTask(null);
                    setIsEditMode(false);
                    setAttachmentFiles([]);
                  }}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <XCircleIcon className="w-4 h-4" />
                </button>
              </div>
            </CardHeader>

            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                {/* Task Title */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Task Title *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-1.5 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-input rounded-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500"
                    placeholder="Enter task title"
                  />
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Description *
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    required
                    rows={4}
                    className="w-full px-3 py-1.5 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-input rounded-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500"
                    placeholder="Describe the task in detail..."
                  />
                </div>

                {/* Priority and Assignee */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Priority *
                    </label>
                    <select
                      name="priority"
                      value={formData.priority}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-1.5 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-input rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Assign To *
                    </label>
                    <select
                      name="assignedTo"
                      value={formData.assignedTo}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-1.5 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-input rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500"
                    >
                      <option value="">Select a talent</option>
                      {talents.map((talent) => (
                        <option key={talent._id} value={talent._id}>
                          {talent.name} ({talent.talentId})
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Due Date */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Due Date *
                  </label>
                  <input
                    type="date"
                    name="dueDate"
                    value={formData.dueDate}
                    onChange={handleInputChange}
                    required
                    min={new Date().toISOString().split("T")[0]}
                    className="w-full px-3 py-1.5 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-input rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500"
                  />
                </div>

                {/* Attachments */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Attachments (Optional)
                  </label>
                  <div className="space-y-3">
                    <input
                      type="file"
                      onChange={handleFileChange}
                      multiple
                      className="w-full px-3 py-1.5 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-input rounded-lg text-foreground file:mr-4 file:py-2 file:px-4 file:rounded-full file:text-sm file:font-semibold file:bg-white file:text-gray-900 hover:file:bg-gray-100 file:border file:border-gray-200"
                    />

                    {/* File Preview List */}
                    {attachmentFiles.length > 0 && (
                      <div className="space-y-2">
                        {attachmentFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-border rounded-lg px-4 py-2"
                          >
                            <div className="flex items-center space-x-2">
                              <PaperClipIcon className="w-4 h-4 text-muted-foreground" />
                              <span className="text-sm text-white">
                                {file.name}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                ({(file.size / 1024).toFixed(1)} KB)
                              </span>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeAttachment(index)}
                              className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                            >
                              <XCircleIcon className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Existing Attachments (for edit mode) */}
                    {isEditMode && selectedTask?.attachments?.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-xs text-muted-foreground">
                          Existing Attachments:
                        </p>
                        {selectedTask.attachments.map((attachment) => (
                          <div
                            key={attachment._id}
                            className="flex items-center justify-between bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-border rounded-lg px-4 py-2"
                          >
                            <div className="flex items-center space-x-2">
                              <PaperClipIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                              <a
                                href={attachment.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                              >
                                {attachment.filename}
                              </a>
                              <span className="text-xs text-muted-foreground">
                                ({(attachment.fileSize / 1024).toFixed(1)} KB)
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-4 border-t border-border">
                  <button
                    type="button"
                    onClick={() => {
                      setIsCreateModalOpen(false);
                      setSelectedTask(null);
                      setIsEditMode(false);
                      setAttachmentFiles([]);
                    }}
                    className="px-3 py-1.5 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-foreground rounded-lg hover:bg-card/90 hover:backdrop-blur hover:supports-[backdrop-filter]:bg-card/70 transition"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={submitLoading}
                    className="px-3 py-1.5 bg-white text-gray-900 border border-gray-200 rounded-lg hover:bg-gray-100 transition disabled:opacity-50"
                  >
                    {submitLoading
                      ? "Saving..."
                      : isEditMode
                      ? "Update Task"
                      : "Create Task"}
                  </button>
                </div>
              </CardContent>
            </form>
          </Card>
        </div>
      )}

      {/* Task Detail Modal */}
      {selectedTask && !isCreateModalOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <Card className="max-w-2xl w-full max-h-[90vh] overflow-y-auto border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
            <CardHeader className="border-b border-border">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl font-bold text-white">
                  Task Details
                </CardTitle>
                <button
                  onClick={() => setSelectedTask(null)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <XCircleIcon className="w-4 h-4" />
                </button>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {selectedTask.title}
                </h3>
                <p className="text-slate-300">{selectedTask.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Status
                  </label>
                  <span
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                      selectedTask.status
                    )}`}
                  >
                    {getStatusIcon(selectedTask.status)}
                    <span className="ml-1 capitalize">
                      {selectedTask.status.replace("-", " ")}
                    </span>
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Priority
                  </label>
                  <span
                    className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full border ${getPriorityColor(
                      selectedTask.priority
                    )}`}
                  >
                    {selectedTask.priority}
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Assigned To
                  </label>
                  <p className="text-white">
                    {selectedTask.assignedTo?.name} (
                    {selectedTask.assignedTo?.talentId})
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Assigned By
                  </label>
                  <p className="text-white">
                    {selectedTask.assignedBy?.name || "Admin"}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Due Date
                  </label>
                  <p
                    className={`${
                      isOverdue(selectedTask.dueDate, selectedTask.status)
                        ? "text-red-600 dark:text-red-400"
                        : "text-foreground"
                    }`}
                  >
                    {formatDate(selectedTask.dueDate)}
                    {isOverdue(selectedTask.dueDate, selectedTask.status) &&
                      " (Overdue)"}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Created
                  </label>
                  <p className="text-white">
                    {formatDate(selectedTask.createdAt)}
                  </p>
                </div>
              </div>

              {/* Attachments Section */}
              {selectedTask.attachments &&
                selectedTask.attachments.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-muted-foreground mb-3">
                      Attachments ({selectedTask.attachments.length})
                    </label>
                    <div className="space-y-2">
                      {selectedTask.attachments.map((attachment) => (
                        <div
                          key={attachment._id}
                          className="flex items-center justify-between bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-border rounded-lg px-4 py-3 hover:bg-card/90 hover:backdrop-blur hover:supports-[backdrop-filter]:bg-card/70 transition-colors"
                        >
                          <div className="flex items-center space-x-3 flex-1">
                            <PaperClipIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                            <div className="flex-1">
                              <p className="text-sm text-foreground font-medium">
                                {attachment.filename}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {attachment.fileType} •{" "}
                                {(attachment.fileSize / 1024).toFixed(1)} KB
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {canPreview(attachment.fileType) && (
                              <button
                                onClick={() => setPreviewAttachment(attachment)}
                                className="flex items-center space-x-1 px-3 py-1 bg-white text-gray-900 border border-gray-200 rounded hover:bg-gray-100 transition-colors"
                              >
                                <EyeIcon className="w-4 h-4" />
                                <span className="text-sm">View</span>
                              </button>
                            )}
                            <a
                              href={attachment.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-1 px-3 py-1 bg-white text-gray-900 border border-gray-200 rounded hover:bg-gray-100 transition-colors"
                            >
                              <DownloadIcon className="w-4 h-4" />
                              <span className="text-sm">Download</span>
                            </a>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

              <div className="flex justify-end space-x-3 pt-4 border-t border-border">
                <button
                  onClick={() => handleEdit(selectedTask)}
                  className="px-3 py-1.5 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition"
                >
                  Edit Task
                </button>
                <button
                  onClick={() => {
                    setSelectedTask(null);
                    handleDelete(selectedTask._id);
                  }}
                  className="px-3 py-1.5 bg-red-600 text-white rounded-lg hover:bg-red-700 transition"
                >
                  Delete Task
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Attachment Preview Modal */}
      {previewAttachment && (
        <div className="fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center z-[60] p-4">
          <Card className="max-w-5xl w-full max-h-[90vh] overflow-hidden flex flex-col border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
            {/* Header */}
            <CardHeader className="border-b border-border flex-row items-center justify-between space-y-0">
              <div className="flex items-center space-x-3">
                <PaperClipIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <div>
                  <CardTitle className="text-base font-semibold text-white">
                    {previewAttachment.filename}
                  </CardTitle>
                  <CardDescription className="text-xs">
                    {previewAttachment.fileType} •{" "}
                    {(previewAttachment.fileSize / 1024).toFixed(1)} KB
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <a
                  href={previewAttachment.url}
                  download
                  className="flex items-center space-x-1 px-3 py-2 bg-white text-gray-900 border border-gray-200 rounded hover:bg-gray-100 transition-colors"
                >
                  <DownloadIcon className="w-4 h-4" />
                  <span className="text-sm">Download</span>
                </a>
                <button
                  onClick={() => setPreviewAttachment(null)}
                  className="text-muted-foreground hover:text-foreground p-2"
                >
                  <XCircleIcon className="w-4 h-4" />
                </button>
              </div>
            </CardHeader>

            {/* Preview Content */}
            <CardContent className="flex-1 overflow-auto bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center p-4">
              {isImage(previewAttachment.fileType) ? (
                <img
                  src={previewAttachment.url}
                  alt={previewAttachment.filename}
                  className="max-w-full max-h-full object-contain"
                />
              ) : isPDF(previewAttachment.fileType) ? (
                <iframe
                  src={previewAttachment.url}
                  className="w-full h-full min-h-[600px]"
                  title={previewAttachment.filename}
                />
              ) : (
                <div className="text-center">
                  <PaperClipIcon className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-slate-300">
                    Preview not available for this file type
                  </p>
                  <a
                    href={previewAttachment.url}
                    download
                    className="mt-4 inline-flex items-center space-x-2 px-3 py-1.5 bg-white text-gray-900 border border-gray-200 rounded hover:bg-gray-100 transition-colors"
                  >
                    <DownloadIcon className="w-4 h-4" />
                    <span>Download to view</span>
                  </a>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </AdminLayout>
  );
};

export default AdminTasks;
