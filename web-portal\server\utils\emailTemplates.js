/**
 * Email Templates for Notifications
 * HTML email templates with consistent styling
 */

import { sanitizeText } from "./securityUtils.js";

const baseStyle = `
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
`;

const containerStyle = `
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f9fafb;
`;

const cardStyle = `
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const buttonStyle = `
  display: inline-block;
  padding: 12px 24px;
  background-color: #2563eb;
  color: white !important;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 600;
  margin-top: 20px;
`;

const footerStyle = `
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  color: #6b7280;
  font-size: 14px;
`;

/**
 * Generate email HTML template
 * @param {Object} options - Email options
 * @param {string} options.title - Email title
 * @param {string} options.message - Main message
 * @param {string} options.actionUrl - Action button URL (optional)
 * @param {string} options.actionText - Action button text (optional)
 * @param {string} options.priority - Notification priority
 * @returns {string} HTML email template
 */
export const generateEmailTemplate = ({
  title,
  message,
  actionUrl,
  actionText,
  priority,
}) => {
  const priorityColors = {
    low: "#10b981",
    medium: "#3b82f6",
    high: "#f59e0b",
    urgent: "#ef4444",
  };

  const priorityColor = priorityColors[priority] || priorityColors.medium;

  // SECURITY: Sanitize all user-provided content
  const safeTitle = sanitizeText(title);
  const safeMessage = sanitizeText(message);
  const safeActionText = actionText ? sanitizeText(actionText) : "";
  // actionUrl validated separately (must be relative path)

  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${safeTitle}</title>
</head>
<body style="${baseStyle}">
  <div style="${containerStyle}">
    <div style="${cardStyle}">
      <!-- Priority Badge -->
      <div style="margin-bottom: 20px;">
        <span style="
          display: inline-block;
          padding: 4px 12px;
          background-color: ${priorityColor};
          color: white;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
        ">
          ${priority}
        </span>
      </div>

      <!-- Title -->
      <h2 style="
        margin: 0 0 20px 0;
        color: #111827;
        font-size: 24px;
        font-weight: 700;
      ">
        ${safeTitle}
      </h2>

      <!-- Message -->
      <p style="
        margin: 0 0 20px 0;
        color: #4b5563;
        font-size: 16px;
        line-height: 1.6;
      ">
        ${safeMessage}
      </p>

      <!-- Action Button -->
      ${
        actionUrl && actionText
          ? `
      <div style="margin-top: 30px;">
        <a href="${actionUrl}" style="${buttonStyle}">
          ${safeActionText}
        </a>
      </div>
      `
          : ""
      }

      <!-- Footer -->
      <div style="${footerStyle}">
        <p style="margin: 0 0 10px 0;">
          This is an automated notification from Talent Portal.
        </p>
        <p style="margin: 0;">
          You can manage your notification preferences in your account settings.
        </p>
      </div>
    </div>

    <!-- Branding Footer -->
    <div style="text-align: center; margin-top: 20px; color: #9ca3af; font-size: 12px;">
      <p style="margin: 0;">
        © ${new Date().getFullYear()} Talent Portal. All rights reserved.
      </p>
    </div>
  </div>
</body>
</html>
  `;
};

/**
 * Generate task assigned email
 */
export const taskAssignedEmail = ({ taskTitle, assignedBy, taskUrl }) => {
  return generateEmailTemplate({
    title: "📋 New Task Assigned",
    message: `${assignedBy} has assigned you a new task: <strong>"${taskTitle}"</strong>. Click the button below to view the details and get started.`,
    actionUrl: taskUrl,
    actionText: "View Task",
    priority: "medium",
  });
};

/**
 * Generate badge earned email
 */
export const badgeEarnedEmail = ({ badgeName, badgeDescription, badgeUrl }) => {
  return generateEmailTemplate({
    title: "🎉 Congratulations! Badge Earned",
    message: `You've earned the <strong>"${badgeName}"</strong> badge! ${
      badgeDescription || "Keep up the great work!"
    }`,
    actionUrl: badgeUrl,
    actionText: "View Your Badges",
    priority: "high",
  });
};

/**
 * Generate announcement posted email
 */
export const announcementPostedEmail = ({
  announcementTitle,
  announcementPreview,
  announcementUrl,
}) => {
  return generateEmailTemplate({
    title: "📢 New Announcement",
    message: `<strong>${announcementTitle}</strong><br><br>${announcementPreview}`,
    actionUrl: announcementUrl,
    actionText: "Read Full Announcement",
    priority: "medium",
  });
};

/**
 * Generate resource added email
 */
export const resourceAddedEmail = ({
  resourceTitle,
  resourceType,
  resourceUrl,
}) => {
  return generateEmailTemplate({
    title: "📚 New Resource Available",
    message: `A new ${resourceType} has been added: <strong>"${resourceTitle}"</strong>. Check it out to enhance your skills!`,
    actionUrl: resourceUrl,
    actionText: "View Resource",
    priority: "low",
  });
};

/**
 * Generate task due soon email
 */
export const taskDueSoonEmail = ({ taskTitle, dueDate, taskUrl }) => {
  return generateEmailTemplate({
    title: "⏰ Task Due Soon",
    message: `Your task <strong>"${taskTitle}"</strong> is due on <strong>${dueDate}</strong>. Make sure to complete it on time!`,
    actionUrl: taskUrl,
    actionText: "View Task",
    priority: "high",
  });
};

/**
 * Generate task overdue email
 */
export const taskOverdueEmail = ({ taskTitle, taskUrl }) => {
  return generateEmailTemplate({
    title: "🚨 Task Overdue",
    message: `Your task <strong>"${taskTitle}"</strong> is now overdue. Please complete it as soon as possible.`,
    actionUrl: taskUrl,
    actionText: "View Task",
    priority: "urgent",
  });
};

/**
 * Generate admin message email
 */
export const adminMessageEmail = ({ message, actionUrl, actionText }) => {
  return generateEmailTemplate({
    title: "💬 Message from Admin",
    message: message,
    actionUrl: actionUrl,
    actionText: actionText || "View Details",
    priority: "high",
  });
};

/**
 * Generate report reminder email
 */
export const reportReminderEmail = ({ reportUrl }) => {
  return generateEmailTemplate({
    title: "📝 Daily Report Reminder",
    message:
      "Don't forget to submit your daily report! Keep your streak going and earn XP points.",
    actionUrl: reportUrl,
    actionText: "Submit Report",
    priority: "medium",
  });
};

/**
 * Leave Request Submitted Email (to Admin)
 */
export function leaveRequestSubmittedEmail(
  internName,
  startDate,
  endDate,
  totalDays,
  reasonCategory,
  actionUrl
) {
  const priority = "medium";
  const priorityColor = "#f59e0b";
  const actionText = "Review Request";

  return generateEmailTemplate({
    title: "📅 New Unavailability Request",
    message: `<strong>${internName}</strong> has submitted an unavailability request.`,
    priority,
    priorityColor,
    actionUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #f3f4f6; border-radius: 6px;">
        <p style="margin: 5px 0;"><strong>Period:</strong> ${startDate} to ${endDate}</p>
        <p style="margin: 5px 0;"><strong>Duration:</strong> ${totalDays} day(s)</p>
        <p style="margin: 5px 0;"><strong>Reason:</strong> ${reasonCategory.replace(
          "_",
          " "
        )}</p>
      </div>
    `,
  });
}

/**
 * Leave Request Urgent Email (to Admin)
 */
export function leaveRequestUrgentEmail(
  internName,
  startDate,
  endDate,
  totalDays,
  reasonText,
  actionUrl
) {
  const priority = "urgent";
  const priorityColor = "#dc2626";
  const actionText = "Review Urgent Request";

  return generateEmailTemplate({
    title: "🚨 URGENT: Unavailability Request",
    message: `<strong>${internName}</strong> has submitted an <strong>URGENT</strong> unavailability request.`,
    priority,
    priorityColor,
    actionUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #fee2e2; border-radius: 6px; border-left: 4px solid #dc2626;">
        <p style="margin: 5px 0;"><strong>Period:</strong> ${startDate} to ${endDate}</p>
        <p style="margin: 5px 0;"><strong>Duration:</strong> ${totalDays} day(s)</p>
        <p style="margin: 10px 0 5px 0;"><strong>Reason:</strong></p>
        <p style="margin: 5px 0; font-style: italic;">"${reasonText}"</p>
        <p style="margin-top: 15px; color: #dc2626; font-weight: 600;">⚠️ Immediate attention required</p>
      </div>
    `,
  });
}

/**
 * Leave Request Acknowledged Email (to Intern)
 */
export function leaveRequestAcknowledgedEmail(
  internName,
  startDate,
  endDate,
  actionUrl
) {
  const priority = "medium";
  const priorityColor = "#10b981";
  const actionText = "View Details";

  return generateEmailTemplate({
    title: "✅ Unavailability Request Acknowledged",
    message: `Hi <strong>${internName}</strong>, your unavailability request has been acknowledged.`,
    priority,
    priorityColor,
    actionUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #d1fae5; border-radius: 6px; border-left: 4px solid #10b981;">
        <p style="margin: 5px 0;">Your time off from <strong>${startDate}</strong> to <strong>${endDate}</strong> has been approved.</p>
        <p style="margin: 15px 0 5px 0; color: #065f46;">Enjoy your time off! We'll see you when you return.</p>
      </div>
    `,
  });
}

/**
 * Leave Request Denied Email (to Intern)
 */
export function leaveRequestDeniedEmail(
  internName,
  startDate,
  endDate,
  denialReason,
  actionUrl
) {
  const priority = "high";
  const priorityColor = "#dc2626";
  const actionText = "View Details";

  return generateEmailTemplate({
    title: "❌ Unavailability Request Denied",
    message: `Hi <strong>${internName}</strong>, unfortunately your unavailability request has been denied.`,
    priority,
    priorityColor,
    actionUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #fee2e2; border-radius: 6px; border-left: 4px solid #dc2626;">
        <p style="margin: 5px 0;"><strong>Requested Period:</strong> ${startDate} to ${endDate}</p>
        <p style="margin: 15px 0 5px 0;"><strong>Reason for Denial:</strong></p>
        <p style="margin: 5px 0; font-style: italic; color: #7f1d1d;">"${denialReason}"</p>
        <p style="margin-top: 15px; color: #991b1b;">If you have questions, please contact your manager or admin.</p>
      </div>
    `,
  });
}

/**
 * Leave Request Cancelled Email (to Admin)
 */
export function leaveRequestCancelledEmail(
  internName,
  startDate,
  endDate,
  cancelledBy,
  actionUrl
) {
  const priority = "low";
  const priorityColor = "#6b7280";
  const actionText = "View Details";

  return generateEmailTemplate({
    title: "🔔 Unavailability Request Cancelled",
    message: `<strong>${internName}'s</strong> unavailability request has been cancelled.`,
    priority,
    priorityColor,
    actionUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #f3f4f6; border-radius: 6px;">
        <p style="margin: 5px 0;"><strong>Period:</strong> ${startDate} to ${endDate}</p>
        <p style="margin: 5px 0;"><strong>Cancelled By:</strong> ${cancelledBy}</p>
        <p style="margin: 15px 0 5px 0; color: #4b5563;">${internName} will now be available during this period.</p>
      </div>
    `,
  });
}

/**
 * Capacity Alert Email (to Admin)
 */
export function capacityAlertEmail(
  date,
  unavailableCount,
  totalInterns,
  capacityPercentage,
  actionUrl
) {
  const priority = "high";
  const priorityColor = "#f59e0b";
  const actionText = "View Calendar";

  return generateEmailTemplate({
    title: "⚠️ Team Capacity Alert",
    message: `<strong>${capacityPercentage}%</strong> of your team will be unavailable on <strong>${date}</strong>.`,
    priority,
    priorityColor,
    actionUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #fef3c7; border-radius: 6px; border-left: 4px solid #f59e0b;">
        <p style="margin: 5px 0;"><strong>Date:</strong> ${date}</p>
        <p style="margin: 5px 0;"><strong>Unavailable:</strong> ${unavailableCount} out of ${totalInterns} interns</p>
        <p style="margin: 5px 0;"><strong>Capacity:</strong> ${capacityPercentage}%</p>
        <p style="margin-top: 15px; color: #92400e;">Consider reviewing upcoming requests to ensure adequate coverage.</p>
      </div>
    `,
  });
}

/**
 * Admin Created Email - Welcome email for new admin accounts
 */
export function adminCreatedEmail(adminName, tempPassword, roleType, loginUrl) {
  const priority = "urgent";
  const priorityColor = "#2563eb";
  const actionText = "Login to Admin Panel";

  const roleDescriptions = {
    super_admin: "Super Administrator - Full system access including user management",
    full_admin: "Full Administrator - Complete admin access except user management", 
    hiring_manager: "Hiring Manager - Manages recruitment and hiring processes",
    employee_manager: "Employee Manager - Manages talent operations and compliance",
    team_lead: "Team Lead - Limited admin access for team coordination"
  };

  return generateEmailTemplate({
    title: "🔐 Admin Account Created",
    message: `Hi <strong>${adminName}</strong>, an admin account has been created for you in the ModelSuite Talent Portal.`,
    priority,
    priorityColor,
    actionUrl: loginUrl || "https://portal.modelsuite.ai/admin",
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #eff6ff; border-radius: 6px; border-left: 4px solid #2563eb;">
        <p style="margin: 5px 0;"><strong>👤 Your Role:</strong> ${roleDescriptions[roleType] || roleType}</p>
        <p style="margin: 15px 0 5px 0;"><strong>🔑 Login Credentials:</strong></p>
        <p style="margin: 5px 0;">Email: <code style="background-color: #dbeafe; padding: 2px 6px; border-radius: 3px; font-family: monospace;">${process.env.TEMP_EMAIL_PLACEHOLDER || '[Your Email]'}</code></p>
        <p style="margin: 5px 0;">Temporary Password: <code style="background-color: #dbeafe; padding: 4px 8px; border-radius: 3px; font-family: monospace; font-weight: 600; color: #1e40af;">${tempPassword}</code></p>
      </div>
      <div style="margin-top: 15px; padding: 15px; background-color: #fef3c7; border-radius: 6px; border-left: 4px solid #f59e0b;">
        <p style="margin: 5px 0; color: #92400e;"><strong>🛡️ Security Important:</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; font-size: 14px; color: #92400e;">
          <li>You must change this password on first login</li>
          <li>Use a strong, unique password</li>
          <li>Keep your credentials secure</li>
          <li>Contact support if you have any issues</li>
        </ul>
      </div>
      <div style="margin-top: 15px; padding: 15px; background-color: #f9fafb; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px;"><strong>🚀 Getting Started:</strong></p>
        <ol style="margin: 10px 0; padding-left: 20px; font-size: 14px;">
          <li>Click the login button above</li>
          <li>Enter your credentials</li>
          <li>Set your new password</li>
          <li>Explore your admin dashboard</li>
        </ol>
      </div>
      <div style="margin-top: 15px; padding: 10px; background-color: #d1fae5; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px; color: #065f46;">💬 <strong>Need Help?</strong> Contact the admin team at <a href="mailto:<EMAIL>" style="color: #065f46;"><EMAIL></a></p>
      </div>
    `,
  });
}

export default {
  generateEmailTemplate,
  taskAssignedEmail,
  badgeEarnedEmail,
  announcementPostedEmail,
  resourceAddedEmail,
  taskDueSoonEmail,
  taskOverdueEmail,
  adminMessageEmail,
  reportReminderEmail,
  leaveRequestSubmittedEmail,
  leaveRequestUrgentEmail,
  leaveRequestAcknowledgedEmail,
  leaveRequestDeniedEmail,
  leaveRequestCancelledEmail,
  capacityAlertEmail,
  adminCreatedEmail,
};

/**
 * Interview Scheduling Email Templates (Section 3)
 */

/**
 * Application Shortlisted Email - Sent to candidate with interview scheduling link
 * @param {string} candidateName - Candidate's full name
 * @param {string} jobTitle - Job position title
 * @param {string} calendlyUrl - Calendly scheduling link (e.g., https://calendly.com/modelsuite/interview)
 */
export function applicationShortlistedEmail(
  candidateName,
  jobTitle,
  calendlyUrl
) {
  const priority = "high";
  const priorityColor = "#10b981";
  const actionText = "Schedule Your Interview";

  return generateEmailTemplate({
    title: "🎉 Congratulations! You've Been Shortlisted",
    message: `Hi <strong>${candidateName}</strong>, we're excited to inform you that you've been shortlisted for the <strong>${jobTitle}</strong> position!`,
    priority,
    priorityColor,
    actionUrl: calendlyUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #d1fae5; border-radius: 6px; border-left: 4px solid #10b981;">
        <p style="margin: 5px 0;"><strong>Next Step:</strong> Schedule your interview via Calendly</p>
        <p style="margin: 15px 0 5px 0;">Click the button above to view our available time slots and book your interview instantly. Our scheduling system will automatically send you a meeting link.</p>
        <p style="margin: 15px 0 5px 0; color: #065f46;"><strong>Interview Format:</strong> 30-minute virtual interview via Zoom/Google Meet</p>
        <p style="margin: 5px 0; color: #065f46;"><strong>Timing:</strong> Available slots between 4:00 PM - 7:00 PM IST, Monday-Friday</p>
      </div>
      <div style="margin-top: 15px; padding: 10px; background-color: #fef3c7; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px; color: #92400e;">⚠️ <strong>Note:</strong> You can reschedule or cancel directly through the Calendly confirmation email if needed.</p>
      </div>
    `,
  });
}

/**
 * Interview Scheduled Confirmation Email - Sent after candidate books slot
 */
export function interviewScheduledEmail(
  candidateName,
  jobTitle,
  interviewDate,
  interviewTime,
  meetingLink,
  meetingPassword
) {
  const priority = "high";
  const priorityColor = "#2563eb";
  const actionText = "Join Interview";

  return generateEmailTemplate({
    title: "✅ Interview Scheduled Successfully",
    message: `Hi <strong>${candidateName}</strong>, your interview for the <strong>${jobTitle}</strong> position has been confirmed!`,
    priority,
    priorityColor,
    actionUrl: meetingLink,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #eff6ff; border-radius: 6px; border-left: 4px solid #2563eb;">
        <p style="margin: 5px 0;"><strong>📅 Date:</strong> ${interviewDate}</p>
        <p style="margin: 5px 0;"><strong>🕐 Time:</strong> ${interviewTime}</p>
        <p style="margin: 5px 0;"><strong>👥 Interviewer:</strong> ModelSuite Team</p>
        <p style="margin: 5px 0;"><strong>📍 Location:</strong> Virtual (Google Meet)</p>
        ${
          meetingPassword
            ? `<p style="margin: 5px 0;"><strong>🔑 Meeting Password:</strong> <code style="background-color: #dbeafe; padding: 2px 6px; border-radius: 3px;">${meetingPassword}</code></p>`
            : ""
        }
      </div>
      <div style="margin-top: 15px; padding: 15px; background-color: #f9fafb; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px;"><strong>📝 Interview Tips:</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; font-size: 14px;">
          <li>Test your internet connection and video/audio beforehand</li>
          <li>Join 5 minutes early to ensure a smooth start</li>
          <li>Have your resume and portfolio ready to discuss</li>
          <li>Prepare questions about the role and company</li>
        </ul>
      </div>
      <div style="margin-top: 15px; padding: 10px; background-color: #fef3c7; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px; color: #92400e;">⚠️ Need to reschedule? You can do so once (minimum 2 hours notice). Contact us at <a href="mailto:<EMAIL>" style="color: #92400e;"><EMAIL></a></p>
      </div>
    `,
  });
}

/**
 * Interview Reminder Email - Sent 6 hours before interview
 */
export function interviewReminderEmail(
  candidateName,
  jobTitle,
  interviewDate,
  interviewTime,
  meetingLink,
  meetingPassword
) {
  const priority = "urgent";
  const priorityColor = "#f59e0b";
  const actionText = "Join Interview";

  return generateEmailTemplate({
    title: "⏰ Interview Reminder - Today!",
    message: `Hi <strong>${candidateName}</strong>, this is a friendly reminder about your interview for the <strong>${jobTitle}</strong> position today!`,
    priority,
    priorityColor,
    actionUrl: meetingLink,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #fef3c7; border-radius: 6px; border-left: 4px solid #f59e0b;">
        <p style="margin: 5px 0;"><strong>📅 Date:</strong> ${interviewDate}</p>
        <p style="margin: 5px 0;"><strong>🕐 Time:</strong> ${interviewTime}</p>
        <p style="margin: 5px 0;"><strong>📍 Meeting Link:</strong> Click the button above</p>
        ${
          meetingPassword
            ? `<p style="margin: 5px 0;"><strong>🔑 Password:</strong> <code style="background-color: #fef3c7; padding: 2px 6px; border-radius: 3px; font-weight: 600;">${meetingPassword}</code></p>`
            : ""
        }
      </div>
      <div style="margin-top: 15px; padding: 15px; background-color: #d1fae5; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px; color: #065f46;"><strong>✅ Quick Checklist:</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; font-size: 14px; color: #065f46;">
          <li>✓ Internet connection tested</li>
          <li>✓ Camera and microphone working</li>
          <li>✓ Quiet space prepared</li>
          <li>✓ Resume and portfolio ready</li>
        </ul>
      </div>
      <div style="margin-top: 15px; padding: 10px; background-color: #eff6ff; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px; color: #1e40af;">💡 <strong>Tip:</strong> Join the meeting 5 minutes early to ensure everything is set up properly!</p>
      </div>
    `,
  });
}

/**
 * Interview Rescheduled Email - Sent when interview is rescheduled
 */
export function interviewRescheduledEmail(
  candidateName,
  jobTitle,
  oldDate,
  oldTime,
  newDate,
  newTime,
  meetingLink
) {
  const priority = "high";
  const priorityColor = "#f59e0b";
  const actionText = "View New Schedule";

  return generateEmailTemplate({
    title: "📅 Interview Rescheduled",
    message: `Hi <strong>${candidateName}</strong>, your interview for the <strong>${jobTitle}</strong> position has been rescheduled.`,
    priority,
    priorityColor,
    actionUrl: meetingLink,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #fee2e2; border-radius: 6px; border-left: 4px solid #dc2626;">
        <p style="margin: 5px 0;"><strong>❌ Previous Schedule:</strong></p>
        <p style="margin: 5px 0; text-decoration: line-through; color: #991b1b;">${oldDate} at ${oldTime}</p>
      </div>
      <div style="margin-top: 15px; padding: 15px; background-color: #d1fae5; border-radius: 6px; border-left: 4px solid #10b981;">
        <p style="margin: 5px 0;"><strong>✅ New Schedule:</strong></p>
        <p style="margin: 5px 0; font-weight: 600; color: #065f46;">${newDate} at ${newTime}</p>
      </div>
      <div style="margin-top: 15px; padding: 10px; background-color: #fef3c7; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px; color: #92400e;">⚠️ <strong>Note:</strong> This was your one-time reschedule. Further changes will require contacting us directly at <a href="mailto:<EMAIL>" style="color: #92400e;"><EMAIL></a></p>
      </div>
    `,
  });
}

/**
 * Application Approved Email - Sent after successful interview
 */
export function applicationApprovedEmail(
  candidateName,
  jobTitle,
  onboardingUrl
) {
  const priority = "urgent";
  const priorityColor = "#10b981";
  const actionText = "Start Onboarding";

  return generateEmailTemplate({
    title: "🎉 Congratulations! You're Hired!",
    message: `Hi <strong>${candidateName}</strong>, we're thrilled to offer you the <strong>${jobTitle}</strong> position at ModelSuite!`,
    priority,
    priorityColor,
    actionUrl: onboardingUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 20px; background-color: #d1fae5; border-radius: 6px; border-left: 4px solid #10b981;">
        <p style="margin: 5px 0; font-size: 16px; font-weight: 600; color: #065f46;">Welcome to the ModelSuite Team! 🚀</p>
        <p style="margin: 15px 0 5px 0;">We were impressed by your skills and enthusiasm during the interview. We're excited to have you join our team!</p>
      </div>
      <div style="margin-top: 15px; padding: 15px; background-color: #eff6ff; border-radius: 6px;">
        <p style="margin: 5px 0;"><strong>📝 Next Steps:</strong></p>
        <ol style="margin: 10px 0; padding-left: 20px; font-size: 14px;">
          <li>Click the button above to access your onboarding portal</li>
          <li>Complete all required documentation</li>
          <li>Set up your work accounts and tools</li>
          <li>Review company policies and guidelines</li>
        </ol>
      </div>
      <div style="margin-top: 15px; padding: 15px; background-color: #f9fafb; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px;"><strong>📞 Need Help?</strong></p>
        <p style="margin: 10px 0 5px 0; font-size: 14px;">Contact our HR team at <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a></p>
      </div>
    `,
  });
}

/**
 * Application Rejected Email (Pre-Interview) - Professional rejection before interview
 */
export function applicationRejectedPreInterviewEmail(candidateName, jobTitle) {
  const priority = "medium";
  const priorityColor = "#6b7280";
  const actionText = "View Other Opportunities";
  const careersUrl = "https://modelsuite.ai/careers";

  return generateEmailTemplate({
    title: "Application Status Update",
    message: `Hi <strong>${candidateName}</strong>, thank you for your interest in the <strong>${jobTitle}</strong> position at ModelSuite.`,
    priority,
    priorityColor,
    actionUrl: careersUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #f9fafb; border-radius: 6px;">
        <p style="margin: 5px 0;">After careful consideration, we've decided to move forward with other candidates whose qualifications more closely match our current needs for this specific role.</p>
        <p style="margin: 15px 0 5px 0;">We appreciate the time and effort you invested in your application. Your skills and experience are impressive, and we encourage you to apply for future openings that align with your expertise.</p>
      </div>
      <div style="margin-top: 15px; padding: 15px; background-color: #eff6ff; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px;"><strong>💡 We're always looking for great talent!</strong></p>
        <p style="margin: 10px 0 5px 0; font-size: 14px;">Check our careers page regularly for new opportunities that may be a better fit.</p>
      </div>
      <div style="margin-top: 15px; padding: 10px; background-color: #fef3c7; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 13px; color: #92400e;">📅 You can reapply for this or other positions after 3 months.</p>
      </div>
    `,
  });
}

/**
 * Application Rejected Email (Post-Interview) - Professional rejection after interview
 */
export function applicationRejectedPostInterviewEmail(candidateName, jobTitle) {
  const priority = "medium";
  const priorityColor = "#6b7280";
  const actionText = "View Other Opportunities";
  const careersUrl = "https://modelsuite.ai/careers";

  return generateEmailTemplate({
    title: "Interview Follow-Up",
    message: `Hi <strong>${candidateName}</strong>, thank you for taking the time to interview for the <strong>${jobTitle}</strong> position at ModelSuite.`,
    priority,
    priorityColor,
    actionUrl: careersUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #f9fafb; border-radius: 6px;">
        <p style="margin: 5px 0;">We enjoyed learning more about your background and experience. After thorough consideration, we've decided to move forward with another candidate whose skills and experience more closely align with the specific requirements of this role.</p>
        <p style="margin: 15px 0 5px 0;">This was a competitive process, and the decision was difficult. We were impressed by your qualifications and encourage you to apply for future opportunities that may be an even better match.</p>
      </div>
      <div style="margin-top: 15px; padding: 15px; background-color: #eff6ff; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 14px;"><strong>📚 Feedback for Growth:</strong></p>
        <p style="margin: 10px 0 5px 0; font-size: 14px;">Consider strengthening your skills in areas relevant to the roles you're targeting. We're always posting new positions, so stay connected with us!</p>
      </div>
      <div style="margin-top: 15px; padding: 10px; background-color: #fef3c7; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 13px; color: #92400e;">📅 You can reapply for this or other positions after 3 months.</p>
      </div>
    `,
  });
}

/**
 * Interview No-Show Follow-Up Email - Sent when candidate misses interview
 */
export function interviewNoShowEmail(
  candidateName,
  jobTitle,
  missedDate,
  missedTime,
  rescheduleUrl
) {
  const priority = "high";
  const priorityColor = "#f59e0b";
  const actionText = "Reschedule Interview";

  return generateEmailTemplate({
    title: "Missed Interview - Let's Reschedule",
    message: `Hi <strong>${candidateName}</strong>, we noticed you weren't able to attend your scheduled interview for the <strong>${jobTitle}</strong> position.`,
    priority,
    priorityColor,
    actionUrl: rescheduleUrl,
    actionText,
    additionalInfo: `
      <div style="margin-top: 20px; padding: 15px; background-color: #fef3c7; border-radius: 6px; border-left: 4px solid #f59e0b;">
        <p style="margin: 5px 0;"><strong>Missed Interview:</strong></p>
        <p style="margin: 5px 0;">${missedDate} at ${missedTime}</p>
      </div>
      <div style="margin-top: 15px; padding: 15px; background-color: #eff6ff; border-radius: 6px;">
        <p style="margin: 5px 0;">We understand that unexpected situations arise. If you're still interested in this opportunity, we'd be happy to give you another chance.</p>
        <p style="margin: 15px 0 5px 0;"><strong>What to do next:</strong></p>
        <ol style="margin: 10px 0; padding-left: 20px; font-size: 14px;">
          <li>Reply to this email or contact us at <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a></li>
          <li>Explain what happened (if you'd like)</li>
          <li>We'll work with you to reschedule</li>
        </ol>
      </div>
      <div style="margin-top: 15px; padding: 10px; background-color: #fee2e2; border-radius: 6px;">
        <p style="margin: 5px 0; font-size: 13px; color: #991b1b;">⏰ <strong>Note:</strong> If we don't hear from you within 48 hours, we'll need to close your application.</p>
      </div>
    `,
  });
}
