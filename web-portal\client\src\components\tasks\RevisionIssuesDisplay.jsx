import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  Alert<PERSON>ircle,
  Info,
  ExternalLink,
  Clock,
  History,
} from "lucide-react";

const priorityConfig = {
  high: {
    icon: Alert<PERSON>riangle,
    bgColor: "bg-red-500/10",
    borderColor: "border-red-500/30",
    textColor: "text-red-400",
    badgeBg: "bg-red-500/20",
    label: "High Priority",
  },
  medium: {
    icon: AlertCircle,
    bgColor: "bg-orange-500/10",
    borderColor: "border-orange-500/30",
    textColor: "text-orange-400",
    badgeBg: "bg-orange-500/20",
    label: "Medium Priority",
  },
  low: {
    icon: Info,
    bgColor: "bg-blue-500/10",
    borderColor: "border-blue-500/30",
    textColor: "text-blue-400",
    badgeBg: "bg-blue-500/20",
    label: "Low Priority",
  },
};

export default function RevisionIssuesDisplay({ section }) {
  if (!section.issues || section.issues.length === 0) {
    // Fallback to old feedback format
    if (section.feedback) {
      return (
        <div className="mt-3 p-3 bg-red-500/10 border border-red-500/30 rounded-lg">
          <p className="text-sm text-red-400 font-medium mb-1">
            ⚠️ Revision Required
          </p>
          <p className="text-sm text-slate-300">{section.feedback}</p>
        </div>
      );
    }
    return null;
  }

  const highPriorityCount = section.issues.filter(
    (i) => i.priority === "high"
  ).length;
  const currentAttempt = section.currentAttempt || 1;

  return (
    <div className="mt-3 space-y-3">
      {/* Header with stats */}
      <div className="flex items-center justify-between p-3 bg-red-500/10 border border-red-500/30 rounded-lg">
        <div className="flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-red-400" />
          <div>
            <p className="text-sm font-semibold text-red-400">
              Revision Required
            </p>
            <p className="text-xs text-slate-400">
              {section.issues.length} issue
              {section.issues.length > 1 ? "s" : ""} to fix
              {highPriorityCount > 0 && ` • ${highPriorityCount} high priority`}
            </p>
          </div>
        </div>
        {currentAttempt > 1 && (
          <div className="flex items-center gap-1.5 px-2 py-1 bg-slate-800 rounded text-xs text-slate-400">
            <History className="w-3 h-3" />
            Attempt #{currentAttempt}
          </div>
        )}
      </div>

      {/* Overall feedback if provided */}
      {section.feedback && (
        <div className="p-3 bg-slate-800/50 border border-slate-700 rounded-lg">
          <p className="text-xs font-medium text-slate-300 mb-1">
            Overall Feedback:
          </p>
          <p className="text-sm text-slate-400">{section.feedback}</p>
        </div>
      )}

      {/* Issues list - Grid layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {section.issues.map((issue, index) => {
          const config =
            priorityConfig[issue.priority] || priorityConfig.medium;
          const Icon = config.icon;
          const isLongDescription = issue.description.length > 100;

          return (
            <div
              key={index}
              className={`p-3 ${config.bgColor} border ${config.borderColor} rounded-lg flex flex-col h-full`}
            >
              {/* Issue header */}
              <div className="flex items-start gap-2 mb-2">
                <Icon
                  className={`w-4 h-4 mt-0.5 flex-shrink-0 ${config.textColor}`}
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-xs font-medium text-slate-400">
                      Issue #{index + 1}
                    </span>
                    <span
                      className={`px-2 py-0.5 rounded text-xs font-medium ${config.badgeBg} ${config.textColor}`}
                    >
                      {config.label}
                    </span>
                  </div>

                  {/* Description with collapse for long text */}
                  {isLongDescription ? (
                    <details className="group">
                      <summary className="text-sm text-white cursor-pointer hover:text-slate-200 list-none">
                        <span className="line-clamp-2">
                          {issue.description}
                        </span>
                        <span className="text-xs text-blue-400 group-open:hidden mt-1 inline-block">
                          Click to see more...
                        </span>
                        <span className="text-xs text-blue-400 hidden group-open:inline-block mt-1">
                          Click to collapse
                        </span>
                      </summary>
                      <p className="text-sm text-white whitespace-pre-wrap mt-2">
                        {issue.description}
                      </p>
                    </details>
                  ) : (
                    <p className="text-sm text-white whitespace-pre-wrap">
                      {issue.description}
                    </p>
                  )}
                </div>
              </div>

              {/* Screenshot if available */}
              {issue.screenshot?.url && (
                <div className="mt-auto pt-2">
                  <button
                    onClick={() => {
                      const modal = document.createElement("div");
                      modal.className =
                        "fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm";
                      modal.onclick = () => modal.remove();
                      modal.innerHTML = `
                        <div class="relative max-w-4xl max-h-[90vh] p-4">
                          <img src="${issue.screenshot.url}" class="max-w-full max-h-full rounded-lg" onclick="event.stopPropagation()" />
                          <button class="absolute top-2 right-2 p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg" onclick="this.closest('.fixed').remove()">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                          </button>
                        </div>
                      `;
                      document.body.appendChild(modal);
                    }}
                    className="relative group inline-block"
                  >
                    <img
                      src={issue.screenshot.url}
                      alt={`Issue ${index + 1} screenshot`}
                      className="w-24 h-24 object-cover rounded border-2 border-slate-700 hover:border-blue-500 transition-colors cursor-pointer"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-colors rounded flex items-center justify-center">
                      <span className="text-white text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                        View
                      </span>
                    </div>
                  </button>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Submission history if available */}
      {section.submissionHistory && section.submissionHistory.length > 1 && (
        <details className="group">
          <summary className="flex items-center gap-2 px-3 py-2 bg-slate-800/50 border border-slate-700 rounded-lg cursor-pointer hover:bg-slate-800 transition-colors">
            <History className="w-4 h-4 text-slate-400" />
            <span className="text-sm text-slate-300">
              View Submission History ({section.submissionHistory.length}{" "}
              attempts)
            </span>
          </summary>
          <div className="mt-2 space-y-2">
            {section.submissionHistory
              .slice()
              .reverse()
              .map((submission, idx) => {
                const isLatest = idx === 0;
                const statusColors = {
                  "pending-review":
                    "bg-yellow-500/10 border-yellow-500/30 text-yellow-400",
                  approved:
                    "bg-green-500/10 border-green-500/30 text-green-400",
                  rejected: "bg-red-500/10 border-red-500/30 text-red-400",
                };

                return (
                  <div
                    key={idx}
                    className={`p-3 bg-slate-800/30 border border-slate-700 rounded-lg ${
                      isLatest ? "ring-2 ring-blue-500/30" : ""
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-slate-400">
                          Attempt #{submission.attemptNumber}
                        </span>
                        {isLatest && (
                          <span className="px-2 py-0.5 bg-blue-500/20 text-blue-400 text-xs rounded">
                            Current
                          </span>
                        )}
                      </div>
                      <span
                        className={`px-2 py-0.5 rounded text-xs border ${
                          statusColors[submission.status]
                        }`}
                      >
                        {submission.status.replace("-", " ").toUpperCase()}
                      </span>
                    </div>

                    <div className="text-xs text-slate-400 space-y-1">
                      <div className="flex items-center gap-1.5">
                        <Clock className="w-3 h-3" />
                        Submitted:{" "}
                        {new Date(submission.submittedAt).toLocaleString()}
                      </div>
                      {submission.reviewedAt && (
                        <div className="flex items-center gap-1.5">
                          <Clock className="w-3 h-3" />
                          Reviewed:{" "}
                          {new Date(submission.reviewedAt).toLocaleString()}
                        </div>
                      )}
                      {submission.prLink && (
                        <a
                          href={submission.prLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-1.5 text-blue-400 hover:text-blue-300"
                        >
                          <ExternalLink className="w-3 h-3" />
                          View PR
                        </a>
                      )}
                    </div>

                    {submission.feedback &&
                      submission.status === "rejected" && (
                        <div className="mt-2 pt-2 border-t border-slate-700">
                          <p className="text-xs text-slate-400">
                            <span className="font-medium">Feedback:</span>{" "}
                            {submission.feedback}
                          </p>
                          {submission.issues &&
                            submission.issues.length > 0 && (
                              <p className="text-xs text-slate-500 mt-1">
                                {submission.issues.length} issue
                                {submission.issues.length > 1 ? "s" : ""}{" "}
                                identified
                              </p>
                            )}
                        </div>
                      )}
                  </div>
                );
              })}
          </div>
        </details>
      )}
    </div>
  );
}
