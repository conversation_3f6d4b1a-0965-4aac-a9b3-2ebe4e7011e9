var zlib = require("zlib");
var promisify = require("./_promisify.js");
var bind = function(c, f) { return f && f.bind(c); };
Object.defineProperties(module.exports, {
  DEFLATE: { enumerable: true, value: zlib.DEFLATE },
  DEFLATERAW: { enumerable: true, value: zlib.DEFLATERAW },
  Deflate: { enumerable: true, value: zlib.Deflate },
  DeflateRaw: { enumerable: true, value: zlib.DeflateRaw },
  GUNZIP: { enumerable: true, value: zlib.GUNZIP },
  GZIP: { enumerable: true, value: zlib.GZIP },
  Gunzip: { enumerable: true, value: zlib.Gunzip },
  Gzip: { enumerable: true, value: zlib.Gzip },
  INFLATE: { enumerable: true, value: zlib.INFLATE },
  INFLATERAW: { enumerable: true, value: zlib.INFLATERAW },
  Inflate: { enumerable: true, value: zlib.Inflate },
  InflateRaw: { enumerable: true, value: zlib.InflateRaw },
  UNZIP: { enumerable: true, value: zlib.UNZIP },
  Unzip: { enumerable: true, value: zlib.Unzip },
  ZLIB_VERNUM: { enumerable: true, value: zlib.ZLIB_VERNUM },
  ZLIB_VERSION: { enumerable: true, value: zlib.ZLIB_VERSION },
  Z_BEST_COMPRESSION: { enumerable: true, value: zlib.Z_BEST_COMPRESSION },
  Z_BEST_SPEED: { enumerable: true, value: zlib.Z_BEST_SPEED },
  Z_BLOCK: { enumerable: true, value: zlib.Z_BLOCK },
  Z_BUF_ERROR: { enumerable: true, value: zlib.Z_BUF_ERROR },
  Z_DATA_ERROR: { enumerable: true, value: zlib.Z_DATA_ERROR },
  Z_DEFAULT_CHUNK: { enumerable: true, value: zlib.Z_DEFAULT_CHUNK },
  Z_DEFAULT_COMPRESSION: { enumerable: true, value: zlib.Z_DEFAULT_COMPRESSION },
  Z_DEFAULT_LEVEL: { enumerable: true, value: zlib.Z_DEFAULT_LEVEL },
  Z_DEFAULT_MEMLEVEL: { enumerable: true, value: zlib.Z_DEFAULT_MEMLEVEL },
  Z_DEFAULT_STRATEGY: { enumerable: true, value: zlib.Z_DEFAULT_STRATEGY },
  Z_DEFAULT_WINDOWBITS: { enumerable: true, value: zlib.Z_DEFAULT_WINDOWBITS },
  Z_ERRNO: { enumerable: true, value: zlib.Z_ERRNO },
  Z_FILTERED: { enumerable: true, value: zlib.Z_FILTERED },
  Z_FINISH: { enumerable: true, value: zlib.Z_FINISH },
  Z_FIXED: { enumerable: true, value: zlib.Z_FIXED },
  Z_FULL_FLUSH: { enumerable: true, value: zlib.Z_FULL_FLUSH },
  Z_HUFFMAN_ONLY: { enumerable: true, value: zlib.Z_HUFFMAN_ONLY },
  Z_MAX_CHUNK: { enumerable: true, value: zlib.Z_MAX_CHUNK },
  Z_MAX_LEVEL: { enumerable: true, value: zlib.Z_MAX_LEVEL },
  Z_MAX_MEMLEVEL: { enumerable: true, value: zlib.Z_MAX_MEMLEVEL },
  Z_MAX_WINDOWBITS: { enumerable: true, value: zlib.Z_MAX_WINDOWBITS },
  Z_MEM_ERROR: { enumerable: true, value: zlib.Z_MEM_ERROR },
  Z_MIN_CHUNK: { enumerable: true, value: zlib.Z_MIN_CHUNK },
  Z_MIN_LEVEL: { enumerable: true, value: zlib.Z_MIN_LEVEL },
  Z_MIN_MEMLEVEL: { enumerable: true, value: zlib.Z_MIN_MEMLEVEL },
  Z_MIN_WINDOWBITS: { enumerable: true, value: zlib.Z_MIN_WINDOWBITS },
  Z_NEED_DICT: { enumerable: true, value: zlib.Z_NEED_DICT },
  Z_NO_COMPRESSION: { enumerable: true, value: zlib.Z_NO_COMPRESSION },
  Z_NO_FLUSH: { enumerable: true, value: zlib.Z_NO_FLUSH },
  Z_OK: { enumerable: true, value: zlib.Z_OK },
  Z_PARTIAL_FLUSH: { enumerable: true, value: zlib.Z_PARTIAL_FLUSH },
  Z_RLE: { enumerable: true, value: zlib.Z_RLE },
  Z_STREAM_END: { enumerable: true, value: zlib.Z_STREAM_END },
  Z_STREAM_ERROR: { enumerable: true, value: zlib.Z_STREAM_ERROR },
  Z_SYNC_FLUSH: { enumerable: true, value: zlib.Z_SYNC_FLUSH },
  Z_VERSION_ERROR: { enumerable: true, value: zlib.Z_VERSION_ERROR },
  Zlib: { enumerable: true, value: zlib.Zlib },
  codes: { enumerable: true, value: zlib.codes },
  constants: { enumerable: true, get: function() { return zlib.constants; }, set: function(v) { zlib.constants = v; } },
  createDeflate: { enumerable: true, value: bind(zlib, zlib.createDeflate) },
  createDeflateRaw: { enumerable: true, value: bind(zlib, zlib.createDeflateRaw) },
  createGunzip: { enumerable: true, value: bind(zlib, zlib.createGunzip) },
  createGzip: { enumerable: true, value: bind(zlib, zlib.createGzip) },
  createInflate: { enumerable: true, value: bind(zlib, zlib.createInflate) },
  createInflateRaw: { enumerable: true, value: bind(zlib, zlib.createInflateRaw) },
  createUnzip: { enumerable: true, value: bind(zlib, zlib.createUnzip) },
  deflate: { enumerable: true, value: promisify(zlib, zlib.deflate, 1) },
  deflateRaw: { enumerable: true, value: promisify(zlib, zlib.deflateRaw, 1) },
  deflateRawSync: { enumerable: true, value: bind(zlib, zlib.deflateRawSync) },
  deflateSync: { enumerable: true, value: bind(zlib, zlib.deflateSync) },
  gunzip: { enumerable: true, value: promisify(zlib, zlib.gunzip, 1) },
  gunzipSync: { enumerable: true, value: bind(zlib, zlib.gunzipSync) },
  gzip: { enumerable: true, value: promisify(zlib, zlib.gzip, 1) },
  gzipSync: { enumerable: true, value: bind(zlib, zlib.gzipSync) },
  inflate: { enumerable: true, value: promisify(zlib, zlib.inflate, 1) },
  inflateRaw: { enumerable: true, value: promisify(zlib, zlib.inflateRaw, 1) },
  inflateRawSync: { enumerable: true, value: bind(zlib, zlib.inflateRawSync) },
  inflateSync: { enumerable: true, value: bind(zlib, zlib.inflateSync) },
  unzip: { enumerable: true, value: promisify(zlib, zlib.unzip, 1) },
  unzipSync: { enumerable: true, value: bind(zlib, zlib.unzipSync) },
});