import dotenv from "dotenv";

// Load environment variables FIRST before any other imports
dotenv.config();

// ==================== CRITICAL SECURITY VALIDATION ====================
// Validate required environment variables for sensitive data encryption
function validateRequiredEnvVars() {
  const requiredVars = {
    ENCRYPTION_KEY: {
      minLength: 32,
      description:
        "AES-256 encryption key for sensitive data (bank details, PAN numbers)",
    },
  };

  const missingVars = [];
  const invalidVars = [];

  for (const [varName, config] of Object.entries(requiredVars)) {
    const value = process.env[varName];

    if (!value) {
      missingVars.push(`${varName}: ${config.description}`);
    } else if (config.minLength && value.length < config.minLength) {
      invalidVars.push(
        `${varName}: Must be at least ${config.minLength} characters (current: ${value.length})`
      );
    }
  }

  if (missingVars.length > 0 || invalidVars.length > 0) {
    console.error("\n" + "=".repeat(80));
    console.error("❌ CRITICAL: Missing or Invalid Environment Variables");
    console.error("=".repeat(80));

    if (missingVars.length > 0) {
      console.error("\n⚠️  Missing Variables:");
      missingVars.forEach((msg) => console.error(`   - ${msg}`));
    }

    if (invalidVars.length > 0) {
      console.error("\n⚠️  Invalid Variables:");
      invalidVars.forEach((msg) => console.error(`   - ${msg}`));
    }

    console.error("\n📝 To fix this:");
    console.error("   1. Open your .env file");
    console.error(
      "   2. Add/update: ENCRYPTION_KEY=<your-32-character-secure-key>"
    );
    console.error(
      "   3. Generate key: node -e \"console.log(require('crypto').randomBytes(32).toString('hex'))\""
    );
    console.error("=".repeat(80) + "\n");

    process.exit(1); // Exit immediately - fail fast!
  }

  console.log("✅ Security: All required environment variables validated");
}

// Run validation before importing any models
validateRequiredEnvVars();
// ==================== END SECURITY VALIDATION ====================

import express from "express";
import cors from "cors";
import cookieParser from "cookie-parser";
import { createServer } from "http";
import { Server } from "socket.io";
import connectDB from "./config/db.js";
import authRoutes from "./routes/auth.js";
import dashRoutes from "./routes/dash.js";
import reportRoutes from "./routes/Report/reportRoutes.js";
import badgeRoutes from "./routes/Badge/badgeRoutes.js";
import levelBadgeRoutes from "./routes/badges/badgeRoutes.js";
import checkinRoutes from "./routes/Checkin/checkinRoutes.js";
import statusRoutes from "./routes/Status/statusRoutes.js";
import adminRoutes from "./routes/admin.js";
import adminManagementRoutes from "./routes/admin/adminManagementRoutes.js";
import tasksRoutes from "./routes/tasks/tasksRoutes.js";
import notificationRoutes from "./routes/notification/notificationRoutes.js";
import leaveRoutes from "./routes/leave/leaveRoutes.js";
import employeeAnalyticsRoutes from "./routes/jibble/employeeAnalytics.js";
import timeTrackingRoutes from "./routes/timeTrackingRoutes.js";
import jibbleConfigRoutes from "./routes/jibble/jibbleConfig.js";
import jobRoutes, { publicJobRoutes } from "./routes/hiring/jobRoutes.js";
import applicationRoutes from "./routes/hiring/applicationRoutes.js";
import adminInterviewRoutes from "./routes/hiring/adminInterviewRoutes.js";
import onboardingRoutes from "./routes/hiring/onboardingRoutes.js";
import adminOnboardingRoutes from "./routes/hiring/adminOnboardingRoutes.js";
import calendlyWebhookRoutes from "./routes/hiring/calendlyWebhookRoutes.js";
import offerRoutes, { publicOfferRoutes } from "./routes/hiring/offerRoutes.js";
import supportRoutes from "./routes/supportRoutes.js";
import chatRoutes from "./routes/chat/index.js";
import pushNotificationRoutes from "./routes/notifications/notificationRoutes.js";
import aiChatRoutes from "./routes/ai/aiChatRoutes.js";
import aiAdminRoutes from "./routes/ai/aiAdminRoutes.js";
import meetingRoutes from "./routes/meetingRoutes.js";
import meetingXpRoutes from "./routes/meetingXp.js";
import webhookRoutes from "./routes/webhookRoutes.js";
import calendarWebhookRoutes from "./routes/calendarWebhookRoutes.js";
import complianceRoutes from "./routes/complianceRoutes.js";
import attendanceRoutes from "./routes/attendance.js";
import paymentRoutes from "./routes/payments.js";
import filesRoutes from "./routes/files.js";
import uploadRoutes from "./routes/uploadRoutes.js";
import debugRoutes from "./routes/debug.js";
import saturdaySpinRoutes from "./routes/saturdaySpinRoutes.js";
import saturdaySpinTestRoutes from "./routes/saturdaySpinTestRoutes.js";
import saturdaySpinAdminRoutes from "./routes/saturdaySpinAdmin.js";
import xpRoutes from "./routes/xpRoutes.js";
import debugXPRoutes from "./routes/debugXP.js";
import checkUpRoutes from "./routes/checkUpRoutes.js";
import { initializeSaturdaySpinFailsafes } from "./utils/saturdaySpinFailsafes.js";
import realtimeComplianceRoutes from "./routes/compliance/realtimeComplianceRoutes.js";
import summaryRoutes from "./routes/compliance/summaryRoutes.js";
import certificateRoutes from "./routes/certificateRoutes.js";
import screenshotDiscrepancyRoutes from "./routes/screenshotDiscrepancy/discrepancyRoutes.js";
import adminDocumentRoutes from "./routes/documents/adminDocumentRoutes.js";
import jibbleAdminRoutes from "./routes/admin/jibbleAdminRoutes.js";
import talentDocumentRoutes from "./routes/documents/talentDocumentRoutes.js";
import weeklyComplianceSummaryJob from "./jobs/weeklyComplianceSummaryJob.js";
import nightlyCertificateJob from "./jobs/nightlyCertificateJob.js";
import taskRequestExpirationJob from "./jobs/taskRequestExpirationJob.js";
import offerExpiryJob from "./jobs/offerExpiryJob.js";
import dailyDeadlineNotificationJob from "./jobs/dailyDeadlineNotificationJob.js";
// HIGH #6: Import HTTPS enforcement and security headers
import { enforceHttps, securityHeaders } from "./middleware/httpsEnforcer.js";
// Connect to MongoDB (env vars already loaded at top of file)
connectDB();

// Enhanced CORS configuration for production
const allowedOrigins = [
  "http://localhost:5173",
  "https://modelsuite-webportal.vercel.app",
  "https://www.talents.modelsuite.ai",
  "https://talents.modelsuite.ai",
  "https://talents.modelsuite.ai/login",
  "https://www.talents.modelsuite.ai/login",
  "https://web-portal-test-link.vercel.app",
];

// Add custom origins from environment variable
if (process.env.ALLOWED_ORIGINS) {
  const customOrigins = process.env.ALLOWED_ORIGINS.split(",").map((origin) =>
    origin.trim()
  );
  allowedOrigins.push(...customOrigins);
}

console.log("🏭 Environment:", process.env.NODE_ENV);

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: function (origin, callback) {
      // Allow requests with no origin (mobile apps)
      if (!origin) return callback(null, true);

      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      } else {
        console.warn(`❌ Socket.IO CORS blocked origin: ${origin}`);
        return callback(new Error("Not allowed by CORS"), false);
      }
    },
    credentials: true,
    methods: ["GET", "POST"],
  },
});

// Make io accessible to controllers
app.set("io", io);

const PORT = process.env.PORT || 5000;

// Global variables for compliance engine state
let complianceEngineReady = false;
let complianceEngineStatus = "not_initialized";
let complianceEngineError = null;

// HIGH #6: HTTPS Enforcement & Security Headers (must be BEFORE other middleware)
app.use(enforceHttps); // Redirect HTTP -> HTTPS in production
app.use(securityHeaders); // Add security headers (HSTS, CSP, etc.)

// Middleware
app.use(
  cors({
    origin: function (origin, callback) {
      // Allow requests with no origin (mobile apps, curl, Postman)
      if (!origin) return callback(null, true);

      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      } else {
        console.warn(`❌ CORS blocked origin: ${origin}`);
        return callback(new Error("Not allowed by CORS"), false);
      }
    },
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "Cookie",
      "X-Requested-With",
    ],
    exposedHeaders: ["Set-Cookie"],
    optionsSuccessStatus: 200, // For legacy browser support
  })
);

app.use(express.json({ limit: "50mb" })); // Increased for AI image uploads
app.use(express.urlencoded({ extended: true, limit: "50mb" }));
app.use(cookieParser());

// Add security headers for better cookie handling
app.use((req, res, next) => {
  // Add security headers for cross-origin cookies
  res.setHeader("Access-Control-Allow-Credentials", "true");

  // Add headers to help with cookie debugging
  if (process.env.NODE_ENV === "production") {
    res.setHeader("Vary", "Origin");
  }

  next();
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
    origin: req.get("origin"),
    userAgent: req.get("user-agent"),
  });
});

// Cookie test endpoint for debugging
app.get("/test-cookie", (req, res) => {
  const isProduction = process.env.NODE_ENV === "production";

  res.cookie("testCookie", "testValue", {
    httpOnly: true,
    secure: isProduction,
    sameSite: isProduction ? "none" : "lax",
    maxAge: 60000, // 1 minute
    path: "/",
  });

  res.json({
    message: "Test cookie set",
    cookieOptions: {
      secure: isProduction,
      sameSite: isProduction ? "none" : "lax",
      isProduction,
      environment: process.env.NODE_ENV,
      origin: req.get("origin"),
      referer: req.get("referer"),
    },
  });
});

// Serve uploaded files statically with proper headers
app.use(
  "/uploads",
  express.static("uploads", {
    setHeaders: (res, path) => {
      // Set Content-Disposition to 'inline' for viewable files
      const ext = path.split(".").pop()?.toLowerCase();
      const fileName = path.split("/").pop()?.toLowerCase();

      const viewableExtensions = [
        "txt",
        "env",
        "log",
        "md",
        "json",
        "xml",
        "html",
        "css",
        "js",
        "jsx",
        "ts",
        "tsx",
        "yml",
        "yaml",
        "config",
        "ini",
        "conf",
        "gitignore",
        "gitattributes",
        "pdf",
        "jpg",
        "jpeg",
        "png",
        "gif",
        "svg",
        "webp",
        "bmp",
      ];

      // Check if file should be viewable by extension or specific filename patterns
      const isViewable =
        viewableExtensions.includes(ext) ||
        fileName?.startsWith(".env") ||
        fileName?.includes(".env.") ||
        fileName === ".gitignore" ||
        fileName === ".gitattributes" ||
        fileName === "dockerfile" ||
        fileName === "readme";

      if (isViewable) {
        res.setHeader("Content-Disposition", "inline");

        // Set correct MIME types for text files
        if (
          [
            "env",
            "log",
            "txt",
            "md",
            "gitignore",
            "gitattributes",
            "conf",
            "ini",
            "config",
          ].includes(ext) ||
          fileName?.startsWith(".env") ||
          fileName === "dockerfile" ||
          fileName === "readme"
        ) {
          res.setHeader("Content-Type", "text/plain; charset=utf-8");
        } else if (ext === "json") {
          res.setHeader("Content-Type", "application/json; charset=utf-8");
        } else if (["yml", "yaml"].includes(ext)) {
          res.setHeader("Content-Type", "text/yaml; charset=utf-8");
        } else if (ext === "xml") {
          res.setHeader("Content-Type", "text/xml; charset=utf-8");
        } else if (["js", "mjs", "jsx"].includes(ext)) {
          res.setHeader(
            "Content-Type",
            "application/javascript; charset=utf-8"
          );
        } else if (["ts", "tsx"].includes(ext)) {
          res.setHeader("Content-Type", "text/typescript; charset=utf-8");
        } else if (ext === "css") {
          res.setHeader("Content-Type", "text/css; charset=utf-8");
        } else if (ext === "html") {
          res.setHeader("Content-Type", "text/html; charset=utf-8");
        } else if (ext === "md") {
          res.setHeader("Content-Type", "text/markdown; charset=utf-8");
        }
      }
    },
  })
);

// Routes
app.use("/auth", authRoutes);
app.use("/api", authRoutes);
app.use("/api", dashRoutes);
app.use("/api/reports", reportRoutes);
app.use("/api/badges", badgeRoutes); // Old achievement badges
app.use("/api/level-badges", levelBadgeRoutes); // ✨ New level-based badge system
app.use("/api/checkins", checkinRoutes);
app.use("/api/status", statusRoutes);
app.use("/admin", adminRoutes);
app.use("/api/admin/management", adminManagementRoutes); // 🔐 Admin team management
app.use("/api/tasks", tasksRoutes);
app.use("/api/notifications", notificationRoutes);
app.use("/api/meetings", meetingXpRoutes); // 🎯 Meeting XP routes
app.use("/api/push-notifications", pushNotificationRoutes); // 🔔 Push notification routes
app.use("/api/unavailability", leaveRoutes);
app.use("/api/files", filesRoutes);
app.use("/api/upload", uploadRoutes);
app.use("/api/xp", xpRoutes); // 📊 XP tracking routes
app.use("/api/debug-xp", debugXPRoutes); // 🔍 XP debugging routes
app.use("/api/check-up", checkUpRoutes); // ⏰ 3-Hour Check-up routes

// Compliance and payments

// Support routes
app.use("/api/support", supportRoutes);

// Desktop App Download Routes
import path from "path";
import fs from "fs";
const DESKTOP_INSTALLER_DIR = path.join(
  process.cwd(),
  "../desktop-apps/installer"
);

// Desktop app download endpoint
app.get("/api/download/desktop/:platform?", (req, res) => {
  const { platform = "windows" } = req.params;
  const { format } = req.query;

  // File mapping for desktop installers
  const fileMap = {
    windows: "ModelSuite Talent Setup 1.0.0.exe",
    "windows-portable": "ModelSuite Talent 1.0.0.exe",
    "macos-x64": "ModelSuite-Talent-1.0.0-x64.dmg",
    "macos-arm64": "ModelSuite-Talent-1.0.0-arm64.dmg",
    macos: "ModelSuite-Talent-1.0.0-x64.dmg",
    "linux-deb": "ModelSuite-Talent-1.0.0-x64.deb",
    "linux-appimage": "ModelSuite-Talent-1.0.0-x64.AppImage",
    linux: "ModelSuite-Talent-1.0.0-x64.AppImage",
  };

  // Handle format parameter
  let key = platform;
  if (platform === "linux" && format) {
    key = `linux-${format.toLowerCase()}`;
  } else if (platform === "windows" && format === "portable") {
    key = "windows-portable";
  } else if (platform === "macos" && format) {
    key = `macos-${format.toLowerCase()}`;
  }

  const fileName = fileMap[key];
  if (!fileName) {
    return res.status(404).json({ error: "Platform not found" });
  }

  const filePath = path.join(DESKTOP_INSTALLER_DIR, fileName);

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      error: "Desktop app installer not found. Please contact support.",
      expectedPath: fileName,
      availablePlatforms: Object.keys(fileMap),
    });
  }

  // Log download
  console.log(
    `📥 Desktop app download: ${platform} (${fileName}) - ${new Date().toISOString()}`
  );
  console.log(`   User-Agent: ${req.get("User-Agent")}`);
  console.log(`   IP: ${req.ip}`);

  // Send file
  res.download(filePath, fileName);
});

// Desktop app info endpoint
app.get("/api/desktop/info", (req, res) => {
  const downloads = [];

  const files = {
    windows: {
      file: "ModelSuite Talent Setup 1.0.0.exe",
      label: "Windows Installer",
    },
    "windows-portable": {
      file: "ModelSuite Talent 1.0.0.exe",
      label: "Windows Portable",
    },
    "macos-x64": {
      file: "ModelSuite-Talent-1.0.0-x64.dmg",
      label: "macOS (Intel)",
    },
    "macos-arm64": {
      file: "ModelSuite-Talent-1.0.0-arm64.dmg",
      label: "macOS (Apple Silicon)",
    },
    "linux-appimage": {
      file: "ModelSuite-Talent-1.0.0-x64.AppImage",
      label: "Linux AppImage",
    },
    "linux-deb": {
      file: "ModelSuite-Talent-1.0.0-x64.deb",
      label: "Linux DEB",
    },
  };

  Object.entries(files).forEach(([key, { file, label }]) => {
    const filePath = path.join(DESKTOP_INSTALLER_DIR, file);
    const exists = fs.existsSync(filePath);
    let size = 0;

    if (exists) {
      try {
        const stats = fs.statSync(filePath);
        size = Math.round(stats.size / (1024 * 1024)); // MB
      } catch (error) {
        console.error("Error getting file stats:", error);
      }
    }

    downloads.push({
      platform: key,
      label,
      available: exists,
      size: `${size} MB`,
      filename: file,
      downloadUrl: `/api/download/desktop/${key}`,
    });
  });

  res.json({
    version: "1.0.0",
    appName: "ModelSuite Talent Desktop",
    description:
      "Professional desktop application for ModelSuite Talent platform",
    features: [
      "Native system integration",
      "System tray support",
      "Desktop notifications",
      "Auto-update capability",
      "Offline access (partial)",
    ],
    downloads,
  });
});

// Chat routes
app.use("/api/chat", chatRoutes);

// AI UI Feedback Routes
app.use("/api/ai-feedback", aiChatRoutes); // User-facing AI chat endpoints
app.use("/api/admin/ai-feedback", aiAdminRoutes); // Admin monitoring endpoints

// Meeting routes
app.use("/api/meetings", meetingRoutes);

// Webhook routes (Fireflies integration)
app.use("/api/webhooks", webhookRoutes);

// Calendar OAuth webhook routes (Automatic Fireflies)
app.use("/api", calendarWebhookRoutes);

// Compliance routes (Payment compliance calculation)
app.use("/api/compliance", complianceRoutes);

// 🎯 Real-Time Compliance Engine Routes
/**
 * Middleware to check if compliance engine is ready
 *
 * Multi-Instance Deployment Note:
 * This middleware checks process-local state. For load-balanced deployments,
 * configure sticky sessions in your load balancer or use the health check endpoint
 * (/api/health) for readiness probes to route traffic only to ready instances.
 *
 * coderabbit:ignore - Process-local state is intentional design choice
 */
app.use("/api/compliance-realtime", (req, res, next) => {
  if (!complianceEngineReady) {
    return res.status(503).json({
      error: "Service Unavailable",
      message:
        "Real-Time Compliance Engine is not ready. Please try again shortly.",
      status: complianceEngineStatus,
      instance: process.env.INSTANCE_ID || "unknown", // Optional: track which instance responded
    });
  }
  next();
});
app.use("/api/compliance-realtime", realtimeComplianceRoutes);

// Compliance Summaries routes (Weekly AI-generated summaries)
app.use("/api/compliance-summaries", summaryRoutes);

// Certificate routes (Certificate generation, verification, and progress tracking)
app.use("/api/certificates", certificateRoutes);

// Screenshot Discrepancy routes (Track Jibble screenshot issues)
app.use("/api/admin/screenshot-discrepancies", screenshotDiscrepancyRoutes);

// Document Management routes (Talent documents upload and management)
app.use("/api/admin/documents", adminDocumentRoutes);

// 🕐 Jibble Clock-Out Validation Admin Routes
app.use("/api/admin/jibble", jibbleAdminRoutes);
app.use("/api/documents", talentDocumentRoutes);

// Payment routes (Payment tracking and history)
app.use("/api/payments", paymentRoutes);

// Attendance routes (Meeting attendance management)
app.use("/admin/attendance", attendanceRoutes);

// Saturday Spin routes (Weekly random XP bonus)
app.use("/api/saturday-spin", saturdaySpinRoutes); // 🎰 Saturday Spin routes
app.use("/api/saturday-spin/test", saturdaySpinTestRoutes); // 🧪 Local testing routes
app.use("/api/admin/saturday-spin", saturdaySpinAdminRoutes); // 🛠️ Saturday Spin admin tools

// Employee Analytics - Focused API for getting employee hours worked
app.use("/api/employee-analytics", employeeAnalyticsRoutes);

// Time Tracking - Clock-in, clock-out, and break functionality
app.use("/api/time-tracking", timeTrackingRoutes);
app.use("/api/jibble", jibbleConfigRoutes);

// Hiring & Onboarding Routes
app.use("/api/hiring/admin/jobs", jobRoutes); // Admin job management
app.use("/api/hiring/jobs", publicJobRoutes); // Public job viewing
app.use("/api/hiring/applications", applicationRoutes); // Public application submission
app.use("/api/hiring/admin/applications", applicationRoutes); // Admin application management (includes offer extension)
app.use("/api/hiring/admin/applications", offerRoutes); // Admin offer management (extend offer)
app.use("/api/hiring/admin", adminInterviewRoutes); // Admin interview management (shortlist/reject/approve/interviews)
app.use("/api/hiring/onboarding", onboardingRoutes); // Public onboarding form
app.use("/api/hiring/admin", adminOnboardingRoutes); // Admin onboarding management
app.use("/api/webhooks", calendlyWebhookRoutes); // Calendly webhook integration (PUBLIC) - IMPORTANT: Must match webhook URL in Calendly
app.use("/offer", publicOfferRoutes); // Public offer acceptance/rejection (token-based, NO AUTH)

/**
 * Health check endpoint
 *
 * Note: In multi-instance deployments, each instance reports its own health.
 * Load balancers should use this endpoint for readiness probes to ensure
 * traffic is only routed to instances where complianceEngine.ready === true.
 *
 * Optional: Set INSTANCE_ID environment variable to track which instance responded.
 */
app.get("/api/health", (req, res) => {
  const healthStatus = {
    message: "Server is running successfully!",
    timestamp: new Date().toISOString(),
    status: "healthy",
    instance: process.env.INSTANCE_ID || "unknown",
    services: {
      complianceEngine: {
        status: complianceEngineStatus,
        ready: complianceEngineReady,
        error: complianceEngineError,
      },
    },
  };

  // Return 503 if compliance engine failed (for readiness probe)
  const httpStatus = complianceEngineStatus === "failed" ? 503 : 200;
  res.status(httpStatus).json(healthStatus);
});

// Socket.IO connection handling
const connectedUsers = new Map();

// Attach connectedUsers to io so routes can access it
io.connectedUsers = connectedUsers;

io.on("connection", async (socket) => {
  // User connected - logging disabled for cleaner output

  // Authenticate socket connection
  const token = socket.handshake.auth.token;
  if (token) {
    try {
      const { verifyAccessToken } = await import("./utils/auth.js");
      const decoded = verifyAccessToken(token);

      if (decoded && decoded.userId) {
        socket.userId = String(decoded.userId);

        // Auto-join user-specific room for compliance updates
        socket.join(`user_${socket.userId}`);
        connectedUsers.set(socket.userId, socket.id);

        // User authenticated and joined room

        // Notify others that user is online
        socket.broadcast.emit("user_online", socket.userId);
      }
    } catch (error) {
      console.error("❌ Socket authentication failed:", error.message);
    }
  }

  // Handle user joining
  socket.on("join", (userId) => {
    // Ensure userId is a string for consistency
    const userIdStr = String(userId);

    connectedUsers.set(userIdStr, socket.id);
    socket.userId = userIdStr;

    // Join user to their personal notification room
    socket.join(userIdStr);
    // User joined personal notification room

    // Send current online users list to the newly connected user
    const currentOnlineUsers = Array.from(connectedUsers.keys());
    socket.emit("online_users_list", currentOnlineUsers);

    // Notify others that user is online
    socket.broadcast.emit("user_online", userIdStr);
    console.log(
      `User ${userIdStr} joined. Total online: ${connectedUsers.size}`
    );
  });

  // Handle joining a conversation
  socket.on("join_conversation", (conversationId) => {
    console.log(`🚪 join_conversation event received:`, {
      userId: socket.userId,
      conversationId,
      socketId: socket.id
    });
    
    socket.join(`conversation_${conversationId}`);
    
    console.log(`✅ User ${socket.userId} successfully joined conversation_${conversationId}`);
  });

  // Handle sending messages
  socket.on("send_message", async (messageData) => {
    try {
      // Create a mock request object for the controller
      const mockReq = {
        body: {
          conversationId: messageData.conversationId,
          content: messageData.content,
          messageType: messageData.messageType || "text",
          replyTo: messageData.replyTo,
        },
        user: {
          userId: messageData.senderId,
          userType: "talent", // Default to talent, could be enhanced to detect actual type
        },
        app: { get: (key) => (key === "io" ? io : null) },
      };

      const mockRes = {
        status: (code) => ({
          json: (data) => {
            if (code >= 400) {
              console.error("Message controller error:", data);
              socket.emit("error", data);
            }
          },
        }),
        json: (data) => {
          if (data.success && data.data) {
            // Message was successfully processed by controller
            console.log("Message processed successfully by controller");
          }
        },
      };

      // Import and call the message controller
      const { sendMessage } = await import(
        "./controllers/chat/messageController.js"
      );
      await sendMessage(mockReq, mockRes);
    } catch (error) {
      console.error("Error sending message:", error);
      socket.emit("error", { message: "Failed to send message" });
    }
  });

  // Handle message editing
  socket.on("edit_message", (messageData) => {
    io.to(`conversation_${messageData.conversationId}`).emit(
      "message_edited",
      messageData
    );
  });

  // Handle message deletion
  socket.on("delete_message", (messageData) => {
    io.to(`conversation_${messageData.conversationId}`).emit(
      "message_deleted",
      messageData
    );
  });

  // Handle typing indicators
  socket.on("typing_start", (data) => {
    socket.to(`conversation_${data.conversationId}`).emit("user_typing", {
      userId: socket.userId,
      conversationId: data.conversationId,
    });
  });

  socket.on("typing_stop", (data) => {
    socket
      .to(`conversation_${data.conversationId}`)
      .emit("user_stopped_typing", {
        userId: socket.userId,
        conversationId: data.conversationId,
      });
  });

  // Handle message read events
  socket.on("message_read", async (data) => {
    try {
      const { messageId, conversationId } = data;
      // Broadcast read receipt to conversation participants
      socket.to(`conversation_${conversationId}`).emit("message_read", {
        messageId,
        userId: socket.userId,
        conversationId,
        readAt: new Date()
      });
    } catch (error) {
      console.error("Message read event error:", error);
    }
  });

  // Handle conversation read events
  socket.on("conversation_read", async (data) => {
    try {
      const { conversationId } = data;
      // Broadcast to conversation that user has read all messages
      socket.to(`conversation_${conversationId}`).emit("conversation_read", {
        conversationId,
        userId: socket.userId,
        readAt: new Date()
      });
    } catch (error) {
      console.error("Conversation read event error:", error);
    }
  });

  // Handle disconnection
  socket.on("disconnect", () => {
    if (socket.userId) {
      connectedUsers.delete(socket.userId);
      // Notify others that user is offline
      socket.broadcast.emit("user_offline", socket.userId);
      console.log(
        `User ${socket.userId} disconnected. Total online: ${connectedUsers.size}`
      );
    }
  });
});

server.listen(PORT, async () => {
  console.log(`🚀 Server running on port ${PORT}`);

  // Initialize Saturday Spin failsafe systems
  try {
    const failsafesInitialized = initializeSaturdaySpinFailsafes();
    if (failsafesInitialized) {
      // Saturday Spin failsafe systems initialized
    } else {
      console.warn(`⚠️ Saturday Spin failsafe initialization failed`);
    }
  } catch (error) {
    console.error(`❌ Saturday Spin failsafe initialization error:`, error);
  }

  // Initialize Real-Time Compliance Engine with timeout and validation
  initializeComplianceEngine();

  // Initialize Weekly Compliance Summary Job
  try {
    await weeklyComplianceSummaryJob.initialize();
    // Weekly Compliance Summary Job initialized
  } catch (error) {
    console.error(
      "❌ Failed to initialize Weekly Compliance Summary Job:",
      error
    );
  }

  // Initialize Nightly Certificate Eligibility Job
  try {
    nightlyCertificateJob.initialize();
    // Nightly Certificate Job initialized
  } catch (error) {
    console.error("❌ Failed to initialize Nightly Certificate Job:", error);
  }

  // Initialize Task Request Expiration Job
  try {
    taskRequestExpirationJob.start(io);
    console.log(
    // Task Request Expiration Job initialized
    );
  } catch (error) {
    console.error(
      "❌ Failed to initialize Task Request Expiration Job:",
      error
    );
  }

  // Initialize Offer Expiry Job
  try {
    offerExpiryJob.start();
    console.log(
      // Offer Expiry Job initialized
    );
  } catch (error) {
    console.error("❌ Failed to initialize Offer Expiry Job:", error);
  }

  // Initialize Daily Deadline Notification Job
  try {
    dailyDeadlineNotificationJob.start();
    // Make job accessible for manual testing
    app.set("dailyDeadlineNotificationJob", dailyDeadlineNotificationJob);
    // Daily Deadline Notification Job initialized
  } catch (error) {
    console.error("❌ Failed to initialize Deadline Notification Job:", error);
  }
});

/**
 * Initialize Real-Time Compliance Engine with proper error handling and timeout
 *
 * Multi-Instance Deployment Strategy:
 *
 * This function sets process-local state (complianceEngineReady, complianceEngineStatus).
 * Each instance in a load-balanced deployment will initialize independently.
 *
 * Recommended Production Deployment Approaches:
 *
 * 1. **Sticky Sessions** (Current recommended approach)
 *    - Configure load balancer for session affinity
 *    - Ensures requests from same client route to same instance
 *    - Example: AWS ALB with stickiness enabled, NGINX ip_hash
 *
 * 2. **Health Check Based Routing** (Container environments)
 *    - Use readiness probes (/api/health endpoint) to delay traffic until initialized
 *    - Load balancer only routes to "ready" instances
 *    - Prevents 503 responses during initialization
 *
 * 3. **Single Dedicated Instance** (Simplest)
 *    - Run compliance engine as single dedicated service
 *    - Main app instances proxy to dedicated instance
 *    - Easier to manage but requires service mesh
 *
 * 4. **Shared State in Redis** (Future enhancement when needed)
 *    - Store initialization state in Redis
 *    - All instances check Redis before processing requests
 *    - Requires Redis connection and state synchronization logic
 *
 * Environment Variables:
 * - COMPLIANCE_INIT_TIMEOUT: Initialization timeout in ms (default: 30000)
 * - COMPLIANCE_INIT_CRITICAL: If "true", process exits on init failure
 * - INSTANCE_ID: Optional identifier for tracking which instance responded
 *
 * @see COMPLIANCE_SYSTEM_ANALYSIS.md for detailed architectural analysis
 * coderabbit:ignore - Deployment pattern is intentional and documented
 */
async function initializeComplianceEngine() {
  const INIT_TIMEOUT = parseInt(process.env.COMPLIANCE_INIT_TIMEOUT) || 30000; // 30s default
  const CRITICAL_FAILURE =
    process.env.COMPLIANCE_INIT_CRITICAL === "true" || false;

  try {
    // Initializing Real-Time Compliance Engine...
    if (process.env.INSTANCE_ID) {
      console.log(`   Instance ID: ${process.env.INSTANCE_ID}`);
    }
    complianceEngineStatus = "initializing";

    // Dynamic imports with validation
    const complianceAggregatorModule = await import(
      "./services/compliance/complianceAggregator.js"
    );
    const dailyComplianceJobModule = await import(
      "./jobs/dailyComplianceJob.js"
    );

    const complianceAggregator = complianceAggregatorModule.default;
    const dailyComplianceJob = dailyComplianceJobModule.default;

    // Validate that modules expose initialize functions
    if (typeof complianceAggregator?.initialize !== "function") {
      throw new Error(
        "complianceAggregator.initialize is not a function - module may not be properly exported"
      );
    }

    if (typeof dailyComplianceJob?.initialize !== "function") {
      throw new Error(
        "dailyComplianceJob.initialize is not a function - module may not be properly exported"
      );
    }

    // Wrap initialization with timeout
    const initPromise = Promise.all([
      complianceAggregator.initialize(),
      dailyComplianceJob.initialize(),
    ]);

    const timeoutPromise = new Promise((_, reject) => {
      const timeoutId = setTimeout(
        () =>
          reject(
            new Error(
              `Compliance Engine initialization timed out after ${INIT_TIMEOUT}ms`
            )
          ),
        INIT_TIMEOUT
      );
      // Clear timeout if initialization completes first
      initPromise.finally(() => clearTimeout(timeoutId));
    });

    await Promise.race([initPromise, timeoutPromise]);

    // Success - mark as ready
    complianceEngineReady = true;
    complianceEngineStatus = "ready";
    complianceEngineError = null;
    // Real-Time Compliance Engine initialized
  } catch (error) {
    // Failure - mark as failed
    complianceEngineReady = false;
    complianceEngineStatus = "failed";
    complianceEngineError = error.message;

    console.error("❌ Failed to initialize Compliance Engine:", error);
    console.error("   Stack:", error.stack);

    // Log to health checks
    console.error(
      "⚠️ Compliance Engine endpoints will return 503 until manually restarted"
    );

    // Optionally terminate process if configured as critical
    if (CRITICAL_FAILURE) {
      console.error(
        "🚨 CRITICAL: Compliance Engine is marked as critical - terminating process"
      );
      process.exit(1);
    }
  }
}
