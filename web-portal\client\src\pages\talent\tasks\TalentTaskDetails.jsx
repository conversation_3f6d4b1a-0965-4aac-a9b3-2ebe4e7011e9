  import { useState, useEffect } from "react";
  import { useParams, useNavigate } from "react-router-dom";
  import { useAuthStore } from "../../../store/authStore";
  import { useTalentTaskStore } from "../../../store/talentTaskStore";

  const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";
  import { toast } from "sonner";
  import backgroundImage from "../../../images/background.png";
  import {
    HiArrowLeft,
    HiCheckCircle,
    HiClock,
    HiExclamation,
    HiPaperAirplane,
    HiCheckCircle as HiCheck,
    HiX,
  } from "react-icons/hi";
  import { Badge } from "../../../components/ui/badge";
  import RevisionIssuesDisplay from "../../../components/tasks/RevisionIssuesDisplay";

  export default function TalentTaskDetails() {
    const { id } = useParams();
    const navigate = useNavigate();
    const { accessToken } = useAuthStore();
    const {
      selectedTask,
      fetchTaskDetails,
      submitSection,
      toggleChecklistItem,
      completeChecklistItemWithScreenshots,
      addComment,
      updateTaskStatus,
      loading,
      submitLoading,
    } = useTalentTaskStore();

  const [prLinks, setPrLinks] = useState({});
  const [commentMessage, setCommentMessage] = useState("");
  const [submittingSections, setSubmittingSections] = useState({});
  const [uploadProgress, setUploadProgress] = useState({}); // Track upload progress per section
  const [checklistNotes, setChecklistNotes] = useState({});
  const [checklistScreenshots, setChecklistScreenshots] = useState({});
  const [taskPrLink, setTaskPrLink] = useState("");
  const [taskSubmissionImages, setTaskSubmissionImages] = useState([]);
  const [taskImagePreviews, setTaskImagePreviews] = useState([]);

  // State for collapsible checklist descriptions
  const [expandedItems, setExpandedItems] = useState({});

  // State for checklist item revision history expansion
  const [expandedRevisionHistory, setExpandedRevisionHistory] = useState({});

  // State for showing revision response form per checklist item
  const [showRevisionResponseForm, setShowRevisionResponseForm] = useState({});

  // State for revision response form data per item
  const [revisionResponseData, setRevisionResponseData] = useState({});
  // Structure: { [itemNumber]: { comment: "", images: [], imagePreviews: [] } }

  // State for submitting revision response
  const [submittingRevisionResponse, setSubmittingRevisionResponse] = useState(
    {}
  );

  // State for image preview modal
  const [imageModal, setImageModal] = useState({
    isOpen: false,
    images: [],
    currentIndex: 0,
  });

  useEffect(() => {
    if (accessToken && id) {
      fetchTaskDetails(id).catch((error) => {
        toast.error(error.message || "Failed to load task");
        navigate("/talent/tasks/queue");
      });
    }
  }, [accessToken, id]);

  // Toggle description visibility
  const toggleDescription = (itemId) => {
    setExpandedItems((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  // Open image modal
  const openImageModal = (images, startIndex = 0) => {
    setImageModal({
      isOpen: true,
      images,
      currentIndex: startIndex,
    });
  };

  // Close image modal
  const closeImageModal = () => {
    setImageModal({
      isOpen: false,
      images: [],
      currentIndex: 0,
    });
  };

  // Navigate images in modal
  const nextImage = () => {
    setImageModal((prev) => ({
      ...prev,
      currentIndex: (prev.currentIndex + 1) % prev.images.length,
    }));
  };

  const prevImage = () => {
    setImageModal((prev) => ({
      ...prev,
      currentIndex:
        prev.currentIndex === 0
          ? prev.images.length - 1
          : prev.currentIndex - 1,
    }));
  };

  const handleSubmitSection = async (sectionNumber) => {
    const prLink = prLinks[sectionNumber];
    if (!prLink || !prLink.trim()) {
      toast.error("Please enter a PR link");
      return;
    }

    setSubmittingSections((prev) => ({ ...prev, [sectionNumber]: true }));

    // Get screenshot files directly
    const screenshotFiles = prLinks[`${sectionNumber}_images`] || [];

    // Show initial upload status
    if (screenshotFiles.length > 0) {
      setUploadProgress((prev) => ({ ...prev, [sectionNumber]: 0 }));
      toast.info(`Uploading ${screenshotFiles.length} screenshot(s)...`);
    }

    try {
      // Simulate progress for better UX (actual upload happens in store)
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += 15;
        if (progress <= 90) {
          setUploadProgress((prev) => ({ ...prev, [sectionNumber]: progress }));
        }
      }, 200);

      console.log("📤 Sending to submitSection:", {
        taskId: id,
        sectionNumber,
        prLink,
        screenshotFiles,
        count: screenshotFiles.length,
      });

      await submitSection(id, sectionNumber, prLink, screenshotFiles);

      clearInterval(progressInterval);
      setUploadProgress((prev) => ({ ...prev, [sectionNumber]: 100 }));

      toast.success("✅ Section submitted for review!");

      // Clear PR link and images after brief delay to show 100%
      setTimeout(() => {
        setPrLinks((prev) => {
          const updated = { ...prev };
          delete updated[sectionNumber];
          delete updated[`${sectionNumber}_images`];
          delete updated[`${sectionNumber}_previews`];
          return updated;
        });
        setUploadProgress((prev) => {
          const updated = { ...prev };
          delete updated[sectionNumber];
          return updated;
        });
      }, 1000);
    } catch (error) {
      setUploadProgress((prev) => {
        const updated = { ...prev };
        delete updated[sectionNumber];
        return updated;
      });
      toast.error(error.message || "Failed to submit section");
    } finally {
      setSubmittingSections((prev) => ({ ...prev, [sectionNumber]: false }));
    }
  };

  const handleToggleChecklistItem = async (itemId) => {
    try {
      await toggleChecklistItem(id, itemId);
      toast.success("Checklist item updated");
    } catch (error) {
      toast.error(error.message || "Failed to update checklist item");
    }
  };

  const handleCompleteChecklistItem = async (itemNumber) => {
    const screenshots = checklistScreenshots[itemNumber] || [];

    if (screenshots.length === 0) {
      toast.error("Please upload at least one screenshot");
      return;
    }

    try {
      const note = checklistNotes[itemNumber] || "";

      await completeChecklistItemWithScreenshots(
        id,
        itemNumber,
        note,
        screenshots
      );

      toast.success("Checklist item completed successfully!");

      // Clear inputs
      setChecklistNotes({ ...checklistNotes, [itemNumber]: "" });
      setChecklistScreenshots({ ...checklistScreenshots, [itemNumber]: [] });
    } catch (error) {
      toast.error(error.message || "Failed to complete checklist item");
    }
  };

  const handleAddComment = async () => {
    if (!commentMessage.trim()) {
      toast.error("Please enter a comment");
      return;
    }

    try {
      await addComment(id, commentMessage);
      setCommentMessage("");
      toast.success("Comment added");
    } catch (error) {
      toast.error(error.message || "Failed to add comment");
    }
  };

  const handleSubmitForReview = async () => {
    // Validate PR link
    if (!taskPrLink.trim()) {
      toast.error("Please provide a PR/test link");
      return;
    }

    try {
      // Upload images if any
      let uploadedImageUrls = [];
      if (taskSubmissionImages.length > 0) {
        for (const image of taskSubmissionImages) {
          const formData = new FormData();
          formData.append("image", image);

          const uploadResponse = await fetch(
            `${API_URL}/api/tasks/upload-revision-image`,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${accessToken}`,
              },
              credentials: "include",
              body: formData,
            }
          );

          if (uploadResponse.ok) {
            const uploadData = await uploadResponse.json();
            uploadedImageUrls.push(uploadData.image);
          }
        }
      }

      // Use appropriate submission method based on task type
      if (task.hasChecklist) {
        await useTalentTaskStore
          .getState()
          .submitChecklistTask(id, taskPrLink, uploadedImageUrls);
      } else {
        // For section-based tasks, update with PR link and images
        await updateTaskStatus(id, "pending-review", {
          prLink: taskPrLink,
          submissionImages: uploadedImageUrls,
        });
      }

      toast.success("Task submitted for review!");
      setTaskPrLink("");
      setTaskSubmissionImages([]);
      setTaskImagePreviews([]);
    } catch (error) {
      toast.error(error.message || "Failed to submit for review");
    }
  };

  // Toggle revision history for checklist item
  const toggleRevisionHistory = (itemNumber) => {
    setExpandedRevisionHistory((prev) => ({
      ...prev,
      [itemNumber]: !prev[itemNumber],
    }));
  };

  // Toggle revision response form for checklist item
  const toggleRevisionResponseForm = (itemNumber) => {
    setShowRevisionResponseForm((prev) => ({
      ...prev,
      [itemNumber]: !prev[itemNumber],
    }));

    // Initialize form data if not exists
    if (!revisionResponseData[itemNumber]) {
      setRevisionResponseData((prev) => ({
        ...prev,
        [itemNumber]: { comment: "", images: [], imagePreviews: [] },
      }));
    }
  };

  // Handle revision response input change
  const handleRevisionResponseInputChange = (itemNumber, value) => {
    setRevisionResponseData((prev) => ({
      ...prev,
      [itemNumber]: { ...prev[itemNumber], comment: value },
    }));
  };

  // Handle revision response image upload
  const handleRevisionResponseImageUpload = (itemNumber, files) => {
    const fileArray = Array.from(files);
    const previews = fileArray.map((file) => URL.createObjectURL(file));

    setRevisionResponseData((prev) => ({
      ...prev,
      [itemNumber]: {
        ...prev[itemNumber],
        images: [...(prev[itemNumber]?.images || []), ...fileArray],
        imagePreviews: [
          ...(prev[itemNumber]?.imagePreviews || []),
          ...previews,
        ],
      },
    }));
  };

  // Remove revision response image
  const removeRevisionResponseImage = (itemNumber, index) => {
    setRevisionResponseData((prev) => {
      const updated = { ...prev[itemNumber] };
      updated.images = updated.images.filter((_, i) => i !== index);
      updated.imagePreviews = updated.imagePreviews.filter(
        (_, i) => i !== index
      );
      return { ...prev, [itemNumber]: updated };
    });
  };

  // Submit revision response for individual checklist item
  const submitChecklistItemRevisionResponse = async (itemNumber) => {
    const formData = revisionResponseData[itemNumber];

    if (!formData || !formData.comment.trim()) {
      toast.error("Please enter a comment for your response");
      return;
    }

    setSubmittingRevisionResponse((prev) => ({ ...prev, [itemNumber]: true }));

    try {
      // Upload images to backend first
      const uploadedImages = [];
      for (const image of formData.images) {
        const uploadFormData = new FormData();
        uploadFormData.append("image", image);

        const uploadResponse = await fetch(
          `http://localhost:5000/api/tasks/upload-revision-image`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
            credentials: "include",
            body: uploadFormData,
          }
        );

        if (uploadResponse.ok) {
          const uploadData = await uploadResponse.json();
          uploadedImages.push(uploadData.image);
        } else {
          throw new Error("Failed to upload image");
        }
      }

      // Send revision response to backend
      const response = await fetch(
        `${API_URL}/api/tasks/${id}/checklist/${itemNumber}/respond`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
          body: JSON.stringify({
            comment: formData.comment.trim(),
            images: uploadedImages,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to submit response");
      }

      toast.success("Response submitted successfully");

      // Clear form and close
      setRevisionResponseData((prev) => ({
        ...prev,
        [itemNumber]: { comment: "", images: [], imagePreviews: [] },
      }));
      setShowRevisionResponseForm((prev) => ({ ...prev, [itemNumber]: false }));

      // Reload task to show updated revision history
      await fetchTaskDetails(id);
    } catch (error) {
      console.error("Submit revision response error:", error);
      toast.error(error.message || "Failed to submit response");
    } finally {
      setSubmittingRevisionResponse((prev) => ({
        ...prev,
        [itemNumber]: false,
      }));
    }
  };

  if (loading || !selectedTask) {
    return (
      <div
        className="min-h-screen flex items-center justify-center"
        style={{
          backgroundImage: `url(${backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundAttachment: "fixed",
        }}
      >
        <div className="text-white text-xl">Loading task details...</div>
      </div>
    );
  }

  const task = selectedTask;

  // Neutral badge style for all badges
  const neutralBadgeStyle = "text-white/70";
  const priorityColors = {};
  const statusColors = {};

  // Calculate progress
  const totalSections = task.sections?.length || 0;
  const completedSections =
    task.sections?.filter((s) => s.status === "completed").length || 0;
  const sectionProgress =
    totalSections > 0
      ? Math.round((completedSections / totalSections) * 100)
      : 0;

  const totalChecklist = task.checklist?.length || 0;
  const completedChecklist =
    task.checklist?.filter((item) => item.completed).length || 0;
  const checklistProgress =
    totalChecklist > 0
      ? Math.round((completedChecklist / totalChecklist) * 100)
      : 0;

  const overallProgress = task.hasSections
    ? sectionProgress
    : task.hasChecklist
    ? checklistProgress
    : 0;

  return (
    <div
      className="min-h-screen relative overflow-hidden"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundAttachment: "fixed",
      }}
    >
      {/* Dark overlay */}
      <div className="absolute inset-0 bg-black/40"></div>

        {/* Minimal background overlays */}
        <div
          className="absolute top-0 right-0 w-96 h-full pointer-events-none"
          style={{
            background:
              "radial-gradient(ellipse at top right, rgba(255, 255, 255, 0.02), transparent 60%)",
          }}
        ></div>
        <div
          className="absolute top-0 left-0 w-64 h-full pointer-events-none"
          style={{
            background:
              "radial-gradient(ellipse at top left, rgba(255, 255, 255, 0.015), transparent 60%)",
          }}
        ></div>

        {/* Main Content */}
        <main className="w-full px-4 sm:px-6 lg:px-8 xl:px-20 pt-2 sm:pt-3 pb-4 sm:pb-6 relative z-10">
          <div className="relative z-10">
            {/* Back Button */}
            <button
              onClick={() => navigate(-1)}
              className="flex items-center gap-2 mb-4 transition-all duration-300 hover:scale-[1.02]"
              style={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              <HiArrowLeft className="w-5 h-5" />
              <span>Back</span>
            </button>

            {/* 2-Column Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {/* LEFT COLUMN - Task Info & Work Area (2/3 width) */}
              <div className="lg:col-span-2 space-y-4">
                {/* Task Header Card */}
                <div
                  className="rounded-2xl border p-4 backdrop-blur-xl"
                  style={{
                    background: "rgba(255, 255, 255, 0.03)",
                    borderColor: "rgba(255, 255, 255, 0.1)",
                    boxShadow: "0 4px 16px 0 rgba(0, 0, 0, 0.3)",
                  }}
                >
                  <h1 className="text-2xl font-bold text-white mb-2">
                    {task.title}
                  </h1>
                  <p
                    className="text-sm mb-3"
                    style={{ color: "rgba(255, 255, 255, 0.6)" }}
                  >
                    {task.description}
                  </p>

                {/* Task Attachments */}
                {task.attachments && task.attachments.length > 0 && (
                  <div
                    className="mb-3 p-3 rounded-lg"
                    style={{
                      background: "rgba(255, 255, 255, 0.03)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                    }}
                  >
                    <h3 className="text-xs font-semibold text-slate-300 mb-2">
                      📎 Attachments ({task.attachments.length})
                    </h3>
                    <div className="space-y-2">
                      {task.attachments.map((attachment, idx) => (
                        <div key={idx} className="space-y-2">
                          {/* View Button */}
                          <button
                            onClick={async () => {
                              try {
                                // Use backend proxy endpoint for viewing
                                const response = await fetch(
                                  `${API_URL}/api/tasks/${task._id}/attachment/${attachment._id}`,
                                  {
                                    headers: {
                                      Authorization: `Bearer ${accessToken}`,
                                    },
                                  }
                                );

                                if (!response.ok) {
                                  throw new Error("Failed to fetch file");
                                }

                                // Convert to blob and open in new tab
                                const blob = await response.blob();
                                const url = window.URL.createObjectURL(blob);
                                window.open(url, "_blank");

                                // Clean up the blob URL after a delay
                                setTimeout(
                                  () => window.URL.revokeObjectURL(url),
                                  100
                                );
                              } catch (error) {
                                console.error("View file error:", error);
                                toast.error(
                                  "Failed to view file. Please try again."
                                );
                              }
                            }}
                            className="w-full flex items-center gap-2 p-2 rounded transition-all hover:scale-[1.02]"
                            style={{
                              background: "rgba(255, 255, 255, 0.05)",
                              border: "1px solid rgba(255, 255, 255, 0.1)",
                            }}
                          >
                            {attachment.fileType === "pdf" ? (
                              <svg
                                className="w-5 h-5 text-red-400 flex-shrink-0"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            ) : (
                              <svg
                                className="w-5 h-5 text-blue-400 flex-shrink-0"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            )}
                            <div className="flex-1 min-w-0 text-left">
                              <p className="text-sm text-white font-medium truncate">
                                {attachment.filename}
                              </p>
                              <p className="text-xs text-slate-400">
                                View in browser
                              </p>
                            </div>
                            <svg
                              className="w-5 h-5 text-blue-400 flex-shrink-0"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                              />
                            </svg>
                          </button>

                            {/* Download Button */}
                            <button
                              onClick={async () => {
                                try {
                                  // Fetch file as blob
                                  const response = await fetch(attachment.url);

                                  if (!response.ok) {
                                    throw new Error("Failed to download file");
                                  }

                                // Convert to blob and trigger download
                                const blob = await response.blob();
                                const url = window.URL.createObjectURL(blob);
                                const link = document.createElement("a");
                                link.href = url;
                                link.download = attachment.filename;
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);

                                // Clean up the blob URL
                                window.URL.revokeObjectURL(url);
                              } catch (error) {
                                console.error("Download file error:", error);
                                toast.error(
                                  "Failed to download file. Please try again."
                                );
                              }
                            }}
                            className="w-full flex items-center gap-2 p-2 rounded transition-all hover:scale-[1.02]"
                            style={{
                              background: "rgba(255, 255, 255, 0.05)",
                              border: "1px solid rgba(34, 197, 94, 0.3)",
                            }}
                          >
                            <svg
                              className="w-5 h-5 text-green-400 flex-shrink-0"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                              />
                            </svg>
                            <div className="flex-1 text-left">
                              <p className="text-sm text-white font-medium">
                                Download
                              </p>
                            </div>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-2 flex-wrap">
                  <span
                    className="px-3 py-1 rounded-md text-sm font-medium capitalize"
                    style={{
                      background: "rgba(255, 255, 255, 0.05)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      color: "rgba(255, 255, 255, 0.7)",
                    }}
                  >
                    {task.status.replace("-", " ")}
                  </span>
                  <span
                    className="px-3 py-1 rounded-md text-sm font-medium capitalize"
                    style={{
                      background: "rgba(255, 255, 255, 0.05)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      color: "rgba(255, 255, 255, 0.7)",
                    }}
                  >
                    {task.priority}
                  </span>
                  <span
                    className="px-3 py-1 rounded-md text-sm font-medium capitalize"
                    style={{
                      background: "rgba(255, 255, 255, 0.05)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      color: "rgba(255, 255, 255, 0.7)",
                    }}
                  >
                    {task.complexity}
                  </span>
                  <span
                    className="px-3 py-1 rounded-md text-sm font-medium"
                    style={{
                      background: "rgba(255, 255, 255, 0.05)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      color: "rgba(255, 255, 255, 0.7)",
                    }}
                  >
                    {task.xpValue} XP
                  </span>
                </div>

                {/* Progress Bar */}
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-1">
                    <span
                      className="text-xs"
                      style={{ color: "rgba(255, 255, 255, 0.6)" }}
                    >
                      Progress
                    </span>
                    <span className="text-xs font-semibold text-white">
                      {overallProgress}%
                    </span>
                  </div>
                  <div
                    className="w-full h-2 rounded-full overflow-hidden"
                    style={{ background: "rgba(255, 255, 255, 0.1)" }}
                  >
                    <div
                      className="h-full rounded-full transition-all duration-500"
                      style={{
                        width: `${overallProgress}%`,
                        background: "rgba(255, 255, 255, 0.3)",
                      }}
                    />
                  </div>
                </div>

                  {/* Submit for Review Button */}
                  {task.status === "in-progress" && overallProgress === 100 && (
                    <div className="mt-3 space-y-2">
                      <label className="text-xs text-slate-300">
                        PR/Test Link <span className="text-white">*</span>
                      </label>
                      <input
                        type="url"
                        value={taskPrLink}
                        onChange={(e) => setTaskPrLink(e.target.value)}
                        placeholder="https://github.com/..."
                        className="w-full px-3 py-2 rounded text-sm text-white placeholder-slate-500 focus:outline-none transition-all"
                        style={{
                          background: "rgba(255, 255, 255, 0.05)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                        }}
                      />
                      <button
                        onClick={handleSubmitForReview}
                        disabled={submitLoading || !taskPrLink.trim()}
                        className="w-full py-2 px-4 text-white text-sm rounded-lg font-semibold transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 hover:opacity-80"
                        style={{
                          background: "rgba(255, 255, 255, 0.1)",
                          border: "1px solid rgba(255, 255, 255, 0.15)",
                        }}
                      >
                        <HiCheckCircle className="w-4 h-4" />
                        {submitLoading ? "Submitting..." : "Submit for Review"}
                      </button>
                    </div>
                  )}
                {/* Submit for Review Button */}
                {task.status === "in-progress" && overallProgress === 100 && (
                  <div className="mt-3 space-y-2">
                    <label className="text-xs text-slate-300">
                      PR/Test Link <span className="text-white">*</span>
                    </label>
                    <input
                      type="url"
                      value={taskPrLink}
                      onChange={(e) => setTaskPrLink(e.target.value)}
                      placeholder="https://github.com/..."
                      className="w-full px-3 py-2 rounded text-sm text-white placeholder-slate-500 focus:outline-none transition-all"
                      style={{
                        background: "rgba(255, 255, 255, 0.05)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                      }}
                    />

                    {/* Image Upload */}
                    <div>
                      <label className="text-xs text-slate-300">
                        Screenshots/Images (Optional)
                      </label>
                      <input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => {
                          const files = Array.from(e.target.files);
                          setTaskSubmissionImages(files);

                          // Generate previews
                          const previews = files.map((file) =>
                            URL.createObjectURL(file)
                          );
                          setTaskImagePreviews(previews);
                        }}
                        className="w-full px-3 py-2 rounded text-sm text-white focus:outline-none transition-all file:mr-2 file:py-1 file:px-3 file:rounded file:border-0 file:text-xs file:bg-white/10 file:text-slate-300 hover:file:bg-white/20"
                        style={{
                          background: "rgba(255, 255, 255, 0.05)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                        }}
                      />

                      {/* Image Previews */}
                      {taskImagePreviews.length > 0 && (
                        <div className="flex gap-2 mt-2 flex-wrap">
                          {taskImagePreviews.map((preview, idx) => (
                            <div key={idx} className="relative">
                              <img
                                src={preview}
                                alt={`Preview ${idx + 1}`}
                                className="w-16 h-16 object-cover rounded border border-white/20"
                              />
                              <button
                                onClick={() => {
                                  setTaskSubmissionImages((prev) =>
                                    prev.filter((_, i) => i !== idx)
                                  );
                                  setTaskImagePreviews((prev) =>
                                    prev.filter((_, i) => i !== idx)
                                  );
                                }}
                                className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    <button
                      onClick={handleSubmitForReview}
                      disabled={submitLoading || !taskPrLink.trim()}
                      className="w-full py-2 px-4 text-white text-sm rounded-lg font-semibold transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 hover:opacity-80 relative overflow-hidden"
                      style={{
                        background: submitLoading
                          ? "linear-gradient(90deg, rgba(34, 197, 94, 0.2) 0%, rgba(34, 197, 94, 0.3) 100%)"
                          : "rgba(255, 255, 255, 0.1)",
                        border: "1px solid rgba(255, 255, 255, 0.15)",
                      }}
                    >
                      {submitLoading ? (
                        <>
                          <svg
                            className="animate-spin h-4 w-4"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            />
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            />
                          </svg>
                          Submitting...
                        </>
                      ) : (
                        <>
                          <HiCheckCircle className="w-4 h-4" />
                          Submit for Review
                        </>
                      )}
                    </button>
                  </div>
                )}

                  {task.status === "needs-revision" && (
                    <div
                      className="mt-3 p-3 rounded-lg"
                      style={{
                        background: "rgba(255, 255, 255, 0.05)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                      }}
                    >
                      <div className="flex items-start gap-2">
                        <HiExclamation className="w-5 h-5 text-slate-300 flex-shrink-0 mt-0.5" />
                        <div className="flex-1">
                          <h3 className="text-white font-semibold text-sm mb-1">
                            Needs Revision
                          </h3>
                          <p className="text-slate-300 text-xs">
                            Check admin comments and update the required sections.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                {task.status === "needs-revision" && (
                  <div
                    className="mt-3 p-3 rounded-lg"
                    style={{
                      background: "rgba(255, 100, 0, 0.08)",
                      border: "1px solid rgba(255, 100, 0, 0.2)",
                    }}
                  >
                    <div className="flex items-start gap-2">
                      <HiExclamation className="w-5 h-5 text-orange-400 flex-shrink-0 mt-0.5" />
                      <div className="flex-1">
                        <h3 className="text-orange-400 font-semibold text-sm mb-1">
                          Needs Revision
                        </h3>
                        <p className="text-orange-300 text-xs">
                          {task.hasChecklist
                            ? "Check the revision notes for each checklist item below and resubmit after fixing."
                            : "Check admin comments and update the required sections."}
                        </p>
                        {task.feedback && (
                          <div className="mt-2 p-2 rounded bg-black/20">
                            <p className="text-xs text-orange-200 font-medium mb-1">
                              Admin Feedback:
                            </p>
                            <p className="text-xs text-slate-300">
                              {task.feedback}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                  {/* Show submitted PR link */}
                  {(task.status === "pending-review" ||
                    task.status === "completed") &&
                    task.prLink && (
                      <div
                        className="mt-3 p-3 rounded-lg"
                        style={{
                          background: "rgba(255, 255, 255, 0.05)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                        }}
                      >
                        <p className="text-xs text-slate-400 mb-1">
                          Submitted PR/Test Link:
                        </p>
                        <a
                          href={task.prLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-slate-300 hover:text-white text-xs break-all underline"
                        >
                          {task.prLink}
                        </a>
                      </div>
                    )}
                </div>

              {/* Sections (if task is section-based) */}
              {task.hasSections &&
                task.sections &&
                task.sections.length > 0 && (
                  <div
                    className="rounded-2xl border p-4 backdrop-blur-xl"
                    style={{
                      background: "rgba(255, 255, 255, 0.03)",
                      borderColor: "rgba(255, 255, 255, 0.1)",
                      boxShadow: "0 4px 16px 0 rgba(0, 0, 0, 0.3)",
                    }}
                  >
                    <h2 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                      <HiCheckCircle className="w-5 h-5 text-slate-300" />
                      Task Sections
                    </h2>
                    <div className="space-y-3">
                      {task.sections.map((section, index) => (
                        <div
                          key={section._id}
                          className={`border rounded-lg p-3 ${
                            section.status === "locked"
                              ? "bg-slate-800/30 border-slate-700/50 opacity-60"
                              : "bg-slate-800/50 border-slate-700"
                          }`}
                        >
                          <div className="mb-2">
                            <div className="flex items-center gap-2 mb-1 flex-wrap">
                              <span className="text-slate-400 font-mono text-xs">
                                #{section.number}
                              </span>
                              <h3 className="text-sm font-semibold text-white">
                                {section.title}
                              </h3>
                              {section.status === "locked" && (
                                <span
                                  className="px-2 py-0.5 rounded text-xs font-medium"
                                  style={{
                                    background: "rgba(255, 255, 255, 0.05)",
                                    border:
                                      "1px solid rgba(255, 255, 255, 0.1)",
                                    color: "rgba(255, 255, 255, 0.5)",
                                  }}
                                >
                                  Locked
                                </span>
                              )}
                              {section.status === "pending-review" && (
                                <span
                                  className="px-2 py-0.5 rounded text-xs font-medium"
                                  style={{
                                    background: "rgba(255, 255, 255, 0.05)",
                                    border:
                                      "1px solid rgba(255, 255, 255, 0.1)",
                                    color: "rgba(255, 255, 255, 0.7)",
                                  }}
                                >
                                  Review
                                </span>
                              )}
                              {section.status === "completed" && (
                                <span
                                  className="px-2 py-0.5 rounded text-xs font-medium flex items-center gap-1"
                                  style={{
                                    background: "rgba(255, 255, 255, 0.05)",
                                    border:
                                      "1px solid rgba(255, 255, 255, 0.1)",
                                    color: "rgba(255, 255, 255, 0.7)",
                                  }}
                                >
                                  <HiCheck className="w-3 h-3" />
                                  Done
                                </span>
                              )}
                              {section.status === "needs-revision" && (
                                <span
                                  className="px-2 py-0.5 rounded text-xs font-medium"
                                  style={{
                                    background: "rgba(255, 255, 255, 0.05)",
                                    border:
                                      "1px solid rgba(255, 255, 255, 0.1)",
                                    color: "rgba(255, 255, 255, 0.7)",
                                  }}
                                >
                                  Revision
                                </span>
                              )}
                            </div>
                            {section.description && (
                              <p
                                className="text-xs mb-2"
                                style={{ color: "rgba(255, 255, 255, 0.6)" }}
                              >
                                {section.description}
                              </p>
                            )}
                            {/* Revision Issues Display */}
                            {section.status === "needs-revision" && (
                              <RevisionIssuesDisplay section={section} />
                            )}
                          </div>

                          {/* PR Link Submission */}
                          {section.status !== "locked" &&
                            section.status !== "completed" &&
                            section.status !== "pending-review" && (
                              <div className="mt-2 space-y-2">
                                <label className="text-xs text-slate-300">
                                  Pull Request Link
                                </label>
                                <input
                                  type="url"
                                  value={
                                    prLinks[section.number] ||
                                    section.prLink ||
                                    ""
                                  }
                                  onChange={(e) =>
                                    setPrLinks((prev) => ({
                                      ...prev,
                                      [section.number]: e.target.value,
                                    }))
                                  }
                                  placeholder="https://github.com/..."
                                  className="w-full px-3 py-1.5 rounded text-sm text-white placeholder-slate-500 focus:outline-none"
                                  style={{
                                    background: "rgba(255, 255, 255, 0.05)",
                                    border:
                                      "1px solid rgba(255, 255, 255, 0.1)",
                                  }}
                                />

                                {/* Image Upload for Section */}
                                <div>
                                  <label className="text-xs text-slate-300">
                                    Screenshots/Images (Optional)
                                  </label>
                                  <input
                                    type="file"
                                    accept="image/*"
                                    multiple
                                    onChange={(e) => {
                                      const files = Array.from(e.target.files);
                                      const previews = files.map((file) =>
                                        URL.createObjectURL(file)
                                      );
                                      // Store section-specific images
                                      setPrLinks((prev) => ({
                                        ...prev,
                                        [`${section.number}_images`]: files,
                                        [`${section.number}_previews`]:
                                          previews,
                                      }));
                                    }}
                                    className="w-full px-3 py-2 rounded text-sm text-white focus:outline-none transition-all file:mr-2 file:py-1 file:px-3 file:rounded file:border-0 file:text-xs file:bg-white/10 file:text-slate-300 hover:file:bg-white/20"
                                    style={{
                                      background: "rgba(255, 255, 255, 0.05)",
                                      border:
                                        "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                  />

                                  {/* Image Previews */}
                                  {prLinks[`${section.number}_previews`] &&
                                    prLinks[`${section.number}_previews`]
                                      .length > 0 && (
                                      <div className="flex gap-2 mt-2 flex-wrap">
                                        {prLinks[
                                          `${section.number}_previews`
                                        ].map((preview, idx) => (
                                          <div key={idx} className="relative">
                                            <img
                                              src={preview}
                                              alt={`Preview ${idx + 1}`}
                                              className="w-16 h-16 object-cover rounded border border-white/20"
                                            />
                                            <button
                                              onClick={() => {
                                                const images = prLinks[
                                                  `${section.number}_images`
                                                ].filter((_, i) => i !== idx);
                                                const previews = prLinks[
                                                  `${section.number}_previews`
                                                ].filter((_, i) => i !== idx);
                                                setPrLinks((prev) => ({
                                                  ...prev,
                                                  [`${section.number}_images`]:
                                                    images,
                                                  [`${section.number}_previews`]:
                                                    previews,
                                                }));
                                              }}
                                              className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600"
                                            >
                                              ×
                                            </button>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                </div>

                                <button
                                  onClick={() =>
                                    handleSubmitSection(section.number)
                                  }
                                  disabled={
                                    submittingSections[section.number] ||
                                    !prLinks[section.number]?.trim()
                                  }
                                  className="w-full px-4 py-2 text-white text-sm rounded font-semibold transition-all disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden"
                                  style={{
                                    background: submittingSections[
                                      section.number
                                    ]
                                      ? "linear-gradient(90deg, rgba(34, 197, 94, 0.2) 0%, rgba(34, 197, 94, 0.3) 100%)"
                                      : "rgba(255, 255, 255, 0.1)",
                                  }}
                                  onMouseEnter={(e) => {
                                    if (
                                      !submittingSections[section.number] &&
                                      prLinks[section.number]?.trim()
                                    )
                                      e.target.style.background =
                                        "rgba(255, 255, 255, 0.15)";
                                  }}
                                  onMouseLeave={(e) => {
                                    if (!submittingSections[section.number])
                                      e.target.style.background =
                                        "rgba(255, 255, 255, 0.1)";
                                  }}
                                >
                                  {/* Progress bar */}
                                  {uploadProgress[section.number] !==
                                    undefined && (
                                    <div
                                      className="absolute inset-0 bg-green-600/30 transition-all duration-300"
                                      style={{
                                        width: `${
                                          uploadProgress[section.number]
                                        }%`,
                                      }}
                                    />
                                  )}

                                  {/* Button content */}
                                  <span className="relative z-10 flex items-center justify-center gap-2">
                                    {submittingSections[section.number] ? (
                                      <>
                                        <svg
                                          className="animate-spin h-4 w-4"
                                          xmlns="http://www.w3.org/2000/svg"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                        >
                                          <circle
                                            className="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            strokeWidth="4"
                                          />
                                          <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                          />
                                        </svg>
                                        {uploadProgress[section.number] !==
                                        undefined
                                          ? `Uploading... ${
                                              uploadProgress[section.number]
                                            }%`
                                          : "Uploading..."}
                                      </>
                                    ) : uploadProgress[section.number] ===
                                      100 ? (
                                      <>
                                        <svg
                                          className="h-4 w-4 text-green-400"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M5 13l4 4L19 7"
                                          />
                                        </svg>
                                        Submitted ✓
                                      </>
                                    ) : (
                                      "Submit Section"
                                    )}
                                  </span>
                                </button>
                              </div>
                            )}

                          {/* Show submitted PR link and screenshots */}
                          {section.prLink && (
                            <div className="mt-2 p-2 bg-slate-800/30 border border-slate-700 rounded">
                              <p className="text-xs text-slate-400 mb-1">
                                Submitted PR:
                              </p>
                              <a
                                href={section.prLink}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-slate-300 hover:text-white text-xs break-all underline"
                              >
                                {section.prLink}
                              </a>

                              {/* Show submitted screenshots */}
                              {(() => {
                                console.log(
                                  "Section screenshots:",
                                  section.screenshots
                                );
                                return (
                                  section.screenshots &&
                                  section.screenshots.length > 0
                                );
                              })() && (
                                <div className="mt-2">
                                  <p className="text-xs text-slate-400 mb-1">
                                    Screenshots ({section.screenshots.length}):
                                  </p>
                                  <div className="flex gap-2 flex-wrap">
                                    {section.screenshots.map(
                                      (screenshot, idx) => {
                                        const screenshotUrl =
                                          typeof screenshot === "string"
                                            ? screenshot
                                            : screenshot?.url;
                                        console.log(
                                          `Screenshot ${idx}:`,
                                          screenshot,
                                          "URL:",
                                          screenshotUrl
                                        );
                                        return screenshotUrl ? (
                                          <button
                                            key={idx}
                                            onClick={() =>
                                              openImageModal(
                                                section.screenshots
                                                  .map((s) =>
                                                    typeof s === "string"
                                                      ? s
                                                      : s?.url
                                                  )
                                                  .filter(Boolean),
                                                idx
                                              )
                                            }
                                            className="block group relative focus:outline-none"
                                          >
                                            <img
                                              src={screenshotUrl}
                                              alt={`Screenshot ${idx + 1}`}
                                              className="w-20 h-20 object-cover rounded border-2 border-slate-600 hover:border-slate-400 transition-colors"
                                              onError={(e) => {
                                                console.error(
                                                  "Image load error:",
                                                  screenshotUrl
                                                );
                                                e.target.src =
                                                  "https://via.placeholder.com/80x80?text=Error";
                                              }}
                                            />
                                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                                              <span className="text-white text-xs">
                                                View
                                              </span>
                                            </div>
                                          </button>
                                        ) : null;
                                      }
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Checklist (if task is checklist-based) */}
                {task.hasChecklist &&
                  task.checklist &&
                  task.checklist.length > 0 && (
                    <div
                      className="rounded-2xl border p-4 backdrop-blur-xl max-h-[600px] overflow-y-auto"
                      style={{
                        background: "rgba(255, 255, 255, 0.03)",
                        borderColor: "rgba(255, 255, 255, 0.1)",
                        boxShadow: "0 4px 16px 0 rgba(0, 0, 0, 0.3)",
                      }}
                    >
                      <h2 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                        <HiCheckCircle className="w-5 h-5 text-slate-300" />
                        Checklist
                      </h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {task.checklist.map((item, index) => (
                          <div
                            key={item._id}
                            className="p-3 rounded-lg"
                            style={{
                              background: "rgba(255, 255, 255, 0.03)",
                              border: "1px solid rgba(255, 255, 255, 0.1)",
                            }}
                          >
                            <div className="flex items-start gap-2">
                              <div
                                className="flex-shrink-0 w-4 h-4 rounded border-2 flex items-center justify-center"
                                style={{
                                  background: item.completed
                                    ? "rgba(255, 255, 255, 0.2)"
                                    : "transparent",
                                  borderColor: item.completed
                                    ? "rgba(255, 255, 255, 0.3)"
                                    : "rgba(255, 255, 255, 0.2)",
                                }}
                              >
                                {item.completed && (
                                  <HiCheck className="w-3 h-3 text-white" />
                                )}
                              </div>
                              <div className="flex-1">
                                <p
                                  className={`text-xs font-medium ${
                                    item.completed
                                      ? "text-slate-400 line-through"
                                      : "text-slate-200"
                                  }`}
                                >
                                  {item.number}. {item.description}
                                </p>
              {/* Checklist (if task is checklist-based) */}
              {task.hasChecklist &&
                task.checklist &&
                task.checklist.length > 0 && (
                  <div
                    className="rounded-2xl border p-4 backdrop-blur-xl max-h-[600px] overflow-y-auto"
                    style={{
                      background: "rgba(255, 255, 255, 0.03)",
                      borderColor: "rgba(255, 255, 255, 0.1)",
                      boxShadow: "0 4px 16px 0 rgba(0, 0, 0, 0.3)",
                    }}
                  >
                    <h2 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                      <HiCheckCircle className="w-5 h-5 text-slate-300" />
                      Checklist
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {task.checklist.map((item, index) => (
                        <div
                          key={item._id}
                          className="p-3 rounded-lg"
                          style={{
                            background: "rgba(255, 255, 255, 0.03)",
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                          }}
                        >
                          <div className="flex items-start gap-2">
                            <div
                              className="flex-shrink-0 w-4 h-4 rounded border-2 flex items-center justify-center"
                              style={{
                                background: item.completed
                                  ? "rgba(255, 255, 255, 0.2)"
                                  : "transparent",
                                borderColor: item.completed
                                  ? "rgba(255, 255, 255, 0.3)"
                                  : "rgba(255, 255, 255, 0.2)",
                              }}
                            >
                              {item.completed && (
                                <HiCheck className="w-3 h-3 text-white" />
                              )}
                            </div>
                            <div className="flex-1">
                              {/* Title - Clickable to expand/collapse description */}
                              <div
                                onClick={() =>
                                  item.description &&
                                  item.title &&
                                  toggleDescription(item._id)
                                }
                                className={`text-xs font-medium ${
                                  item.completed
                                    ? "text-slate-400 line-through"
                                    : "text-slate-200"
                                } ${
                                  item.description && item.title
                                    ? "cursor-pointer hover:text-white transition-colors"
                                    : ""
                                }`}
                              >
                                <span>
                                  {item.number}.{" "}
                                  {item.title || item.description}
                                </span>
                                {item.description && item.title && (
                                  <svg
                                    className={`inline-block w-3 h-3 ml-1 transition-transform ${
                                      expandedItems[item._id]
                                        ? "rotate-180"
                                        : ""
                                    }`}
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M19 9l-7 7-7-7"
                                    />
                                  </svg>
                                )}
                              </div>

                              {/* Collapsible Description */}
                              {item.description &&
                                item.title &&
                                expandedItems[item._id] && (
                                  <div
                                    className="mt-2 p-2 rounded text-xs text-slate-300"
                                    style={{
                                      background: "rgba(255, 255, 255, 0.05)",
                                      border:
                                        "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                  >
                                    {item.description}
                                  </div>
                                )}

                                {/* Reference Images from Admin */}
                                {item.referenceImages &&
                                  item.referenceImages.length > 0 && (
                                    <div
                                      className="mt-2 p-2 rounded"
                                      style={{
                                        background: "rgba(255, 255, 255, 0.03)",
                                        border:
                                          "1px solid rgba(255, 255, 255, 0.1)",
                                      }}
                                    >
                                      <p className="text-[10px] text-slate-300 font-semibold mb-1.5">
                                        📎 ({item.referenceImages.length})
                                      </p>
                                      <div className="flex gap-2 flex-wrap">
                                        {item.referenceImages.map((img, idx) => {
                                          const imageUrl =
                                            img.url || img.imageUrl;
                                          return imageUrl ? (
                                            <a
                                              key={idx}
                                              href={imageUrl}
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              className="block group relative"
                                            >
                                              <img
                                                src={imageUrl}
                                                alt={
                                                  img.fileName ||
                                                  `Reference ${idx + 1}`
                                                }
                                                className="w-20 h-20 object-cover rounded border-2 transition-colors"
                                                style={{
                                                  borderColor:
                                                    "rgba(255, 255, 255, 0.15)",
                                                }}
                                                onError={(e) => {
                                                  e.target.src =
                                                    "https://via.placeholder.com/80x80?text=Image";
                                                }}
                                              />
                                              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                                                <span className="text-white text-xs">
                                                  View
                                                </span>
                                              </div>
                                            </a>
                                          ) : null;
                                        })}
                                      </div>
                                    </div>
                                  )}
                              {/* Reference Images from Admin */}
                              {item.referenceImages &&
                                item.referenceImages.length > 0 && (
                                  <div
                                    className="mt-2 p-2 rounded"
                                    style={{
                                      background: "rgba(255, 255, 255, 0.03)",
                                      border:
                                        "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                  >
                                    <p className="text-[10px] text-slate-300 font-semibold mb-1.5">
                                      📎 ({item.referenceImages.length})
                                    </p>
                                    <div className="flex gap-2 flex-wrap">
                                      {item.referenceImages.map((img, idx) => {
                                        const imageUrl =
                                          img.url || img.imageUrl;
                                        return imageUrl ? (
                                          <button
                                            key={idx}
                                            onClick={() =>
                                              openImageModal(
                                                item.referenceImages
                                                  .map(
                                                    (i) => i.url || i.imageUrl
                                                  )
                                                  .filter(Boolean),
                                                idx
                                              )
                                            }
                                            className="block group relative focus:outline-none"
                                          >
                                            <img
                                              src={imageUrl}
                                              alt={
                                                img.filename ||
                                                `Reference ${idx + 1}`
                                              }
                                              className="w-20 h-20 object-cover rounded border-2 transition-colors"
                                              style={{
                                                borderColor:
                                                  "rgba(255, 255, 255, 0.15)",
                                              }}
                                              onError={(e) => {
                                                e.target.src =
                                                  "https://via.placeholder.com/80x80?text=Image";
                                              }}
                                            />
                                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                                              <span className="text-white text-xs">
                                                View
                                              </span>
                                            </div>
                                          </button>
                                        ) : null;
                                      })}
                                    </div>
                                  </div>
                                )}

                                {/* Completed item details */}
                                {item.completed && (
                                  <div
                                    className="mt-2 p-2 rounded space-y-1.5"
                                    style={{
                                      background: "rgba(255, 255, 255, 0.03)",
                                      border:
                                        "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                  >
                                    <p className="text-[10px] text-slate-300 font-semibold mb-1">
                                      ✓ Completed
                                    </p>
                              {/* Work History Timeline - Shows submitted work (either completed OR needs revision) */}
                              {(item.completed ||
                                (item.needsRevision &&
                                  item.screenshots &&
                                  item.screenshots.length > 0)) && (
                                <div className="mt-2 space-y-2">
                                  {/* Your Previous Submission */}
                                  <div
                                    className="p-2 rounded"
                                    style={{
                                      background: item.needsRevision
                                        ? "rgba(251, 191, 36, 0.1)"
                                        : "rgba(34, 197, 94, 0.1)",
                                      border: item.needsRevision
                                        ? "1px solid rgba(251, 191, 36, 0.2)"
                                        : "1px solid rgba(34, 197, 94, 0.2)",
                                    }}
                                  >
                                    <p
                                      className={`text-[10px] font-semibold mb-1 ${
                                        item.needsRevision
                                          ? "text-yellow-400"
                                          : "text-green-400"
                                      }`}
                                    >
                                      {item.needsRevision
                                        ? "⚠ Your Previous Submission (Needs Revision)"
                                        : "✓ Your Submitted Work"}
                                    </p>

                                    {item.completionNote && (
                                      <div className="p-1.5 bg-slate-900/50 rounded">
                                        <p className="text-[10px] text-slate-400">
                                          Note:
                                        </p>
                                        <p className="text-xs text-slate-300">
                                          {item.completionNote}
                                        </p>
                                      </div>
                                    )}
                                    {item.completionNote && (
                                      <div className="p-1.5 bg-slate-900/50 rounded mb-1.5">
                                        <p className="text-[10px] text-slate-400">
                                          Your Note:
                                        </p>
                                        <p className="text-xs text-slate-300">
                                          {item.completionNote}
                                        </p>
                                      </div>
                                    )}

                                    {/* Screenshots Section - Always show if item is completed */}
                                    <div className="p-1.5 bg-slate-900/50 rounded">
                                      <p className="text-[10px] text-slate-400 mb-1.5">
                                        Screenshots:{" "}
                                        {item.screenshots?.length || 0}
                                      </p>
                                      {item.screenshots &&
                                      item.screenshots.length > 0 ? (
                                        <div className="flex gap-2 flex-wrap">
                                          {item.screenshots.map(
                                            (screenshot, idx) => (
                                              <a
                                                key={idx}
                                                href={screenshot.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="block group relative"
                                              >
                                                <img
                                                  src={screenshot.url}
                                                  alt={`Screenshot ${idx + 1}`}
                                                  className="w-24 h-24 object-cover rounded border-2 transition-colors"
                                                  style={{
                                                    borderColor:
                                                      "rgba(255, 255, 255, 0.15)",
                                                  }}
                                                  onError={(e) => {
                                                    e.target.src =
                                                      "https://via.placeholder.com/96x96?text=Load+Error";
                                                    e.target.style.borderColor =
                                                      "rgba(255, 255, 255, 0.15)";
                                                  }}
                                                />
                                                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                                                  <span className="text-white text-xs">
                                                    View
                                                  </span>
                                                </div>
                                              </a>
                                            )
                                          )}
                                        </div>
                                      ) : (
                                        <p className="text-xs text-slate-500 italic">
                                          No screenshots uploaded
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                )}
                                    {/* Your Screenshots - ALWAYS show */}
                                    <div className="p-1.5 bg-slate-900/50 rounded">
                                      <p className="text-[10px] text-slate-400 mb-1.5">
                                        Your Screenshots:{" "}
                                        {item.screenshots?.length || 0}
                                      </p>
                                      {item.screenshots &&
                                      item.screenshots.length > 0 ? (
                                        <div className="flex gap-2 flex-wrap">
                                          {item.screenshots.map(
                                            (screenshot, idx) => {
                                              // Handle both string URLs and object structures
                                              const screenshotUrl =
                                                typeof screenshot === "string"
                                                  ? screenshot
                                                  : screenshot.url;
                                              return (
                                                <button
                                                  key={idx}
                                                  onClick={() =>
                                                    openImageModal(
                                                      item.screenshots
                                                        .map((s) =>
                                                          typeof s === "string"
                                                            ? s
                                                            : s.url
                                                        )
                                                        .filter(Boolean),
                                                      idx
                                                    )
                                                  }
                                                  className="block group relative focus:outline-none"
                                                >
                                                  <img
                                                    src={screenshotUrl}
                                                    alt={`Your screenshot ${
                                                      idx + 1
                                                    }`}
                                                    className="w-24 h-24 object-cover rounded border-2 transition-colors"
                                                    style={{
                                                      borderColor:
                                                        item.needsRevision
                                                          ? "rgba(251, 191, 36, 0.5)"
                                                          : "rgba(34, 197, 94, 0.5)",
                                                    }}
                                                    onError={(e) => {
                                                      e.target.src =
                                                        "https://via.placeholder.com/96x96?text=Load+Error";
                                                    }}
                                                  />
                                                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                                                    <span className="text-white text-xs">
                                                      View
                                                    </span>
                                                  </div>
                                                  <div
                                                    className={`absolute top-1 right-1 text-white text-[10px] px-1.5 py-0.5 rounded ${
                                                      item.needsRevision
                                                        ? "bg-yellow-600"
                                                        : "bg-green-600"
                                                    }`}
                                                  >
                                                    {new Date(
                                                      (typeof screenshot ===
                                                      "string"
                                                        ? Date.now()
                                                        : screenshot.uploadedAt) ||
                                                        Date.now()
                                                    ).toLocaleDateString()}
                                                  </div>
                                                </button>
                                              );
                                            }
                                          )}
                                        </div>
                                      ) : (
                                        <p className="text-xs text-slate-500 italic">
                                          No screenshots uploaded
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              )}

                              {/* Admin's Revision Request */}
                              {item.needsRevision &&
                                item.issues &&
                                item.issues.length > 0 && (
                                  <div
                                    className="mt-2 p-3 rounded"
                                    style={{
                                      background: "rgba(255, 100, 0, 0.08)",
                                      border:
                                        "1px solid rgba(255, 100, 0, 0.2)",
                                    }}
                                  >
                                    <p className="text-xs font-semibold text-orange-400 mb-2 flex items-center gap-1.5">
                                      <HiExclamation className="w-4 h-4" />
                                      Admin Requested Revision
                                    </p>
                                    {item.revisionNote && (
                                      <p className="text-xs text-orange-300 mb-2 italic">
                                        {item.revisionNote}
                                      </p>
                                    )}
                                    <div className="space-y-2">
                                      {item.issues.map((issue, issueIdx) => (
                                        <div
                                          key={issueIdx}
                                          className="p-2 rounded"
                                          style={{
                                            background: "rgba(0, 0, 0, 0.2)",
                                            border:
                                              "1px solid rgba(255, 100, 0, 0.15)",
                                          }}
                                        >
                                          <div className="flex items-center gap-2 mb-1">
                                            <span className="text-[10px] text-slate-500">
                                              {new Date(
                                                issue.createdAt
                                              ).toLocaleDateString()}
                                            </span>
                                          </div>
                                          <p className="text-xs text-slate-300 mb-2">
                                            {issue.description}
                                          </p>
                                          {issue.screenshot &&
                                            (issue.screenshot.url ||
                                              typeof issue.screenshot ===
                                                "string") && (
                                              <div>
                                                <p className="text-[10px] text-orange-400 mb-1">
                                                  Admin's Screenshot:
                                                </p>
                                                <button
                                                  onClick={() =>
                                                    openImageModal(
                                                      [
                                                        typeof issue.screenshot ===
                                                        "string"
                                                          ? issue.screenshot
                                                          : issue.screenshot
                                                              .url,
                                                      ],
                                                      0
                                                    )
                                                  }
                                                  className="block group relative focus:outline-none"
                                                >
                                                  <img
                                                    src={
                                                      typeof issue.screenshot ===
                                                      "string"
                                                        ? issue.screenshot
                                                        : issue.screenshot.url
                                                    }
                                                    alt="Admin's issue screenshot"
                                                    className="w-32 h-32 object-cover rounded border-2 transition-colors"
                                                    style={{
                                                      borderColor:
                                                        "rgba(255, 100, 0, 0.5)",
                                                    }}
                                                    onError={(e) => {
                                                      e.target.src =
                                                        "https://via.placeholder.com/128x128?text=Image+Error";
                                                    }}
                                                  />
                                                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                                                    <span className="text-white text-xs">
                                                      View Full Size
                                                    </span>
                                                  </div>
                                                </button>
                                              </div>
                                            )}
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}

                                {/* Upload interface */}
                                {!item.completed &&
                                  task.status === "in-progress" && (
                                    <div
                                      className="mt-2 space-y-2 p-2 rounded"
                                      style={{
                                        background: "rgba(255, 255, 255, 0.03)",
                                        border:
                                          "1px solid rgba(255, 255, 255, 0.1)",
                                      }}
                                    >
                                      <textarea
                                        placeholder="Note (optional)"
                                        value={checklistNotes[item.number] || ""}
                                        onChange={(e) =>
                                          setChecklistNotes({
                                            ...checklistNotes,
                                            [item.number]: e.target.value,
                                          })
                                        }
                                        rows={2}
                                        className="w-full px-2 py-1.5 rounded text-xs text-white placeholder-slate-500 focus:outline-none resize-none"
                                        style={{
                                          background: "rgba(255, 255, 255, 0.03)",
                                          border:
                                            "1px solid rgba(255, 255, 255, 0.1)",
                                        }}
                                      />
                                      <input
                                        type="file"
                                        accept="image/*"
                                        multiple
                                        onChange={(e) => {
                                          const files = Array.from(
                                            e.target.files
                                          ).slice(0, 5);
                                          setChecklistScreenshots({
                                            ...checklistScreenshots,
                                            [item.number]: files,
                                          });
                                        }}
                                        className="w-full text-[10px] text-slate-400 cursor-pointer"
                                        style={{
                                          background: "rgba(255, 255, 255, 0.03)",
                                          padding: "4px",
                                          borderRadius: "4px",
                                          border:
                                            "1px solid rgba(255, 255, 255, 0.1)",
                                        }}
                                      />
                                      {checklistScreenshots[item.number] &&
                                        checklistScreenshots[item.number].length >
                                          0 && (
                                          <div className="flex gap-1.5 flex-wrap">
                                            {checklistScreenshots[
                                              item.number
                                            ].map((file, idx) => (
                                              <div
                                                key={idx}
                                                className="relative group"
                                              >
                                                <img
                                                  src={URL.createObjectURL(file)}
                                                  alt={file.name}
                                                  className="w-14 h-14 object-cover rounded border border-slate-600"
                                                />
                                                <button
                                                  type="button"
                                                  onClick={() => {
                                                    const updated =
                                                      checklistScreenshots[
                                                        item.number
                                                      ].filter(
                                                        (_, i) => i !== idx
                                                      );
                                                    setChecklistScreenshots({
                                                      ...checklistScreenshots,
                                                      [item.number]: updated,
                                                    });
                                                  }}
                                                  className="absolute -top-1 -right-1 w-5 h-5 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                                                  style={{
                                                    background:
                                                      "rgba(255, 255, 255, 0.2)",
                                                  }}
                                                  onMouseEnter={(e) =>
                                                    (e.target.style.background =
                                                      "rgba(255, 255, 255, 0.3)")
                                                  }
                                                  onMouseLeave={(e) =>
                                                    (e.target.style.background =
                                                      "rgba(255, 255, 255, 0.2)")
                                                  }
                                                >
                                                  ×
                                                </button>
                                              </div>
                                            ))}
                                          </div>
                                        )}
                                      <button
                                        onClick={() =>
                                          handleCompleteChecklistItem(item.number)
                                        }
                                        disabled={submitLoading}
                                        className="w-full px-3 py-1.5 text-white text-xs font-medium rounded transition-opacity disabled:opacity-50 flex items-center justify-center gap-1"
                                        style={{
                                          background: "rgba(255, 255, 255, 0.1)",
                                        }}
                                        onMouseEnter={(e) => {
                                          if (!submitLoading)
                                            e.target.style.background =
                                              "rgba(255, 255, 255, 0.15)";
                                        }}
                                        onMouseLeave={(e) =>
                                          (e.target.style.background =
                                            "rgba(255, 255, 255, 0.1)")
                                        }
                                      >
                                        <HiCheckCircle className="w-4 h-4" />
                                        Submit
                                      </button>
                                    </div>
                                  )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
              </div>
                              {/* Upload interface */}
                              {!item.completed &&
                                (task.status === "in-progress" ||
                                  task.status === "needs-revision") && (
                                  <div
                                    className="mt-2 space-y-2 p-3 rounded"
                                    style={{
                                      background: "rgba(255, 255, 255, 0.03)",
                                      border:
                                        "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                  >
                                    <textarea
                                      placeholder="Note (optional)"
                                      value={checklistNotes[item.number] || ""}
                                      onChange={(e) =>
                                        setChecklistNotes({
                                          ...checklistNotes,
                                          [item.number]: e.target.value,
                                        })
                                      }
                                      rows={2}
                                      className="w-full px-2 py-1.5 rounded text-xs text-white placeholder-slate-500 focus:outline-none resize-none"
                                      style={{
                                        background: "rgba(255, 255, 255, 0.03)",
                                        border:
                                          "1px solid rgba(255, 255, 255, 0.1)",
                                      }}
                                    />

                                    {/* Screenshot upload section with required label */}
                                    <div className="space-y-1">
                                      <label className="flex items-center gap-1 text-xs text-slate-300 font-medium">
                                        <span className="text-red-400">*</span>
                                        Screenshots Required (max 5)
                                      </label>
                                      <input
                                        type="file"
                                        accept="image/*"
                                        multiple
                                        required
                                        onChange={(e) => {
                                          const files = Array.from(
                                            e.target.files
                                          ).slice(0, 5);
                                          setChecklistScreenshots({
                                            ...checklistScreenshots,
                                            [item.number]: files,
                                          });
                                        }}
                                        className="w-full text-xs text-slate-400 cursor-pointer file:mr-2 file:py-1.5 file:px-3 file:rounded file:border-0 file:text-xs file:bg-blue-600/20 file:text-blue-300 hover:file:bg-blue-600/30 file:cursor-pointer"
                                        style={{
                                          background:
                                            "rgba(255, 255, 255, 0.05)",
                                          padding: "6px",
                                          borderRadius: "4px",
                                          border:
                                            "1px solid rgba(59, 130, 246, 0.3)",
                                        }}
                                      />
                                      {(!checklistScreenshots[item.number] ||
                                        checklistScreenshots[item.number]
                                          .length === 0) && (
                                        <p className="text-[10px] text-orange-400 flex items-center gap-1">
                                          <svg
                                            className="w-3 h-3"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                          >
                                            <path
                                              fillRule="evenodd"
                                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                              clipRule="evenodd"
                                            />
                                          </svg>
                                          Please upload at least one screenshot
                                          before submitting
                                        </p>
                                      )}
                                    </div>
                                    {checklistScreenshots[item.number] &&
                                      checklistScreenshots[item.number].length >
                                        0 && (
                                        <div className="flex gap-1.5 flex-wrap">
                                          {checklistScreenshots[
                                            item.number
                                          ].map((file, idx) => (
                                            <div
                                              key={idx}
                                              className="relative group"
                                            >
                                              <img
                                                src={URL.createObjectURL(file)}
                                                alt={file.name}
                                                className="w-14 h-14 object-cover rounded border border-slate-600"
                                              />
                                              <button
                                                type="button"
                                                onClick={() => {
                                                  const updated =
                                                    checklistScreenshots[
                                                      item.number
                                                    ].filter(
                                                      (_, i) => i !== idx
                                                    );
                                                  setChecklistScreenshots({
                                                    ...checklistScreenshots,
                                                    [item.number]: updated,
                                                  });
                                                }}
                                                className="absolute -top-1 -right-1 w-5 h-5 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                                                style={{
                                                  background:
                                                    "rgba(255, 255, 255, 0.2)",
                                                }}
                                                onMouseEnter={(e) =>
                                                  (e.target.style.background =
                                                    "rgba(255, 255, 255, 0.3)")
                                                }
                                                onMouseLeave={(e) =>
                                                  (e.target.style.background =
                                                    "rgba(255, 255, 255, 0.2)")
                                                }
                                              >
                                                ×
                                              </button>
                                            </div>
                                          ))}
                                        </div>
                                      )}
                                    <button
                                      onClick={() =>
                                        handleCompleteChecklistItem(item.number)
                                      }
                                      disabled={
                                        submitLoading ||
                                        !checklistScreenshots[item.number] ||
                                        checklistScreenshots[item.number]
                                          .length === 0
                                      }
                                      className="w-full px-3 py-1.5 text-white text-xs font-medium rounded transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-1"
                                      style={{
                                        background: "rgba(255, 255, 255, 0.1)",
                                      }}
                                      onMouseEnter={(e) => {
                                        if (
                                          !submitLoading &&
                                          checklistScreenshots[item.number]
                                            ?.length > 0
                                        )
                                          e.target.style.background =
                                            "rgba(255, 255, 255, 0.15)";
                                      }}
                                      onMouseLeave={(e) =>
                                        (e.target.style.background =
                                          "rgba(255, 255, 255, 0.1)")
                                      }
                                    >
                                      <HiCheckCircle className="w-4 h-4" />
                                      {!checklistScreenshots[item.number] ||
                                      checklistScreenshots[item.number]
                                        .length === 0
                                        ? "Upload Screenshots to Submit"
                                        : "Submit"}
                                    </button>
                                  </div>
                                )}

                              {/* Revision History Section */}
                              {item.revisionHistory &&
                                item.revisionHistory.length > 0 && (
                                  <div className="mt-3">
                                    <button
                                      onClick={() =>
                                        toggleRevisionHistory(item.number)
                                      }
                                      className="flex items-center gap-2 text-xs font-medium text-slate-300 hover:text-white transition-colors"
                                    >
                                      💬 Revision History (
                                      {item.revisionHistory.length})
                                      <svg
                                        className={`w-3 h-3 transition-transform ${
                                          expandedRevisionHistory[item.number]
                                            ? "rotate-180"
                                            : ""
                                        }`}
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M19 9l-7 7-7-7"
                                        />
                                      </svg>
                                    </button>

                                    {expandedRevisionHistory[item.number] && (
                                      <div className="mt-2 space-y-2">
                                        {item.revisionHistory.map(
                                          (entry, idx) => (
                                            <div
                                              key={idx}
                                              className="p-2 rounded"
                                              style={{
                                                background:
                                                  entry.type ===
                                                  "admin_revision"
                                                    ? "rgba(255, 100, 0, 0.08)"
                                                    : "rgba(59, 130, 246, 0.08)",
                                                border:
                                                  entry.type ===
                                                  "admin_revision"
                                                    ? "1px solid rgba(255, 100, 0, 0.2)"
                                                    : "1px solid rgba(59, 130, 246, 0.2)",
                                              }}
                                            >
                                              <div className="flex items-center justify-between mb-1.5">
                                                <span
                                                  className={`text-[10px] font-semibold ${
                                                    entry.type ===
                                                    "admin_revision"
                                                      ? "text-orange-400"
                                                      : "text-blue-400"
                                                  }`}
                                                >
                                                  {entry.type ===
                                                  "admin_revision"
                                                    ? "🔧 Admin Revision Request"
                                                    : "✅ Your Response"}
                                                </span>
                                                <span className="text-[10px] text-slate-400">
                                                  {new Date(
                                                    entry.createdAt
                                                  ).toLocaleDateString(
                                                    "en-US",
                                                    {
                                                      month: "short",
                                                      day: "numeric",
                                                      hour: "2-digit",
                                                      minute: "2-digit",
                                                    }
                                                  )}
                                                </span>
                                              </div>
                                              <p className="text-xs text-slate-300 mb-1.5">
                                                {entry.comment}
                                              </p>
                                              {entry.images &&
                                                entry.images.length > 0 && (
                                                  <div className="flex gap-1.5 flex-wrap mt-1.5">
                                                    {entry.images.map(
                                                      (img, imgIdx) => {
                                                        const imageUrl =
                                                          typeof img ===
                                                          "string"
                                                            ? img
                                                            : img.url;
                                                        return (
                                                          <button
                                                            key={imgIdx}
                                                            onClick={() =>
                                                              openImageModal(
                                                                entry.images.map(
                                                                  (i) =>
                                                                    typeof i ===
                                                                    "string"
                                                                      ? i
                                                                      : i.url
                                                                ),
                                                                imgIdx
                                                              )
                                                            }
                                                            className="block group relative focus:outline-none"
                                                          >
                                                            <img
                                                              src={imageUrl}
                                                              alt={
                                                                (typeof img ===
                                                                  "object" &&
                                                                  img.filename) ||
                                                                `Image ${
                                                                  imgIdx + 1
                                                                }`
                                                              }
                                                              className="w-20 h-20 object-cover rounded border-2 transition-colors"
                                                              style={{
                                                                borderColor:
                                                                  entry.type ===
                                                                  "admin_revision"
                                                                    ? "rgba(255, 100, 0, 0.5)"
                                                                    : "rgba(59, 130, 246, 0.5)",
                                                              }}
                                                            />
                                                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                                                              <span className="text-white text-[10px]">
                                                                View
                                                              </span>
                                                            </div>
                                                          </button>
                                                        );
                                                      }
                                                    )}
                                                  </div>
                                                )}
                                            </div>
                                          )
                                        )}
                                      </div>
                                    )}
                                  </div>
                                )}

                              {/* Respond to Revision Button & Form */}
                              {item.status === "needs-revision" && (
                                <div className="mt-3">
                                  <button
                                    onClick={() =>
                                      toggleRevisionResponseForm(item.number)
                                    }
                                    className="text-xs px-2 py-1 rounded text-white transition-colors flex items-center gap-1.5"
                                    style={{
                                      background: "rgba(59, 130, 246, 0.2)",
                                      border:
                                        "1px solid rgba(59, 130, 246, 0.3)",
                                    }}
                                    onMouseEnter={(e) =>
                                      (e.target.style.background =
                                        "rgba(59, 130, 246, 0.3)")
                                    }
                                    onMouseLeave={(e) =>
                                      (e.target.style.background =
                                        "rgba(59, 130, 246, 0.2)")
                                    }
                                  >
                                    {showRevisionResponseForm[item.number]
                                      ? "Cancel"
                                      : "📝 Respond to Revision"}
                                  </button>

                                  {showRevisionResponseForm[item.number] && (
                                    <div
                                      className="mt-2 p-3 rounded space-y-2"
                                      style={{
                                        background: "rgba(255, 255, 255, 0.03)",
                                        border:
                                          "1px solid rgba(255, 255, 255, 0.1)",
                                      }}
                                    >
                                      <div>
                                        <label className="block text-[10px] font-medium text-slate-300 mb-1">
                                          Your Response *
                                        </label>
                                        <textarea
                                          value={
                                            revisionResponseData[item.number]
                                              ?.comment || ""
                                          }
                                          onChange={(e) =>
                                            handleRevisionResponseInputChange(
                                              item.number,
                                              e.target.value
                                            )
                                          }
                                          placeholder="Explain what you fixed..."
                                          rows={2}
                                          className="w-full px-2 py-1.5 rounded text-xs text-white placeholder-slate-500 focus:outline-none resize-none"
                                          style={{
                                            background: "rgba(0, 0, 0, 0.3)",
                                            border:
                                              "1px solid rgba(255, 255, 255, 0.1)",
                                          }}
                                        />
                                      </div>

                                      <div>
                                        <label className="block text-[10px] font-medium text-slate-300 mb-1">
                                          Images (optional)
                                        </label>
                                        <input
                                          type="file"
                                          accept="image/*"
                                          multiple
                                          onChange={(e) =>
                                            handleRevisionResponseImageUpload(
                                              item.number,
                                              e.target.files
                                            )
                                          }
                                          className="hidden"
                                          id={`revision-response-upload-${item.number}`}
                                        />
                                        <label
                                          htmlFor={`revision-response-upload-${item.number}`}
                                          className="inline-flex items-center gap-1.5 px-2 py-1 text-white rounded cursor-pointer transition-colors text-[10px]"
                                          style={{
                                            background:
                                              "rgba(255, 255, 255, 0.1)",
                                          }}
                                          onMouseEnter={(e) =>
                                            (e.target.style.background =
                                              "rgba(255, 255, 255, 0.15)")
                                          }
                                          onMouseLeave={(e) =>
                                            (e.target.style.background =
                                              "rgba(255, 255, 255, 0.1)")
                                          }
                                        >
                                          📎 Upload Images
                                        </label>

                                        {revisionResponseData[item.number]
                                          ?.imagePreviews &&
                                          revisionResponseData[item.number]
                                            .imagePreviews.length > 0 && (
                                            <div className="flex gap-1.5 flex-wrap mt-2">
                                              {revisionResponseData[
                                                item.number
                                              ].imagePreviews.map(
                                                (preview, idx) => (
                                                  <div
                                                    key={idx}
                                                    className="relative group"
                                                  >
                                                    <img
                                                      src={preview}
                                                      alt={`Preview ${idx + 1}`}
                                                      className="w-16 h-16 object-cover rounded border border-slate-600"
                                                    />
                                                    <button
                                                      onClick={() =>
                                                        removeRevisionResponseImage(
                                                          item.number,
                                                          idx
                                                        )
                                                      }
                                                      className="absolute -top-1 -right-1 w-4 h-4 bg-red-600 hover:bg-red-700 rounded-full text-white text-[10px] flex items-center justify-center"
                                                    >
                                                      ×
                                                    </button>
                                                  </div>
                                                )
                                              )}
                                            </div>
                                          )}
                                      </div>

                                      <button
                                        onClick={() =>
                                          submitChecklistItemRevisionResponse(
                                            item.number
                                          )
                                        }
                                        disabled={
                                          !revisionResponseData[
                                            item.number
                                          ]?.comment?.trim() ||
                                          submittingRevisionResponse[
                                            item.number
                                          ]
                                        }
                                        className="w-full px-3 py-1.5 text-white text-xs font-medium rounded transition-opacity disabled:opacity-50"
                                        style={{
                                          background: "rgba(59, 130, 246, 0.3)",
                                        }}
                                        onMouseEnter={(e) => {
                                          if (
                                            !submittingRevisionResponse[
                                              item.number
                                            ] &&
                                            revisionResponseData[
                                              item.number
                                            ]?.comment?.trim()
                                          )
                                            e.target.style.background =
                                              "rgba(59, 130, 246, 0.4)";
                                        }}
                                        onMouseLeave={(e) =>
                                          (e.target.style.background =
                                            "rgba(59, 130, 246, 0.3)")
                                        }
                                      >
                                        {submittingRevisionResponse[item.number]
                                          ? "Submitting..."
                                          : "Submit Response"}
                                      </button>
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
            </div>

              {/* RIGHT COLUMN - Comments & Info (1/3 width) */}
              <div className="lg:col-span-1 space-y-4">
                {/* Comments Section */}
                <div
                  className="rounded-2xl border p-4 backdrop-blur-xl max-h-[calc(100vh-140px)] overflow-y-auto"
                  style={{
                    background: "rgba(255, 255, 255, 0.03)",
                    borderColor: "rgba(255, 255, 255, 0.1)",
                    boxShadow: "0 4px 16px 0 rgba(0, 0, 0, 0.3)",
                  }}
                >
                  <h2 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                    <HiPaperAirplane className="w-5 h-5 text-slate-300" />
                    Comments
                  </h2>

                  {/* Comment List */}
                  <div className="space-y-3 mb-4">
                    {task.comments && task.comments.length > 0 ? (
                      task.comments.map((comment) => (
                        <div
                          key={comment._id}
                          className="p-3 rounded"
                          style={{
                            background: "rgba(255, 255, 255, 0.03)",
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                          }}
                        >
                          <div className="flex items-center gap-2 mb-2 flex-wrap">
                            <span className="font-semibold text-white text-xs">
                              {comment.author?.name || "Unknown"}
                            </span>
                            <span
                              className="px-2 py-0.5 rounded text-[10px] font-medium"
                              style={{
                                background: "rgba(255, 255, 255, 0.05)",
                                border: "1px solid rgba(255, 255, 255, 0.1)",
                                color: "rgba(255, 255, 255, 0.7)",
                              }}
                            >
                              {comment.authorModel}
                            </span>
                            <span className="text-[10px] text-slate-500">
                              {new Date(comment.createdAt).toLocaleString()}
                            </span>
                          </div>
                          <p className="text-slate-300 text-xs">
                            {comment.message}
                          </p>
                        </div>
                      ))
                    ) : (
                      <p className="text-slate-400 text-center text-xs py-4">
                        No comments yet
                      </p>
                    )}
                  </div>

                {/* Add Comment Form */}
                <div className="space-y-2">
                  <label className="text-xs text-slate-300">Add Comment</label>
                  <textarea
                    value={commentMessage}
                    onChange={(e) => setCommentMessage(e.target.value)}
                    placeholder="Type your comment..."
                    rows={3}
                    maxLength={1000}
                    className="w-full px-3 py-2 rounded text-xs text-white placeholder-slate-500 focus:outline-none resize-none"
                    style={{
                      background: "rgba(255, 255, 255, 0.05)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                    }}
                  />
                  <div className="flex items-center justify-between">
                    <span className="text-[10px] text-slate-500">
                      {commentMessage.length}/1000
                    </span>
                    <button
                      onClick={handleAddComment}
                      disabled={submitLoading || !commentMessage.trim()}
                      className="px-4 py-1.5 text-white text-xs rounded font-semibold transition-opacity disabled:opacity-50 flex items-center gap-1"
                      style={{
                        background: "rgba(255, 255, 255, 0.1)",
                      }}
                      onMouseEnter={(e) => {
                        if (!submitLoading && commentMessage.trim())
                          e.target.style.background =
                            "rgba(255, 255, 255, 0.15)";
                      }}
                      onMouseLeave={(e) =>
                        (e.target.style.background = "rgba(255, 255, 255, 0.1)")
                      }
                    >
                      <HiPaperAirplane className="w-3 h-3" />
                      {submitLoading ? "..." : "Post"}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Image Preview Modal */}
      {imageModal.isOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
          onClick={closeImageModal}
        >
          <div
            className="relative max-w-4xl max-h-[90vh] w-full mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button - X at top right */}
            <button
              onClick={closeImageModal}
              className="absolute -top-12 right-0 text-white hover:text-slate-300 transition-colors"
            >
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            {/* Image */}
            <img
              src={imageModal.images[imageModal.currentIndex]}
              alt={`Preview ${imageModal.currentIndex + 1}`}
              className="w-full h-auto max-h-[85vh] object-contain rounded-lg"
              onError={(e) => {
                e.target.src =
                  "https://via.placeholder.com/800x600?text=Image+Not+Found";
              }}
            />

            {/* Navigation buttons */}
            {imageModal.images.length > 1 && (
              <>
                {/* Previous button */}
                <button
                  onClick={prevImage}
                  className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-colors"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>

                {/* Next button */}
                <button
                  onClick={nextImage}
                  className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-colors"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>

                {/* Image counter */}
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full text-sm">
                  {imageModal.currentIndex + 1} / {imageModal.images.length}
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
