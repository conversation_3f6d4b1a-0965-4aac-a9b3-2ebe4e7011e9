import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useJobStore } from "../../store/hiring/jobStore";
import { useApplicationStore } from "../../store/hiring/applicationStore";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import {
  HiOfficeBuilding,
  HiLocationMarker,
  HiCurrencyDollar,
  HiBriefcase,
  HiClock,
  HiCheckCircle,
  HiXCircle,
  HiDocumentText,
  HiPlus,
  HiTrash,
  HiArrowRight,
  HiArrowLeft,
  HiUser,
  HiMail,
  HiGlobeAlt,
} from "react-icons/hi";

export default function JobApplication() {
  const { slug } = useParams();
  const navigate = useNavigate();
  const { fetchJobBySlug, selectedJob, loading: jobLoading } = useJobStore();
  const { submitPublicApplication, loading: submitting } =
    useApplicationStore();

  // Form state
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    currentLocation: "",
    education: "",
    linkedinUrl: "",
    portfolioUrl: "",
    githubUrl: "",
    employmentStatus: "",
    noticePeriod: "",
    howDidYouHear: "",
    expectedSalary: "",
    availableToStart: "",
    projects: [
      { name: "", description: "", url: "", username: "", password: "" },
      { name: "", description: "", url: "", username: "", password: "" },
    ],
  });

  const [resume, setResume] = useState(null);
  const [errors, setErrors] = useState({});
  const [submitted, setSubmitted] = useState(false);

  useEffect(() => {
    if (slug) {
      fetchJobBySlug(slug);
    }
  }, [slug]);

  // Debug: Log the selectedJob to see if images are loaded
  useEffect(() => {
    if (selectedJob) {
      console.log("Selected Job Data:", selectedJob);
      console.log("Background Image:", selectedJob.backgroundImage);
      console.log("Banner Image:", selectedJob.bannerImage);
    }
  }, [selectedJob]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: null }));
    }
  };

  const handlePhoneChange = (value) => {
    // Keep the value even if it's just a country code
    setFormData((prev) => ({
      ...prev,
      phone: value !== undefined ? value : "",
    }));
    if (errors.phone) {
      setErrors((prev) => ({ ...prev, phone: null }));
    }
  };

  const handleProjectChange = (index, field, value) => {
    const newProjects = [...formData.projects];
    newProjects[index][field] = value;
    setFormData((prev) => ({ ...prev, projects: newProjects }));
  };

  const addProject = () => {
    if (formData.projects.length < 5) {
      setFormData((prev) => ({
        ...prev,
        projects: [
          ...prev.projects,
          { name: "", description: "", url: "", username: "", password: "" },
        ],
      }));
    }
  };

  const removeProject = (index) => {
    if (formData.projects.length > 2) {
      setFormData((prev) => ({
        ...prev,
        projects: prev.projects.filter((_, i) => i !== index),
      }));
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const validTypes = [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ];
      if (!validTypes.includes(file.type)) {
        setErrors((prev) => ({
          ...prev,
          resume: "Only PDF and DOC files are allowed",
        }));
        return;
      }
      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors((prev) => ({
          ...prev,
          resume: "File size must be less than 5MB",
        }));
        return;
      }
      setResume(file);
      setErrors((prev) => ({ ...prev, resume: null }));
    }
  };

  const validate = () => {
    const newErrors = {};

    if (!formData.fullName.trim()) newErrors.fullName = "Full name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.email))
      newErrors.email = "Invalid email format";
    if (!formData.phone.trim()) newErrors.phone = "Phone is required";
    if (!formData.currentLocation.trim())
      newErrors.currentLocation = "Location is required";
    if (!formData.education.trim())
      newErrors.education = "Education is required";
    if (!formData.linkedinUrl.trim())
      newErrors.linkedinUrl = "LinkedIn URL is required";
    if (!formData.howDidYouHear.trim())
      newErrors.howDidYouHear = "This field is required";
    if (!resume) newErrors.resume = "Resume is required";

    // Validate notice period for employed candidates
    if (formData.employmentStatus === "employed" && !formData.noticePeriod) {
      newErrors.noticePeriod =
        "Notice period is required for employed candidates";
    }

    // Validate projects (at least 2)
    const validProjects = formData.projects.filter(
      (p) => p.name.trim() && p.description.trim()
    );
    if (validProjects.length < 2) {
      newErrors.projects = "At least 2 projects are required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validate()) {
      return;
    }

    // Filter out empty projects and ensure all required fields (name and URL only)
    const validProjects = formData.projects.filter(
      (p) => p.name.trim() && p.url.trim()
    );

    const applicationData = new FormData();
    applicationData.append("jobId", selectedJob._id);
    applicationData.append("fullName", formData.fullName);
    applicationData.append("email", formData.email);
    applicationData.append("phone", formData.phone);
    applicationData.append("currentLocation", formData.currentLocation);
    applicationData.append("education", formData.education);
    applicationData.append("linkedinUrl", formData.linkedinUrl);
    applicationData.append("portfolioUrl", formData.portfolioUrl || "");
    applicationData.append("githubUrl", formData.githubUrl || "");
    applicationData.append("employmentStatus", formData.employmentStatus);
    applicationData.append("noticePeriod", formData.noticePeriod || "");
    applicationData.append("howDidYouHear", formData.howDidYouHear);
    applicationData.append("expectedSalary", formData.expectedSalary || "");
    applicationData.append("availableToStart", formData.availableToStart || "");
    applicationData.append("projects", JSON.stringify(validProjects));
    applicationData.append("resume", resume);

    const success = await submitPublicApplication(applicationData);
    if (success) {
      setSubmitted(true);
      window.scrollTo(0, 0);
    }
  };

  if (jobLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-foreground text-lg">Loading job details...</div>
      </div>
    );
  }

  if (!selectedJob) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="border border-border/40 backdrop-blur-sm bg-card/95 max-w-md">
          <CardHeader>
            <CardTitle className="text-red-400 flex items-center gap-2">
              <HiXCircle className="w-6 h-6" />
              Job Not Found
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              The job you're looking for doesn't exist or has been closed.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (selectedJob.status !== "published") {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="border border-border/40 backdrop-blur-sm bg-card/95 max-w-md">
          <CardHeader>
            <CardTitle className="text-orange-400 flex items-center gap-2">
              <HiXCircle className="w-6 h-6" />
              Job Not Available
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              This job is currently not accepting applications.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="border border-border/40 backdrop-blur-sm bg-card/95 max-w-2xl w-full">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <HiCheckCircle className="w-16 h-16 text-green-500" />
            </div>
            <CardTitle className="text-2xl text-foreground">
              Application Submitted Successfully!
            </CardTitle>
            <CardDescription className="text-muted-foreground text-base mt-2">
              Thank you for applying to{" "}
              <span className="font-semibold text-foreground">
                {selectedJob.title}
              </span>
              . We've received your application and will review it shortly.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-slate-700/30 rounded-lg p-4 space-y-2">
              <p className="text-sm text-muted-foreground">
                <strong className="text-foreground">What's Next?</strong>
              </p>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li>You'll receive a confirmation email shortly</li>
                <li>
                  Our team will review your application within 3-5 business days
                </li>
                <li>If shortlisted, you'll receive an email with next steps</li>
              </ul>
            </div>
            <div className="text-center py-4">
              <p className="text-muted-foreground text-sm">
                You can now safely close this window.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-8 px-4 relative">
      {/* Background Image */}
      {selectedJob?.backgroundImage?.url && (
        <div
          className="fixed inset-0"
          style={{
            backgroundImage: `url(${selectedJob.backgroundImage.url})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            filter: "blur(3px)",
            zIndex: 0,
          }}
        />
      )}

      <div className="max-w-7xl mx-auto relative" style={{ zIndex: 10 }}>
        {/* Banner Image */}
        {selectedJob?.bannerImage?.url && (
          <div className="mb-6 rounded-xl overflow-hidden shadow-2xl border border-border/20">
            <img
              src={selectedJob.bannerImage.url}
              alt="Job Banner"
              className="w-full h-48 md:h-64 lg:h-80 object-cover"
              onError={(e) => {
                console.error(
                  "Banner image failed to load:",
                  selectedJob.bannerImage.url
                );
                e.target.style.display = "none";
              }}
            />
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Job Details (Sticky) */}
          <div className="lg:col-span-1">
            <div className="sticky top-8 space-y-6">
              {/* Job Header */}
              <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                <CardHeader>
                  <CardTitle className="text-xl text-foreground">
                    {selectedJob.title}
                  </CardTitle>
                  <CardDescription className="text-muted-foreground space-y-3 mt-3">
                    <div className="flex flex-col gap-2 text-sm">
                      <span className="flex items-center gap-2">
                        <HiLocationMarker className="w-4 h-4 text-muted-foreground" />
                        {selectedJob.location}
                      </span>
                      <span className="flex items-center gap-2">
                        <HiBriefcase className="w-4 h-4 text-muted-foreground" />
                        {selectedJob.experienceLevel === "fresher"
                          ? "Fresher"
                          : `${selectedJob.experienceLevel} years`}
                      </span>
                      <span className="flex items-center gap-2">
                        <HiClock className="w-4 h-4 text-muted-foreground" />
                        {selectedJob.jobType === "full-time"
                          ? "Full-Time"
                          : selectedJob.jobType === "part-time"
                          ? "Part-Time"
                          : selectedJob.jobType === "contract"
                          ? "Contract"
                          : "Internship"}
                      </span>
                      <span className="flex items-center gap-2">
                        <HiCurrencyDollar className="w-4 h-4 text-muted-foreground" />
                        {selectedJob.compensationType === "paid"
                          ? "Paid"
                          : "Unpaid"}
                      </span>
                    </div>
                  </CardDescription>
                </CardHeader>
              </Card>

              {/* Job Description */}
              <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                <CardHeader>
                  <CardTitle className="text-lg text-foreground">
                    About the Job
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="text-sm font-semibold text-muted-foreground mb-2">
                      Description
                    </h4>
                    <div
                      className="text-sm text-muted-foreground prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{
                        __html: selectedJob.description,
                      }}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Responsibilities */}
              <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                <CardHeader>
                  <CardTitle className="text-lg text-foreground">
                    Responsibilities
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div
                    className="text-sm text-muted-foreground prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{
                      __html: selectedJob.responsibilities,
                    }}
                  />
                </CardContent>
              </Card>

              {/* Company Info */}
              <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <HiOfficeBuilding className="w-5 h-5 text-muted-foreground" />
                    Company Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground whitespace-pre-line">
                    {selectedJob.companyInfo}
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Right Column - Application Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit}>
              <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                <CardHeader>
                  <CardTitle className="text-xl">
                    Apply for this position
                  </CardTitle>
                  <CardDescription>
                    Fill out the form below to submit your application. All
                    fields marked with * are required.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Personal Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Personal Information
                    </h3>{" "}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">
                          Full Name *
                        </label>
                        <input
                          type="text"
                          name="fullName"
                          value={formData.fullName}
                          onChange={handleChange}
                          className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                          placeholder="John Doe"
                        />
                        {errors.fullName && (
                          <p className="text-red-400 text-sm mt-1">
                            {errors.fullName}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">
                          Email *
                        </label>
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                          placeholder="<EMAIL>"
                        />
                        {errors.email && (
                          <p className="text-red-400 text-sm mt-1">
                            {errors.email}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">
                          Phone *
                        </label>
                        <PhoneInput
                          international
                          value={formData.phone || ""}
                          onChange={handlePhoneChange}
                          placeholder="Enter phone number"
                          className="w-full px-4 py-2 border border-input bg-background rounded-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                          inputClassName="bg-transparent border-none outline-none text-foreground w-full"
                          containerClassName="flex items-center gap-2"
                          style={{
                            width: "100%",
                          }}
                        />
                        {errors.phone && (
                          <p className="text-red-400 text-sm mt-1">
                            {errors.phone}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">
                          Current Location *
                        </label>
                        <input
                          type="text"
                          name="currentLocation"
                          value={formData.currentLocation}
                          onChange={handleChange}
                          className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                          placeholder="New York, USA"
                        />
                        {errors.currentLocation && (
                          <p className="text-red-400 text-sm mt-1">
                            {errors.currentLocation}
                          </p>
                        )}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-1">
                        Education *
                      </label>
                      <select
                        name="education"
                        value={formData.education}
                        onChange={handleChange}
                        className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                      >
                        <option value="">Select your education level</option>
                        <option value="Bachelor's Degree">
                          Bachelor's Degree
                        </option>
                        <option value="Master's Degree">Master's Degree</option>
                        <option value="PhD">PhD</option>
                        <option value="Diploma">Diploma</option>
                        <option value="High School">High School</option>
                        <option value="Other">Other</option>
                      </select>
                      {errors.education && (
                        <p className="text-red-400 text-sm mt-1">
                          {errors.education}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Professional Links */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Professional Links
                    </h3>

                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">
                          LinkedIn URL *
                        </label>
                        <input
                          type="url"
                          name="linkedinUrl"
                          value={formData.linkedinUrl}
                          onChange={handleChange}
                          className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                          placeholder="https://linkedin.com/in/johndoe"
                        />
                        {errors.linkedinUrl && (
                          <p className="text-red-400 text-sm mt-1">
                            {errors.linkedinUrl}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">
                          Portfolio URL (Optional)
                        </label>
                        <input
                          type="url"
                          name="portfolioUrl"
                          value={formData.portfolioUrl}
                          onChange={handleChange}
                          className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                          placeholder="https://johndoe.com"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">
                          GitHub URL (Optional)
                        </label>
                        <input
                          type="url"
                          name="githubUrl"
                          value={formData.githubUrl}
                          onChange={handleChange}
                          className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                          placeholder="https://github.com/johndoe"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Employment Details */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Employment Details
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">
                          Employment Status *
                        </label>
                        <select
                          name="employmentStatus"
                          value={formData.employmentStatus}
                          onChange={handleChange}
                          className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                        >
                          <option value="">
                            Select your employment status
                          </option>
                          <option value="Employed">Employed</option>
                          <option value="Unemployed">Unemployed</option>
                          <option value="Student">Student</option>
                          <option value="Freelancer">Freelancer</option>
                          <option value="Other">Other</option>
                        </select>
                      </div>

                      {formData.employmentStatus === "Employed" && (
                        <div>
                          <label className="block text-sm font-medium text-muted-foreground mb-1">
                            Notice Period (days) *
                          </label>
                          <input
                            type="number"
                            name="noticePeriod"
                            value={formData.noticePeriod}
                            onChange={handleChange}
                            className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                            placeholder="30"
                          />
                          {errors.noticePeriod && (
                            <p className="text-red-400 text-sm mt-1">
                              {errors.noticePeriod}
                            </p>
                          )}
                        </div>
                      )}

                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">
                          Expected Salary (Optional)
                        </label>
                        <input
                          type="text"
                          name="expectedSalary"
                          value={formData.expectedSalary}
                          onChange={handleChange}
                          className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                          placeholder="$80,000 - $100,000"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-muted-foreground mb-1">
                          Available to Start (Optional)
                        </label>
                        <input
                          type="date"
                          name="availableToStart"
                          value={formData.availableToStart}
                          onChange={handleChange}
                          className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Projects */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-foreground">
                        Projects * (Min 2, Max 5)
                      </h3>
                      {formData.projects.length < 5 && (
                        <button
                          type="button"
                          onClick={addProject}
                          className="flex items-center gap-1 text-muted-foreground hover:text-foreground text-sm"
                        >
                          <HiPlus className="w-4 h-4" />
                          Add Project
                        </button>
                      )}
                    </div>

                    {errors.projects && (
                      <p className="text-red-400 text-sm">{errors.projects}</p>
                    )}

                    {formData.projects.map((project, index) => (
                      <div
                        key={index}
                        className="bg-slate-700/30 rounded-lg p-4 space-y-3 relative"
                      >
                        {formData.projects.length > 2 && (
                          <button
                            type="button"
                            onClick={() => removeProject(index)}
                            className="absolute top-2 right-2 text-red-400 hover:text-red-300"
                          >
                            <HiTrash className="w-5 h-5" />
                          </button>
                        )}

                        <h4 className="text-foreground font-medium">
                          Project {index + 1}
                        </h4>

                        <div>
                          <label className="block text-sm font-medium text-muted-foreground mb-1">
                            Project Name *
                          </label>
                          <input
                            type="text"
                            value={project.name}
                            onChange={(e) =>
                              handleProjectChange(index, "name", e.target.value)
                            }
                            className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                            placeholder="E-commerce Website"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-muted-foreground mb-1">
                            Description *
                          </label>
                          <textarea
                            value={project.description}
                            onChange={(e) =>
                              handleProjectChange(
                                index,
                                "description",
                                e.target.value
                              )
                            }
                            rows={3}
                            className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                            placeholder="Built a full-stack e-commerce platform with React and Node.js..."
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-muted-foreground mb-1">
                            Project URL *
                          </label>
                          <input
                            type="url"
                            value={project.url}
                            onChange={(e) =>
                              handleProjectChange(index, "url", e.target.value)
                            }
                            className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                            placeholder="https://myproject.com"
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-sm font-medium text-muted-foreground mb-1">
                              Test Username (Optional)
                            </label>
                            <input
                              type="text"
                              value={project.username}
                              onChange={(e) =>
                                handleProjectChange(
                                  index,
                                  "username",
                                  e.target.value
                                )
                              }
                              className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                              placeholder="test_user"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-muted-foreground mb-1">
                              Test Password (Optional)
                            </label>
                            <input
                              type="text"
                              value={project.password}
                              onChange={(e) =>
                                handleProjectChange(
                                  index,
                                  "password",
                                  e.target.value
                                )
                              }
                              className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                              placeholder="test123"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Resume Upload */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Resume *
                    </h3>

                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-2">
                        Upload your resume (PDF or DOC, max 5MB)
                      </label>
                      <div className="flex items-center gap-4">
                        <label className="flex items-center gap-2 border border-input bg-background rounded-lg px-4 py-2 cursor-pointer hover:bg-slate-700 transition-colors">
                          <HiDocumentText className="w-5 h-5 text-muted-foreground" />
                          <span className="text-foreground">Choose File</span>
                          <input
                            type="file"
                            accept=".pdf,.doc,.docx"
                            onChange={handleFileChange}
                            className="hidden"
                          />
                        </label>
                        {resume && (
                          <span className="text-sm text-muted-foreground">
                            {resume.name}
                          </span>
                        )}
                      </div>
                      {errors.resume && (
                        <p className="text-red-400 text-sm mt-1">
                          {errors.resume}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Additional Info */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Additional Information
                    </h3>

                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-1">
                        How did you hear about this position? *
                      </label>
                      <select
                        name="howDidYouHear"
                        value={formData.howDidYouHear}
                        onChange={handleChange}
                        className="w-full border border-input bg-background rounded-lg px-4 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                      >
                        <option value="">Select an option</option>
                        <option value="LinkedIn">LinkedIn</option>
                        <option value="Company Website">Company Website</option>
                        <option value="Referral">Referral</option>
                        <option value="Job Portal">Job Portal</option>
                        <option value="Social Media">Social Media</option>
                        <option value="Other">Other</option>
                      </select>
                      {errors.howDidYouHear && (
                        <p className="text-red-400 text-sm mt-1">
                          {errors.howDidYouHear}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex gap-4 pt-4">
                    <button
                      type="button"
                      onClick={() => navigate(-1)}
                      className="flex-1 border border-border hover:bg-accent text-foreground py-3 px-4 rounded-lg transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={submitting}
                      className="flex-1 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:cursor-not-allowed text-foreground py-3 px-4 rounded-lg transition-colors"
                    >
                      {submitting ? "Submitting..." : "Submit Application"}
                    </button>
                  </div>
                </CardContent>
              </Card>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
