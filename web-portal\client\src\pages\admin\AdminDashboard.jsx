import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../store/authStore";
import { useDashboardStore } from "../../store/dashboardStore";
import { getUserRole } from "../../utils/rolePermissions";
import AdminLayout from "../../components/admin/AdminLayout";
import StatsCards from "../../components/dashboard-homepage/StatsCards";
import RecentTalents from "../../components/dashboard-homepage/RecentTalents";
import NoReportDashboard from "../../components/dashboard-homepage/NoReportDashboard";
import MeetingsSection from "../../components/dashboard-homepage/MeetingsSection";
import JibbleOnlineStatus from "../../components/dashboard-homepage/JibbleOnlineStatus";

export default function AdminDashboard() {
  const navigate = useNavigate();
  const { user, accessToken, isInitializing } = useAuthStore();
  const {
    stats,
    recentTalents,
    noReportTalents,
    upcomingMeetings,
    jibbleStatus,
    loading,
    error,
    initializeAdminDashboard,
  } = useDashboardStore();

  // Check admin access - Wait for auth initialization before checking
  useEffect(() => {
    console.log('🔍 [AdminDashboard] Access check:', {
      isInitializing,
      hasUser: !!user,
      userType: user?.userType,
      roles: user?.roles,
      userRole: user ? getUserRole(user) : null
    });

    // Don't check access while still initializing
    if (isInitializing) {
      console.log('⏳ [AdminDashboard] Waiting for auth initialization...');
      return;
    }
    
    // After initialization, check if user has admin access
    const userRole = getUserRole(user);
    if (!userRole) {
      console.log('❌ [AdminDashboard] No admin role found - redirecting to talent dashboard');
      navigate("/talent/dashboard");
    } else {
      console.log('✅ [AdminDashboard] Admin access granted with role:', userRole);
    }
  }, [user, navigate, isInitializing]);

  // Fetch dashboard stats
  useEffect(() => {
    const userRole = getUserRole(user);
    if (userRole && accessToken) {
      initializeAdminDashboard(accessToken);
    }
  }, [user, accessToken]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Show loading while auth is initializing
  if (isInitializing) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  // Don't render if user doesn't have admin access
  const userRole = getUserRole(user);
  if (!userRole) return null;

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex-1 space-y-4 p-4">
        {/* Stats Cards - Row 1 */}
        <StatsCards stats={stats} />

        {/* Main Grid Layout - Use auto-rows-min for natural height */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 auto-rows-min">
          {/* Row 2, Col 1-2: Recent Talents */}
          <div className="lg:col-span-2">
            <div className="h-full">
              <RecentTalents />
            </div>
          </div>

          {/* Row 2, Col 3: Meetings */}
          <div className="lg:col-span-1">
            <div className="h-full">
              <MeetingsSection />
            </div>
          </div>

          {/* Row 2-3, Col 4: Jibble - Spans 2 rows vertically */}
          <div className="lg:col-span-1 lg:row-span-2">
            <div className="h-full">
              <JibbleOnlineStatus />
            </div>
          </div>

          {/* Row 3, Col 1-3: No Report Dashboard */}
          <div className="lg:col-span-3">
            <div className="h-full">
              <NoReportDashboard />
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
