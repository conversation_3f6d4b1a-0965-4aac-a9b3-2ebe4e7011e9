import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Shield, Users, UserCheck, Briefcase, Award } from 'lucide-react';

export default function CreateAdminDialog({ open, onOpenChange, onSubmit, availableRoles }) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    roleType: '',
    canAccessTalentPortal: false,
    sendWelcomeEmail: true
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when dialog opens/closes
  React.useEffect(() => {
    if (open) {
      setFormData({
        name: '',
        email: '',
        roleType: '',
        canAccessTalentPortal: false,
        sendWelcomeEmail: true
      });
      setErrors({});
    }
  }, [open]);

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.roleType) {
      newErrors.roleType = 'Role is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      await onSubmit(formData);
    } catch (error) {
      console.error('Error creating admin:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input changes
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  // Get role icon
  const getRoleIcon = (roleType) => {
    const icons = {
      super_admin: Shield,
      full_admin: UserCheck,
      hiring_manager: Briefcase,
      employee_manager: Users,
      team_lead: Award
    };
    
    return icons[roleType] || Users;
  };

  // Get role color
  const getRoleColor = (roleType) => {
    const colors = {
      super_admin: 'text-red-600 bg-red-50 border-red-200',
      full_admin: 'text-purple-600 bg-purple-50 border-purple-200',
      hiring_manager: 'text-blue-600 bg-blue-50 border-blue-200',
      employee_manager: 'text-green-600 bg-green-50 border-green-200',
      team_lead: 'text-orange-600 bg-orange-50 border-orange-200'
    };
    
    return colors[roleType] || 'text-gray-600 bg-gray-50 border-gray-200';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="text-blue-600" size={20} />
            Create Admin User
          </DialogTitle>
          <DialogDescription>
            Create a new admin user with specific role permissions. They will receive login credentials via email.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  placeholder="Enter full name"
                  className={errors.name ? 'border-red-300 focus:border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle size={12} />
                    {errors.name}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  placeholder="Enter email address"
                  className={errors.email ? 'border-red-300 focus:border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle size={12} />
                    {errors.email}
                  </p>
                )}
              </div>
            </div>

            {/* Role Selection */}
            <div className="space-y-2">
              <Label>Admin Role *</Label>
              <Select 
                value={formData.roleType} 
                onValueChange={(value) => handleChange('roleType', value)}
              >
                <SelectTrigger className={errors.roleType ? 'border-red-300 focus:border-red-500' : ''}>
                  <SelectValue placeholder="Select admin role" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(availableRoles).map(([key, role]) => {
                    const Icon = getRoleIcon(key);
                    return (
                      <SelectItem key={key} value={key} className="p-3">
                        <div className="flex items-center gap-3">
                          <Icon size={16} className={getRoleColor(key).split(' ')[0]} />
                          <div>
                            <p className="font-medium">{role.name}</p>
                            <p className="text-xs text-gray-500">{role.description}</p>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              {errors.roleType && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle size={12} />
                  {errors.roleType}
                </p>
              )}
            </div>

            {/* Selected Role Info */}
            {formData.roleType && availableRoles[formData.roleType] && (
              <div className={`p-3 rounded-lg border ${getRoleColor(formData.roleType)}`}>
                <div className="flex items-center gap-2 mb-2">
                  {React.createElement(getRoleIcon(formData.roleType), { size: 16 })}
                  <span className="font-medium">{availableRoles[formData.roleType].name}</span>
                </div>
                <p className="text-sm opacity-80 mb-3">{availableRoles[formData.roleType].description}</p>
                <div className="space-y-1">
                  <p className="text-xs font-medium opacity-70">Permissions:</p>
                  <div className="flex flex-wrap gap-1">
                    {availableRoles[formData.roleType].permissions.slice(0, 3).map((permission, index) => (
                      <Badge key={index} className="text-xs px-2 py-0 bg-white/50 text-current border-current/20">
                        {permission}
                      </Badge>
                    ))}
                    {availableRoles[formData.roleType].permissions.length > 3 && (
                      <Badge className="text-xs px-2 py-0 bg-white/50 text-current border-current/20">
                        +{availableRoles[formData.roleType].permissions.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Additional Options */}
          <div className="space-y-4 border-t pt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Talent Portal Access</Label>
                <p className="text-xs text-gray-500">
                  Allow this admin to also access the talent portal (dual access)
                </p>
              </div>
              <Switch
                checked={formData.canAccessTalentPortal}
                onCheckedChange={(checked) => handleChange('canAccessTalentPortal', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Send Welcome Email</Label>
                <p className="text-xs text-gray-500">
                  Send login credentials and welcome message via email
                </p>
              </div>
              <Switch
                checked={formData.sendWelcomeEmail}
                onCheckedChange={(checked) => handleChange('sendWelcomeEmail', checked)}
              />
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertCircle className="text-amber-600 mt-0.5" size={16} />
              <div className="text-sm">
                <p className="font-medium text-amber-800">Security Notice</p>
                <p className="text-amber-700 mt-1">
                  A temporary password will be generated and the user must change it on first login.
                  {!formData.sendWelcomeEmail && ' Password will be shown after creation since email is disabled.'}
                </p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Creating...
                </div>
              ) : (
                'Create Admin'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}