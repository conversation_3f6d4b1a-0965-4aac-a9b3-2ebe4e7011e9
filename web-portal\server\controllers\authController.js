import Talent from "../models/Talent.js";
import Admin from "../models/Admin.js";
import RefreshToken from "../models/RefreshToken.js";
import {
  hashPassword,
  verifyPassword,
  generateAccessToken,
  generateRefreshToken,
  verifyRefreshToken,
  getRefreshTokenExpiry,
} from "../utils/auth.js";
import { getSecureCookieOptions } from "../middleware/httpsEnforcer.js";
import { safeLog, safeError } from "../utils/logSanitizer.js";

// @desc    Register a new talent
// @route   POST /auth/register
// @access  Public
export const register = async (req, res) => {
  try {
    const { talentId, name, email, password, batchId } = req.body;

    // Validation
    if (!talentId || !name || !email || !password) {
      return res.status(400).json({ error: "All fields are required" });
    }

    // Check if email already exists
    const existingTalent = await Talent.findOne({ email: email.toLowerCase() });
    if (existingTalent) {
      return res.status(409).json({ error: "Email already registered" });
    }

    // Check if talentId already exists
    const existingTalentId = await Talent.findOne({ talentId });
    if (existingTalentId) {
      return res.status(409).json({ error: "Talent ID already exists" });
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create talent
    const talent = await Talent.create({
      talentId,
      name,
      email: email.toLowerCase(),
      passwordHash,
      status: "active",
      batchId: batchId || null,
    });

    res.status(201).json({
      message: "Talent registered successfully",
      profile: {
        userId: talent._id,
        talentId: talent.talentId,
        name: talent.name,
        email: talent.email,
        batchId: talent.batchId,
        status: talent.status,
        userType: "talent",
      },
    });
  } catch (error) {
    console.error("Registration error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// @desc    Login (Admin or Talent)
// @route   POST /auth/login
// @access  Public
export const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return res.status(400).json({ error: "Email and password are required" });
    }

    // Try to find as Admin first
    let user = await Admin.findOne({ email: email.toLowerCase() });
    let userType = "admin";

    // If not admin, try Talent (search both personal email and company email)
    if (!user) {
      user = await Talent.findOne({
        $or: [
          { email: email.toLowerCase() },
          { companyEmail: email.toLowerCase() },
        ],
      }).populate("badges");
      userType = "talent";
    }

    if (!user) {
      return res.status(401).json({ error: "Invalid email or password" });
    }

    // Check status/active
    if (userType === "admin" && !user.isActive) {
      return res.status(403).json({ error: "Admin account is deactivated" });
    }

    // Only block suspended and alumni talents, allow invited talents to login
    if (
      userType === "talent" &&
      (user.status === "suspended" || user.status === "alumni")
    ) {
      return res.status(403).json({
        error: `Account is ${user.status}. Please contact support.`,
      });
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.passwordHash);
    if (!isPasswordValid) {
      return res.status(401).json({ error: "Invalid email or password" });
    }

    // If talent is logging in for first time (status=invited), activate them
    if (userType === "talent" && user.status === "invited") {
      user.status = "active";
      await user.save();
      console.log(`✅ Talent ${user.talentId} activated on first login`);
    }

    // Generate JWT tokens with userType
    const payload = {
      userId: user._id,
      email: user.email,
      userType,
      ...(userType === "talent"
        ? {
            talentId: user.talentId,
            status: user.status,
            batchId: user.batchId,
            assignedProject: user.assignedProject, // ✅ Include assigned project for task filtering
            hasFullAccess: user.hasFullAccess || false, // ✅ Include full access flag for cross-project tasks
            roles: user.roles || [], // ✅ Include roles for admin middleware
            adminPermissions: user.adminPermissions || [], // ✅ Include admin permissions
            canViewPayments: user.canViewPayments || false, // ✅ Include payment access
          }
        : {
            role: user.role,
            roleType: user.roleType, // ✅ Include new roleType field for JWT payload
            permissions: user.permissions,
            permissionMode: user.permissionMode, // ✅ Include permission mode in JWT
            customPermissions: user.customPermissions || [], // ✅ Include custom permissions in JWT
            canViewPayments: user.canViewPayments || false,
            canAccessTalentPortal: user.canAccessTalentPortal || false, // ✅ Include talent portal access
          }),
    };

    const accessToken = generateAccessToken(payload);
    const refreshToken = generateRefreshToken({ userId: user._id, userType });

    // Store refresh token in database
    await RefreshToken.create({
      userId: user._id,
      token: refreshToken,
      expiresAt: getRefreshTokenExpiry(),
    });

    // HIGH #6: Use secure cookie options with HTTPS enforcement
    const cookieOptions = getSecureCookieOptions();

    // Production uses sameSite: 'none' for cross-origin, lax for dev
    const isProduction = process.env.NODE_ENV === "production";
    if (isProduction) {
      cookieOptions.sameSite = "none"; // Allow cross-origin in production
    }

    // Set httpOnly cookie for refresh token
    res.cookie("refreshToken", refreshToken, cookieOptions);

    // HIGH #5: Log without exposing sensitive data
    safeLog("🍪 Cookie configured", {
      secure: cookieOptions.secure,
      sameSite: cookieOptions.sameSite,
      httpOnly: cookieOptions.httpOnly,
      environment: process.env.NODE_ENV,
    });

    // Return access token and profile
    const profile = {
      userId: user._id,
      name: user.name,
      email: user.email,
      userType,
      ...(userType === "talent"
        ? {
            talentId: user.talentId,
            batchId: user.batchId,
            status: user.status,
            roles: user.roles, // Include roles array for admin access check
            adminPermissions: user.adminPermissions, // Include admin permissions
            canViewPayments: user.canViewPayments, // Include payment access flag
            xp: user.xp,
            streak: user.streak,
            longestStreak: user.longestStreak,
            badges: user.badges,
            phone: user.phone,
            companyEmail: user.companyEmail,
            jibbleEmployeeId: user.jibbleEmployeeId, // ⏰ For 3-hour check-up
            lastJibbleClockInTime: user.lastJibbleClockInTime, // ⏰ For 3-hour check-up
            profilePicture: user.profilePicture,
            group: user.group,
            designation: user.designation,
            employmentType: user.employmentType,
            createdAt: user.createdAt,
            hasAcceptedRules: user.hasAcceptedRules,
            rulesAcceptedAt: user.rulesAcceptedAt,
            rulesVersion: user.rulesVersion,
            country: user.country,
            timezone: user.timezone,
          }
        : {
            role: user.role,
            roleType: user.roleType, // ✅ Include new roleType field for permission system
            permissions: user.permissions,
            permissionMode: user.permissionMode, // ✅ Include permission mode
            customPermissions: user.customPermissions || [], // ✅ Include custom permissions
            canViewPayments: user.canViewPayments, // ✅ Include payment access flag
            canAccessTalentPortal: user.canAccessTalentPortal || false, // ✅ Include talent portal access
          }),
    };

    const response = {
      accessToken,
      profile,
    };

    // PRODUCTION FALLBACK: Include refresh token in response for cookie issues
    if (isProduction) {
      console.log(
        "🔄 Adding refresh token fallback for production cookie issues"
      );
      response._refreshTokenFallback = refreshToken;
      response._cookieDebug = {
        cookiesEnabled: req.cookies
          ? Object.keys(req.cookies).length > 0
          : false,
        hasExistingCookies: !!req.cookies?.refreshToken,
        browserInfo: req.get("User-Agent")?.substring(0, 50),
        timestamp: new Date().toISOString(),
      };
    }

    res.json(response);
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// @desc    Refresh access token
// @route   POST /auth/refresh
// @access  Public (requires refresh token cookie or fallback token)
export const refresh = async (req, res) => {
  try {
    let refreshToken = req.cookies.refreshToken || req.cookies.rt_backup;

    // Check for fallback token in request body or Authorization header
    if (!refreshToken && req.body.fallbackRefresh) {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith("Bearer ")) {
        refreshToken = authHeader.substring(7);
        console.log(
          "🔄 Using fallback refresh token from Authorization header"
        );
      }
    }

    if (!refreshToken) {
      console.log("❌ No refresh token found in cookies or fallback");
      return res.status(401).json({ error: "Refresh token required" });
    }

    console.log("✅ Refresh token found:", refreshToken ? "Yes" : "No");

    // Verify refresh token JWT
    let decoded;
    try {
      decoded = verifyRefreshToken(refreshToken);
    } catch (error) {
      console.log("❌ Invalid refresh token:", error.message);
      return res
        .status(401)
        .json({ error: "Invalid or expired refresh token" });
    }

    // Check if refresh token exists in database and is valid
    const tokenRecord = await RefreshToken.findOne({
      token: refreshToken,
      userId: decoded.userId,
      isRevoked: false,
    });

    if (!tokenRecord) {
      return res
        .status(401)
        .json({ error: "Refresh token is invalid or revoked" });
    }

    // Check if token is expired
    if (new Date() > tokenRecord.expiresAt) {
      return res.status(401).json({ error: "Refresh token has expired" });
    }

    // Get user data based on userType
    const { userType } = decoded;
    let user;

    if (userType === "admin") {
      user = await Admin.findById(decoded.userId);
      if (!user || !user.isActive) {
        return res.status(403).json({ error: "Admin account is not active" });
      }
    } else {
      user = await Talent.findById(decoded.userId);
      if (!user || user.status !== "active") {
        return res.status(403).json({ error: "Talent account is not active" });
      }
    }

    if (!user) {
      return res.status(401).json({ error: "User not found" });
    }

    // Generate new access token with appropriate payload
    const payload = {
      userId: user._id,
      email: user.email,
      userType,
      ...(userType === "talent"
        ? {
            talentId: user.talentId,
            status: user.status,
            batchId: user.batchId,
            assignedProject: user.assignedProject, // ✅ Include assigned project for task filtering
            hasFullAccess: user.hasFullAccess || false, // ✅ Include full access flag for cross-project tasks
            roles: user.roles || [], // ✅ Include roles for admin middleware
            adminPermissions: user.adminPermissions || [], // ✅ Include admin permissions
            canViewPayments: user.canViewPayments || false, // ✅ Include payment access
          }
        : {
            role: user.role,
            roleType: user.roleType, // ✅ Include new roleType field for JWT payload
            permissions: user.permissions,
            permissionMode: user.permissionMode, // ✅ Include permission mode in JWT
            customPermissions: user.customPermissions || [], // ✅ Include custom permissions in JWT
            canAccessTalentPortal: user.canAccessTalentPortal || false, // ✅ Include talent portal access
          }),
    };

    const accessToken = generateAccessToken(payload);

    // ✅ Return same profile as login
    const profile = {
      userId: user._id,
      name: user.name,
      email: user.email,
      userType,
      ...(userType === "talent"
        ? {
            talentId: user.talentId,
            batchId: user.batchId,
            status: user.status,
            xp: user.xp,
            streak: user.streak,
            longestStreak: user.longestStreak,
            badges: user.badges,
            phone: user.phone,
            companyEmail: user.companyEmail,
            jibbleEmployeeId: user.jibbleEmployeeId, // ⏰ For 3-hour check-up
            lastJibbleClockInTime: user.lastJibbleClockInTime, // ⏰ For 3-hour check-up
            profilePicture: user.profilePicture,
            group: user.group,
            designation: user.designation,
            employmentType: user.employmentType,
            createdAt: user.createdAt,
            hasAcceptedRules: user.hasAcceptedRules,
            rulesAcceptedAt: user.rulesAcceptedAt,
            rulesVersion: user.rulesVersion,
            country: user.country,
            timezone: user.timezone,
            roles: user.roles || [], // ✅ Include roles for admin access check
            adminPermissions: user.adminPermissions || [], // ✅ Include admin permissions
            canViewPayments: user.canViewPayments || false, // ✅ Include payment access flag
          }
        : {
            role: user.role,
            roleType: user.roleType, // ✅ Include new roleType field for permission system
            permissions: user.permissions,
            permissionMode: user.permissionMode, // ✅ Include permission mode (role/custom)
            customPermissions: user.customPermissions || [], // ✅ Include custom permissions array
            canAccessTalentPortal: user.canAccessTalentPortal || false, // ✅ Include talent portal access
          }),
    };

    res.json({
      accessToken,
      profile,
    });
  } catch (error) {
    console.error("Refresh error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// @desc    Logout talent
// @route   POST /auth/logout
// @access  Public (requires refresh token cookie)
export const logout = async (req, res) => {
  try {
    const refreshToken = req.cookies.refreshToken;

    if (refreshToken) {
      try {
        // Revoke refresh token in database
        await RefreshToken.updateOne(
          { token: refreshToken },
          { isRevoked: true }
        );
      } catch (error) {
        console.error("Token revocation error:", error);
      }
    }

    // Clear refresh token cookie (cross-origin setup)
    const isProduction = process.env.NODE_ENV === "production";
    const cookieOptions = {
      httpOnly: true,
      secure: isProduction,
      sameSite: isProduction ? "none" : "lax",
      path: "/",
    };

    res.clearCookie("refreshToken", cookieOptions);

    res.json({ success: true, message: "Logged out successfully" });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// @desc    Get current user profile
// @route   GET /api/me
// @access  Private
export const getMe = async (req, res) => {
  try {
    const { userType, userId } = req.user;

    let user;
    if (userType === "admin") {
      user = await Admin.findById(userId).select("-passwordHash");
    } else {
      user = await Talent.findById(userId)
        .select("-passwordHash")
        .populate("badges")
        .populate("batchId");
    }

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    const profile = {
      userId: user._id,
      name: user.name,
      email: user.email,
      userType,
      ...(userType === "talent"
        ? {
            talentId: user.talentId,
            batchId: user.batchId,
            status: user.status,
            xp: user.xp,
            level: user.level,
            streak: user.streak,
            longestStreak: user.longestStreak,
            badges: user.badges,
            levelBadges: user.levelBadges,
            phone: user.phone,
            companyEmail: user.companyEmail,
            jibbleEmployeeId: user.jibbleEmployeeId, // ⏰ For 3-hour check-up
            lastJibbleClockInTime: user.lastJibbleClockInTime, // ⏰ For 3-hour check-up
            profilePicture: user.profilePicture,
            group: user.group,
            designation: user.designation,
            employmentType: user.employmentType,
            createdAt: user.createdAt,
            hasAcceptedRules: user.hasAcceptedRules,
            rulesAcceptedAt: user.rulesAcceptedAt,
            rulesVersion: user.rulesVersion,
            country: user.country,
            timezone: user.timezone,
            roles: user.roles,
          }
        : {
            role: user.role,
            roleType: user.roleType, // ✅ Include new roleType field for getMe endpoint
            permissions: user.permissions,
            isActive: user.isActive,
            canAccessTalentPortal: user.canAccessTalentPortal || false, // ✅ Include talent portal access
          }),
    };

    res.json(profile);
  } catch (error) {
    console.error("Get user error:", error);
    res.status(500).json({ error: "Server error" });
  }
};

// @desc    Accept rules and terms
// @route   POST /api/auth/accept-rules
// @access  Private
export const acceptRules = async (req, res) => {
  try {
    const { userId, userType } = req.user;
    const { rulesVersion } = req.body;

    // Only talents need to accept rules, not admins
    if (userType !== "talent") {
      return res
        .status(400)
        .json({ error: "Rules acceptance is only for talent users" });
    }

    const talent = await Talent.findById(userId);
    if (!talent) {
      return res.status(404).json({ error: "Talent not found" });
    }

    // Update rules acceptance
    talent.hasAcceptedRules = true;
    talent.rulesAcceptedAt = new Date();
    talent.rulesVersion = rulesVersion || "v1.0";
    await talent.save();

    res.json({
      message: "Rules accepted successfully",
      data: {
        hasAcceptedRules: true,
        rulesAcceptedAt: talent.rulesAcceptedAt,
        rulesVersion: talent.rulesVersion,
      },
    });
  } catch (error) {
    console.error("Accept rules error:", error);
    res.status(500).json({ error: "Server error" });
  }
};
