import { useState, useEffect } from "react";
import {
  X,
  Plus,
  Trash2,
  Upload,
  AlertTriangle,
  AlertCircle,
  Info,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";

const priorityColors = {
  high: "bg-red-500/20 text-red-400 border-red-500/30",
  medium: "bg-orange-500/20 text-orange-400 border-orange-500/30",
  low: "bg-blue-500/20 text-blue-400 border-blue-500/30",
};

const priorityIcons = {
  high: AlertTriangle,
  medium: AlertCircle,
  low: Info,
};

export default function RejectSectionModal({
  isOpen,
  onClose,
  onSubmit,
  taskTitle,
  sectionNumber,
  sectionData,
  onEditScreenshot,
  loading = false,
}) {
  const [feedback, setFeedback] = useState("");
  const [issues, setIssues] = useState([
    {
      description: "",
      priority: "medium",
      screenshot: null,
      screenshotPreview: null,
    },
  ]);

  // Listen for annotated images from the annotation editor
  useEffect(() => {
    const handleAnnotatedImage = (event) => {
      const { file, preview } = event.detail;

      // Find first issue without a screenshot or add new issue
      const emptyIssueIndex = issues.findIndex((issue) => !issue.screenshot);

      if (emptyIssueIndex !== -1) {
        // Add to existing empty issue
        const newIssues = [...issues];
        newIssues[emptyIssueIndex].screenshot = file;
        newIssues[emptyIssueIndex].screenshotPreview = preview;
        setIssues(newIssues);
      } else {
        // Add new issue with this screenshot
        setIssues([
          ...issues,
          {
            description: "",
            priority: "medium",
            screenshot: file,
            screenshotPreview: preview,
          },
        ]);
      }
    };

    window.addEventListener("annotatedImageReady", handleAnnotatedImage);
    return () => {
      window.removeEventListener("annotatedImageReady", handleAnnotatedImage);
    };
  }, [issues]);

  const addIssue = () => {
    setIssues([
      ...issues,
      {
        description: "",
        priority: "medium",
        screenshot: null,
        screenshotPreview: null,
      },
    ]);
  };

  const removeIssue = (index) => {
    if (issues.length > 1) {
      const newIssues = issues.filter((_, i) => i !== index);
      setIssues(newIssues);
    }
  };

  const updateIssue = (index, field, value) => {
    const newIssues = [...issues];
    newIssues[index][field] = value;
    setIssues(newIssues);
  };

  const handleScreenshotChange = (index, file) => {
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        alert("Please upload an image file");
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert("Image size must be less than 5MB");
        return;
      }

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        updateIssue(index, "screenshotPreview", e.target.result);
      };
      reader.readAsDataURL(file);

      updateIssue(index, "screenshot", file);
    }
  };

  const removeScreenshot = (index) => {
    updateIssue(index, "screenshot", null);
    updateIssue(index, "screenshotPreview", null);
  };

  const handleSubmit = () => {
    // Validate issues
    const validIssues = issues.filter((issue) => issue.description.trim());

    if (validIssues.length === 0) {
      alert("Please add at least one issue with a description");
      return;
    }

    onSubmit({
      feedback: feedback.trim(),
      issues: validIssues,
    });
  };

  const handleClose = () => {
    setFeedback("");
    setIssues([
      {
        description: "",
        priority: "medium",
        screenshot: null,
        screenshotPreview: null,
      },
    ]);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-red-600">Request Revision</CardTitle>
              <CardDescription>
                Section {sectionNumber} of "{taskTitle}"
              </CardDescription>
            </div>
            <button
              onClick={handleClose}
              disabled={loading}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </CardHeader>

        {/* Body */}
        <CardContent className="flex-1 overflow-y-auto space-y-6">
          {/* Submitted Screenshots */}
          {sectionData?.screenshots && sectionData.screenshots.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-foreground mb-3">
                Submitted Screenshots ({sectionData.screenshots.length})
              </label>
              <div className="grid grid-cols-3 gap-3">
                {sectionData.screenshots.map((screenshot, idx) => {
                  const screenshotUrl =
                    typeof screenshot === "string"
                      ? screenshot
                      : screenshot?.url;
                  return (
                    <div key={idx} className="relative group">
                      <img
                        src={screenshotUrl}
                        alt={`Screenshot ${idx + 1}`}
                        className="w-full h-24 object-cover rounded-lg border-2 border-border"
                      />
                      <button
                        onClick={() =>
                          onEditScreenshot?.(screenshotUrl, idx, sectionNumber)
                        }
                        disabled={loading}
                        className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center text-white text-xs font-medium disabled:opacity-50"
                      >
                        ✏️ Edit & Add to Issue
                      </button>
                    </div>
                  );
                })}
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                💡 Click "Edit & Add to Issue" to annotate a screenshot and
                attach it to an issue below
              </p>
            </div>
          )}

          {/* Overall Feedback */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Overall Feedback (Optional)
            </label>
            <textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="General comments about the submission..."
              className="w-full px-4 py-3 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all resize-none"
              rows={3}
              disabled={loading}
            />
          </div>

          {/* Issues */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <label className="block text-sm font-medium text-foreground">
                Issues to Fix <span className="text-red-500">*</span>
              </label>
              <button
                onClick={addIssue}
                disabled={loading}
                className="flex items-center gap-2 px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Plus className="w-4 h-4" />
                Add Issue
              </button>
            </div>

            <div className="space-y-4">
              {issues.map((issue, index) => {
                const PriorityIcon = priorityIcons[issue.priority];

                return (
                  <div
                    key={index}
                    className="p-4 border border-border rounded-lg bg-card space-y-3"
                  >
                    {/* Issue Header */}
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-medium text-muted-foreground">
                            Issue #{index + 1}
                          </span>
                          {issues.length > 1 && (
                            <button
                              onClick={() => removeIssue(index)}
                              disabled={loading}
                              className="text-red-600 hover:text-red-700 transition-colors disabled:opacity-50"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>

                        {/* Priority Selector */}
                        <div className="flex gap-2 mb-3">
                          {["high", "medium", "low"].map((priority) => {
                            const Icon = priorityIcons[priority];
                            return (
                              <button
                                key={priority}
                                onClick={() =>
                                  updateIssue(index, "priority", priority)
                                }
                                disabled={loading}
                                className={`flex items-center gap-1.5 px-3 py-1.5 rounded-lg border text-xs font-medium transition-all disabled:opacity-50 ${
                                  issue.priority === priority
                                    ? priorityColors[priority]
                                    : "bg-background text-muted-foreground border-border hover:border-muted-foreground"
                                }`}
                              >
                                <Icon className="w-3.5 h-3.5" />
                                {priority.charAt(0).toUpperCase() +
                                  priority.slice(1)}
                              </button>
                            );
                          })}
                        </div>

                        {/* Description */}
                        <textarea
                          value={issue.description}
                          onChange={(e) =>
                            updateIssue(index, "description", e.target.value)
                          }
                          placeholder="Describe what needs to be fixed..."
                          className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 text-sm resize-none"
                          rows={2}
                          disabled={loading}
                        />
                      </div>
                    </div>

                    {/* Screenshot Upload */}
                    <div>
                      {!issue.screenshotPreview ? (
                        <label className="flex items-center justify-center gap-2 px-4 py-3 bg-background border-2 border-dashed border-border rounded-lg cursor-pointer hover:border-muted-foreground transition-colors">
                          <Upload className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">
                            Upload screenshot (optional)
                          </span>
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) =>
                              handleScreenshotChange(index, e.target.files[0])
                            }
                            className="hidden"
                            disabled={loading}
                          />
                        </label>
                      ) : (
                        <div className="relative inline-block">
                          <button
                            onClick={() => {
                              // Open preview modal
                              const modal = document.createElement("div");
                              modal.className =
                                "fixed inset-0 z-[60] flex items-center justify-center bg-black/80 backdrop-blur-sm";
                              modal.onclick = () => modal.remove();
                              modal.innerHTML = `
                                <div class="relative max-w-4xl max-h-[90vh] p-4">
                                  <img src="${issue.screenshotPreview}" class="max-w-full max-h-full rounded-lg" onclick="event.stopPropagation()" />
                                  <button class="absolute top-2 right-2 p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg" onclick="this.closest('.fixed').remove()">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                                  </button>
                                </div>
                              `;
                              document.body.appendChild(modal);
                            }}
                            className="relative group"
                          >
                            <img
                              src={issue.screenshotPreview}
                              alt="Screenshot preview"
                              className="w-32 h-32 object-cover rounded-lg border-2 border-border hover:border-blue-500 transition-colors"
                            />
                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                              <span className="text-white text-xs font-medium">
                                Click to preview
                              </span>
                            </div>
                          </button>
                          <button
                            onClick={() => removeScreenshot(index)}
                            disabled={loading}
                            className="absolute -top-2 -right-2 p-1.5 bg-red-600 hover:bg-red-700 text-white rounded-full transition-colors disabled:opacity-50 shadow-lg"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between pt-4 border-t border-border mt-6">
            <p className="text-sm text-muted-foreground">
              {issues.filter((i) => i.description.trim()).length} issue(s) added
            </p>
            <div className="flex gap-3">
              <button
                onClick={handleClose}
                disabled={loading}
                className="px-4 py-2 border border-border hover:bg-accent text-foreground rounded-lg transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={
                  loading ||
                  issues.filter((i) => i.description.trim()).length === 0
                }
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? "Sending..." : "Request Revision"}
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
