import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import AdminLayout from "../../../components/admin/AdminLayout";
import { useAuthStore } from "../../../store/authStore";
import { useAdminTaskStore } from "../../../store/adminTaskStore";
import { getUserRole } from "../../../utils/rolePermissions";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../components/ui/card";
import { Badge } from "../../../components/ui/badge";
import { HiPlus, HiTrash, HiDocumentText, HiPhotograph } from "react-icons/hi";
import { toast } from "sonner";

export default function CreateTask() {
  const navigate = useNavigate();
  const { user, isInitializing, accessToken } = useAuthStore();
  const { createTask, submitLoading } = useAdminTaskStore();

  // Basic task info
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [project, setProject] = useState("Talent Portal");
  const [category, setCategory] = useState("Currently Working on");
  const [taskType, setTaskType] = useState("section-based");
  const [priority, setPriority] = useState("medium");
  const [complexity, setComplexity] = useState("medium");
  const [xpValue, setXpValue] = useState("0");
  const [dueDate, setDueDate] = useState("");
  const [adminNotes, setAdminNotes] = useState("");
  const [status, setStatus] = useState("available"); // Store or Active
  const [tags, setTags] = useState([]);
  const [tagInput, setTagInput] = useState("");

  // Section-based task sections
  const [sections, setSections] = useState([
    { number: 1, title: "", description: "" },
  ]);

  // Checklist-based task items
  const [checklistItems, setChecklistItems] = useState([
    { number: 1, title: "", description: "", images: [] },
  ]);

  // File attachments
  const [attachments, setAttachments] = useState([]);

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;
    const userRole = getUserRole(user);
    if (!userRole) {
      navigate("/talent/dashboard");
    }
  }, [user, isInitializing, navigate]);

  // Section handlers
  const addSection = () => {
    setSections([
      ...sections,
      { number: sections.length + 1, title: "", description: "" },
    ]);
  };

  const removeSection = (index) => {
    if (sections.length === 1) {
      toast.error("Task must have at least one section");
      return;
    }
    const newSections = sections.filter((_, i) => i !== index);
    // Renumber sections
    setSections(
      newSections.map((section, i) => ({ ...section, number: i + 1 }))
    );
  };

  const updateSection = (index, field, value) => {
    setSections(
      sections.map((section, i) =>
        i === index ? { ...section, [field]: value } : section
      )
    );
  };

  // Checklist handlers
  const addChecklistItem = () => {
    setChecklistItems([
      ...checklistItems,
      {
        number: checklistItems.length + 1,
        title: "",
        description: "",
        images: [],
      },
    ]);
  };

  const removeChecklistItem = (index) => {
    if (checklistItems.length === 1) {
      toast.error("Task must have at least one checklist item");
      return;
    }
    const newItems = checklistItems.filter((_, i) => i !== index);
    // Renumber items
    setChecklistItems(newItems.map((item, i) => ({ ...item, number: i + 1 })));
  };

  const updateChecklistItemTitle = (index, value) => {
    setChecklistItems(
      checklistItems.map((item, i) =>
        i === index ? { ...item, title: value } : item
      )
    );
  };

  const updateChecklistItemDescription = (index, value) => {
    setChecklistItems(
      checklistItems.map((item, i) =>
        i === index ? { ...item, description: value } : item
      )
    );
  };

  // Checklist image handlers
  const handleChecklistImageChange = (itemIndex, e) => {
    const files = Array.from(e.target.files);
    const validImages = [];

    for (const file of files) {
      // Validate file type (images only)
      const validTypes = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/gif",
        "image/webp",
      ];

      if (!validTypes.includes(file.type)) {
        toast.error(`${file.name}: Only image files are allowed`);
        continue;
      }

      // Validate file size (5MB per image)
      if (file.size > 5 * 1024 * 1024) {
        toast.error(`${file.name}: Image size must not exceed 5MB`);
        continue;
      }

      validImages.push(file);
    }

    const currentImages = checklistItems[itemIndex].images || [];
    if (currentImages.length + validImages.length > 3) {
      toast.error("Maximum 3 images per checklist item");
      return;
    }

    setChecklistItems(
      checklistItems.map((item, i) =>
        i === itemIndex
          ? { ...item, images: [...(item.images || []), ...validImages] }
          : item
      )
    );
  };

  const removeChecklistImage = (itemIndex, imageIndex) => {
    setChecklistItems(
      checklistItems.map((item, i) =>
        i === itemIndex
          ? {
              ...item,
              images: item.images.filter((_, imgIdx) => imgIdx !== imageIndex),
            }
          : item
      )
    );
  };

  // File attachment handlers
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    const validFiles = [];

    for (const file of files) {
      // Validate file type
      const validTypes = [
        "application/pdf",
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/gif",
        "image/webp",
      ];

      if (!validTypes.includes(file.type)) {
        toast.error(`${file.name}: Only PDF and image files are allowed`);
        continue;
      }

      // Validate file size (10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error(`${file.name}: File size must not exceed 10MB`);
        continue;
      }

      validFiles.push(file);
    }

    if (attachments.length + validFiles.length > 5) {
      toast.error("Maximum 5 files allowed");
      return;
    }

    setAttachments([...attachments, ...validFiles]);
  };

  const removeAttachment = (index) => {
    setAttachments(attachments.filter((_, i) => i !== index));
  };

  // Validate form
  const validateForm = () => {
    if (!title.trim()) {
      toast.error("Task title is required");
      return false;
    }
    if (!description.trim()) {
      toast.error("Task description is required");
      return false;
    }

    if (taskType === "section-based") {
      // Validate sections
      for (let i = 0; i < sections.length; i++) {
        if (!sections[i].title.trim()) {
          toast.error(`Section ${i + 1} title is required`);
          return false;
        }
      }
    } else {
      // Validate checklist items
      const hasValidItem = checklistItems.some((item) => item.title.trim());
      if (!hasValidItem) {
        toast.error("At least one checklist item with title is required");
        return false;
      }
    }

    return true;
  };

  // Handle submit
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Create FormData for file upload
    const formData = new FormData();

    // Add basic fields
    formData.append("title", title.trim());
    formData.append("description", description.trim());
    formData.append("project", project);
    formData.append("category", category);
    formData.append("taskType", taskType);
    formData.append("hasSections", taskType === "section-based");
    formData.append("hasChecklist", taskType === "checklist-based");
    formData.append("priority", priority);
    formData.append("complexity", complexity);
    formData.append("status", status);

    console.log("🎯 Creating task with status:", status);

    if (dueDate) formData.append("dueDate", dueDate);
    if (adminNotes.trim()) formData.append("adminNotes", adminNotes.trim());
    if (xpValue) formData.append("xpValue", parseInt(xpValue));
    if (tags.length > 0) formData.append("tags", JSON.stringify(tags));

    // Add sections or checklist based on type
    if (taskType === "section-based") {
      const filteredSections = sections
        .filter((s) => s.title.trim())
        .map((s) => ({
          number: s.number,
          title: s.title.trim(),
          description: s.description.trim(),
        }));
      formData.append("sections", JSON.stringify(filteredSections));
    } else {
      const filteredChecklist = checklistItems
        .filter((item) => item.title.trim())
        .map((item, index) => ({
          number: item.number,
          title: item.title.trim(),
          description: item.description.trim(),
          completed: false,
          hasImages: item.images && item.images.length > 0,
          imageCount: item.images ? item.images.length : 0,
        }));
      formData.append("checklist", JSON.stringify(filteredChecklist));

      // Add checklist item images
      checklistItems.forEach((item, itemIndex) => {
        if (item.images && item.images.length > 0) {
          item.images.forEach((image) => {
            formData.append(`checklistImages_${itemIndex}`, image);
          });
        }
      });
    }

    // Add file attachments
    attachments.forEach((file) => {
      formData.append("attachments", file);
    });

    try {
      await createTask(formData);
      toast.success("Task created successfully!");
      navigate("/admin/tasks/all");
    } catch (error) {
      toast.error(error.message || "Failed to create task");
    }
  };

  return (
    <AdminLayout>
      <div className="flex-1 p-8">
        <div className="mb-6">
          <h2 className="text-3xl font-bold text-foreground">
            Create New Task
          </h2>
          <p className="text-muted-foreground mt-1">
            Create a section-based or checklist-based task for your team
          </p>

          {/* Horizontal Navigation Tabs */}
          <div className="flex gap-2 border-b border-border mt-4">
            <button
              onClick={() => navigate("/admin/tasks")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Dashboard
            </button>
            <button
              onClick={() => navigate("/admin/tasks/all")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Active Tasks
            </button>
            <button
              onClick={() => navigate("/admin/tasks/stored")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Store
            </button>
            <button
              onClick={() => navigate("/admin/tasks/completed")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Completed
            </button>
            <button
              onClick={() => navigate("/admin/tasks/requests")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Requests
            </button>
            <button
              onClick={() => navigate("/admin/tasks/create")}
              className="px-4 py-3 border-b-2 border-primary text-primary font-semibold transition-colors"
            >
              Create Task
            </button>
          </div>
        </div>

        <form
          onSubmit={handleSubmit}
          className="grid grid-cols-1 lg:grid-cols-2 gap-6"
        >
          {/* LEFT COLUMN - Basic Info */}
          <div className="space-y-6">
            <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-foreground">
                      Basic Information
                    </CardTitle>
                    <CardDescription className="text-muted-foreground">
                      Provide basic details about the task
                    </CardDescription>
                  </div>

                  {/* Store/Active Toggle - matches hiring dashboard Employee/Hiring pattern */}
                  <div className="flex items-center bg-muted rounded-lg p-0.5">
                    <button
                      type="button"
                      onClick={() => setStatus("stored")}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                        status === "stored"
                          ? "bg-background text-foreground shadow-sm"
                          : "text-muted-foreground hover:text-foreground"
                      }`}
                    >
                      Store
                    </button>
                    <button
                      type="button"
                      onClick={() => setStatus("available")}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                        status === "available"
                          ? "bg-background text-foreground shadow-sm"
                          : "text-muted-foreground hover:text-foreground"
                      }`}
                    >
                      Active
                    </button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Task Title <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="e.g., Build REST API"
                    className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                  />
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Description <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Detailed description of the task"
                    rows={3}
                    className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all resize-none"
                  />
                </div>

                {/* Project */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Project <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={project}
                    onChange={(e) => setProject(e.target.value)}
                    className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                  >
                    <option value="Talent Portal">Talent Portal</option>
                    <option value="ModelSuite">ModelSuite</option>
                  </select>
                  <p className="text-xs text-muted-foreground mt-1">
                    Which team will work on this task
                  </p>
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Workflow Status <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                  >
                    <option value="Currently Working on">
                      Currently Working on
                    </option>
                    <option value="Finished">Finished</option>
                    <option value="Future">Future</option>
                    <option value="Merged">Merged</option>
                    <option value="Needs Fixing">Needs Fixing</option>
                    <option value="Open">Open</option>
                    <option value="Redo">Redo</option>
                  </select>
                  <p className="text-xs text-muted-foreground mt-1">
                    Organize tasks by workflow status
                  </p>
                </div>

                {/* Task Type */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Task Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={taskType}
                    onChange={(e) => setTaskType(e.target.value)}
                    className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                  >
                    <option value="section-based">
                      Section-Based (Multi-step)
                    </option>
                    <option value="checklist-based">
                      Checklist-Based (Todo list)
                    </option>
                  </select>
                  <p className="text-xs text-muted-foreground mt-1">
                    {taskType === "section-based"
                      ? "Sections submitted one by one"
                      : "All items completed together"}
                  </p>
                </div>

                {/* Priority & Complexity */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Priority <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={priority}
                      onChange={(e) => setPriority(e.target.value)}
                      className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Complexity <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={complexity}
                      onChange={(e) => setComplexity(e.target.value)}
                      className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                    >
                      <option value="small">Small</option>
                      <option value="medium">Medium</option>
                      <option value="large">Large</option>
                    </select>
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Tags
                  </label>
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            const tag = tagInput.trim();
                            if (
                              tag &&
                              tag.length <= 30 &&
                              !tags.includes(tag)
                            ) {
                              setTags([...tags, tag]);
                              setTagInput("");
                            } else if (tag.length > 30) {
                              toast.error("Tag must be 30 characters or less");
                            } else if (tags.includes(tag)) {
                              toast.error("Tag already exists");
                            }
                          }
                        }}
                        placeholder="Type and press Enter to add tag"
                        className="flex-1 px-4 py-2.5 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const tag = tagInput.trim();
                          if (tag && tag.length <= 30 && !tags.includes(tag)) {
                            setTags([...tags, tag]);
                            setTagInput("");
                          } else if (tag.length > 30) {
                            toast.error("Tag must be 30 characters or less");
                          } else if (tags.includes(tag)) {
                            toast.error("Tag already exists");
                          }
                        }}
                        className="px-4 py-2.5 bg-rose-600 hover:bg-rose-700 text-white rounded-lg transition-colors"
                      >
                        Add
                      </button>
                    </div>
                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {tags.map((tag, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="px-3 py-1 text-sm flex items-center gap-2"
                          >
                            {tag}
                            <button
                              type="button"
                              onClick={() =>
                                setTags(tags.filter((_, i) => i !== index))
                              }
                              className="hover:text-red-400 transition-colors"
                            >
                              ×
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Add searchable tags (e.g., "UI", "Backend", "Bug Fix")
                  </p>
                </div>

                {/* XP Value & Due Date */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      XP Value
                    </label>
                    <input
                      type="number"
                      value={xpValue}
                      onChange={(e) => setXpValue(e.target.value)}
                      min="0"
                      step="10"
                      placeholder="Auto-calc"
                      className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Due Date
                    </label>
                    <input
                      type="date"
                      value={dueDate}
                      onChange={(e) => setDueDate(e.target.value)}
                      className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all [color-scheme:dark]"
                      style={{
                        colorScheme: "dark",
                      }}
                    />
                  </div>
                </div>

                {/* Admin Notes */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Admin Notes (Private)
                  </label>
                  <textarea
                    value={adminNotes}
                    onChange={(e) => setAdminNotes(e.target.value)}
                    placeholder="Internal notes visible only to admins"
                    rows={2}
                    className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all resize-none"
                  />
                </div>

                {/* File Attachments */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Attachments (PDFs & Images)
                  </label>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png,.gif,.webp"
                    onChange={handleFileChange}
                    className="w-full px-4 py-2.5 border border-input rounded-lg bg-background text-foreground file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90 cursor-pointer focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Max 5 files, 10MB each. Talents can download these files.
                  </p>

                  {/* Display selected files */}
                  {attachments.length > 0 && (
                    <div className="mt-3 space-y-2">
                      {attachments.map((file, index) => {
                        const isPdf = file.type === "application/pdf";
                        return (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-muted/30 rounded-lg border border-border"
                          >
                            <div className="flex items-center gap-2 flex-1 min-w-0">
                              {isPdf ? (
                                <HiDocumentText className="w-5 h-5 text-red-500 shrink-0" />
                              ) : (
                                <HiPhotograph className="w-5 h-5 text-blue-500 shrink-0" />
                              )}
                              <span className="text-sm text-foreground truncate">
                                {file.name}
                              </span>
                              <span className="text-xs text-muted-foreground shrink-0">
                                ({(file.size / 1024).toFixed(1)} KB)
                              </span>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeAttachment(index)}
                              className="text-red-500 hover:text-red-600 transition-colors ml-2"
                            >
                              <HiTrash className="w-4 h-4" />
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* RIGHT COLUMN - Sections or Checklist */}
          <div className="space-y-6">
            {/* Task Sections (Section-based) */}
            {taskType === "section-based" && (
              <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                <CardHeader className="flex flex-row items-center justify-between space-y-0">
                  <div>
                    <CardTitle className="text-foreground">
                      Task Sections
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      Define sequential steps for this task
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={addSection}
                    className="px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2 text-sm font-medium"
                  >
                    <HiPlus className="w-4 h-4" />
                    Add
                  </button>
                </CardHeader>
                <CardContent className="space-y-3 max-h-[600px] overflow-y-auto">
                  {sections.map((section, index) => (
                    <div
                      key={index}
                      className="p-3 border border-border rounded-lg bg-muted/30 space-y-2"
                    >
                      <div className="flex items-center justify-between">
                        <Badge
                          variant="outline"
                          className="text-foreground text-xs"
                        >
                          Section {section.number}
                        </Badge>
                        {sections.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeSection(index)}
                            className="text-red-500 hover:text-red-600 transition-colors"
                          >
                            <HiTrash className="w-4 h-4" />
                          </button>
                        )}
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-foreground mb-1">
                          Title <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          value={section.title}
                          onChange={(e) =>
                            updateSection(index, "title", e.target.value)
                          }
                          placeholder="e.g., Setup Backend"
                          className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all text-sm"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-foreground mb-1">
                          Description
                        </label>
                        <textarea
                          value={section.description}
                          onChange={(e) =>
                            updateSection(index, "description", e.target.value)
                          }
                          placeholder="Instructions for this section"
                          rows={2}
                          className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all resize-none text-sm"
                        />
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Checklist Items (Checklist-based) */}
            {taskType === "checklist-based" && (
              <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                <CardHeader className="flex flex-row items-center justify-between space-y-0">
                  <div>
                    <CardTitle className="text-foreground">
                      Checklist Items
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      Define todo items for this task
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={addChecklistItem}
                    className="px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2 text-sm font-medium"
                  >
                    <HiPlus className="w-4 h-4" />
                    Add
                  </button>
                </CardHeader>
                <CardContent className="space-y-3 max-h-[600px] overflow-y-auto">
                  {checklistItems.map((item, index) => (
                    <div
                      key={index}
                      className="p-3 border border-border rounded-lg bg-muted/30 space-y-2"
                    >
                      <div className="flex items-start gap-2">
                        <Badge
                          variant="outline"
                          className="mt-1.5 text-foreground shrink-0 text-xs"
                        >
                          {item.number}
                        </Badge>
                        <div className="flex-1 space-y-2">
                          <input
                            type="text"
                            value={item.title}
                            onChange={(e) =>
                              updateChecklistItemTitle(index, e.target.value)
                            }
                            placeholder="Enter checklist item title (e.g., Fix login bug)"
                            className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all text-sm font-medium"
                          />
                          <textarea
                            value={item.description}
                            onChange={(e) =>
                              updateChecklistItemDescription(
                                index,
                                e.target.value
                              )
                            }
                            placeholder="Enter detailed description (optional)"
                            rows={3}
                            className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all text-sm resize-none"
                          />
                        </div>
                        {checklistItems.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeChecklistItem(index)}
                            className="text-red-500 hover:text-red-600 transition-colors mt-1.5"
                          >
                            <HiTrash className="w-4 h-4" />
                          </button>
                        )}
                      </div>

                      {/* Image Upload for Checklist Item */}
                      <div className="ml-7">
                        <div className="flex items-center gap-2">
                          <label className="cursor-pointer">
                            <input
                              type="file"
                              multiple
                              accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                              onChange={(e) =>
                                handleChecklistImageChange(index, e)
                              }
                              className="hidden"
                            />
                            <div className="px-3 py-1.5 bg-blue-600/20 text-blue-400 rounded-lg hover:bg-blue-600/30 transition-colors flex items-center gap-1.5 text-xs font-medium">
                              <HiPhotograph className="w-4 h-4" />
                              Add Images
                            </div>
                          </label>
                          <span className="text-xs text-muted-foreground">
                            Max 3 images (5MB each)
                          </span>
                        </div>

                        {/* Display attached images */}
                        {item.images && item.images.length > 0 && (
                          <div className="mt-2 space-y-1.5">
                            {item.images.map((image, imgIndex) => (
                              <div
                                key={imgIndex}
                                className="flex items-center justify-between p-1.5 bg-background rounded border border-border"
                              >
                                <div className="flex items-center gap-2 flex-1 min-w-0">
                                  <HiPhotograph className="w-4 h-4 text-blue-500 shrink-0" />
                                  <span className="text-xs text-foreground truncate">
                                    {image.name}
                                  </span>
                                  <span className="text-xs text-muted-foreground shrink-0">
                                    ({(image.size / 1024).toFixed(0)} KB)
                                  </span>
                                </div>
                                <button
                                  type="button"
                                  onClick={() =>
                                    removeChecklistImage(index, imgIndex)
                                  }
                                  className="text-red-500 hover:text-red-600 transition-colors ml-2"
                                >
                                  <HiTrash className="w-3.5 h-3.5" />
                                </button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Actions - Move to right column bottom */}
            <div className="flex items-center gap-4">
              <button
                type="submit"
                disabled={submitLoading}
                className="flex-1 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 font-medium"
              >
                {submitLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <HiPlus className="w-5 h-5" />
                    Create Task
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={() => navigate("/admin/tasks/all")}
                className="px-6 py-3 border border-border rounded-lg hover:bg-muted transition-colors text-foreground font-medium"
              >
                Cancel
              </button>
            </div>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
}
