import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import AdminLayout from "../../../components/admin/AdminLayout";
import TagInput from "../../../components/hiring/TagInput";
import StatusBadge from "../../../components/hiring/StatusBadge";
import { useJobStore } from "../../../store/hiring/jobStore";
import { useAuthStore } from "../../../store/authStore";
import COMMON_SKILLS from "../../../data/commonSkills";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../components/ui/card";
import {
  HiArrowLeft,
  HiSave,
  HiCheckCircle,
  HiEye,
  HiBriefcase,
  HiX,
  HiClipboardCopy,
  HiExternalLink,
} from "react-icons/hi";

export default function CreateJob() {
  const navigate = useNavigate();
  const { accessToken } = useAuthStore();
  const { createJob, publishJob, loading } = useJobStore();

  // Form state - MATCHES BACKEND SCHEMA
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    companyInfo: "",
    responsibilities: "", // String, not array
    experienceLevel: "",
    jobType: "",
    compensationType: "paid",
  });

  const [errors, setErrors] = useState({});
  const [showPreview, setShowPreview] = useState(false);
  const [publishedJob, setPublishedJob] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [copied, setCopied] = useState(false);

  // Image upload state
  const [backgroundImage, setBackgroundImage] = useState(null);
  const [bannerImage, setBannerImage] = useState(null);
  const [backgroundPreview, setBackgroundPreview] = useState(null);
  const [bannerPreview, setBannerPreview] = useState(null);

  // Quill modules configuration
  const quillModules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ indent: "-1" }, { indent: "+1" }],
      ["link"],
      ["clean"],
    ],
  };

  const quillFormats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "indent",
    "link",
  ];

  // Handle input changes
  const handleChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error for this field
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: null }));
    }
  };

  // Handle background image upload
  const handleBackgroundImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        alert("Please upload an image file");
        return;
      }
      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert("Image size must be less than 5MB");
        return;
      }
      setBackgroundImage(file);
      setBackgroundPreview(URL.createObjectURL(file));
    }
  };

  // Handle banner image upload
  const handleBannerImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        alert("Please upload an image file");
        return;
      }
      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert("Image size must be less than 5MB");
        return;
      }
      setBannerImage(file);
      setBannerPreview(URL.createObjectURL(file));
    }
  };

  // Remove background image
  const removeBackgroundImage = () => {
    setBackgroundImage(null);
    setBackgroundPreview(null);
  };

  // Remove banner image
  const removeBannerImage = () => {
    setBannerImage(null);
    setBannerPreview(null);
  };

  // Validate form - MATCHES BACKEND REQUIRED FIELDS
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) newErrors.title = "Job title is required";
    if (!formData.description.trim())
      newErrors.description = "Job description is required";
    if (!formData.companyInfo.trim())
      newErrors.companyInfo = "Company information is required";
    if (!formData.responsibilities.trim())
      newErrors.responsibilities = "Responsibilities are required";
    if (!formData.experienceLevel)
      newErrors.experienceLevel = "Experience level is required";
    if (!formData.jobType) newErrors.jobType = "Job type is required";
    if (!formData.compensationType)
      newErrors.compensationType = "Compensation type is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle save as draft
  const handleSaveDraft = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // Create FormData for file uploads
      const formDataToSend = new FormData();

      // Append all job data
      Object.keys(formData).forEach((key) => {
        if (formData[key]) {
          formDataToSend.append(key, formData[key]);
        }
      });

      formDataToSend.append("location", "Remote");

      // Append images if present
      if (backgroundImage) {
        formDataToSend.append("backgroundImage", backgroundImage);
      }
      if (bannerImage) {
        formDataToSend.append("bannerImage", bannerImage);
      }

      await createJob(accessToken, formDataToSend, true); // true = isFormData
      navigate("/admin/hiring/jobs");
    } catch (error) {
      console.error("Create job error:", error);
    }
  };

  // Handle publish
  const handlePublish = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // Create FormData for file uploads
      const formDataToSend = new FormData();

      // Append all job data
      Object.keys(formData).forEach((key) => {
        if (formData[key]) {
          formDataToSend.append(key, formData[key]);
        }
      });

      formDataToSend.append("location", "Remote");

      // Append images if present
      if (backgroundImage) {
        formDataToSend.append("backgroundImage", backgroundImage);
      }
      if (bannerImage) {
        formDataToSend.append("bannerImage", bannerImage);
      }

      // First create as draft
      const createdJob = await createJob(accessToken, formDataToSend, true); // true = isFormData

      // Then publish it
      const published = await publishJob(accessToken, createdJob._id);

      // Show success modal with application link
      setPublishedJob(published);
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Publish job error:", error);
    }
  };

  // Copy link to clipboard
  const copyToClipboard = () => {
    if (publishedJob?.slug) {
      const link = `${window.location.origin}/apply/${publishedJob.slug}`;
      navigator.clipboard.writeText(link);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  return (
    <AdminLayout>
      <div className="flex-1 p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 relative z-10">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate("/admin/hiring/jobs")}
              className="p-2 hover:bg-accent rounded-lg transition-colors"
            >
              <HiArrowLeft className="w-5 h-5 text-foreground" />
            </button>
            <div>
              <h2 className="text-xl font-bold text-white">Create New Job</h2>
              <p className="text-gray-400 mt-1">
                Fill in the details to post a new job
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowPreview(!showPreview)}
              className="flex items-center gap-2 px-4 py-1 border border-border/40 hover:bg-accent text-foreground rounded-lg transition-colors font-medium"
            >
              <HiEye className="w-4 h-4" />
              <span>{showPreview ? "Hide" : "Show"} Preview</span>
            </button>
            <button
              onClick={handleSaveDraft}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-1 border border-border/40 hover:bg-accent text-foreground rounded-lg transition-colors font-medium disabled:opacity-50"
            >
              <HiSave className="w-4 h-4" />
              <span>Save Draft</span>
            </button>
            <button
              onClick={handlePublish}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-1 bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg transition-colors font-medium disabled:opacity-50 shadow-lg"
            >
              <HiCheckCircle className="w-4 h-4" />
              <span>Publish Now</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Form Column */}
          <div className="space-y-4">
            {/* Basic Information */}
            <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
              <CardHeader>
                <CardTitle className="text-base font-semibold">
                  Basic Information
                </CardTitle>
                <CardDescription>
                  Essential details about the position
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Job Title */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Job Title <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleChange("title", e.target.value)}
                    placeholder="e.g., Senior React Developer"
                    className={`w-full px-3 py-1.5 border ${
                      errors.title ? "border-red-500" : "border-input"
                    } rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all`}
                  />
                  {errors.title && (
                    <p className="text-xs text-red-500 mt-1">{errors.title}</p>
                  )}
                </div>

                {/* Experience Level */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Experience Level <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.experienceLevel}
                    onChange={(e) =>
                      handleChange("experienceLevel", e.target.value)
                    }
                    className={`w-full px-3 py-1.5 border ${
                      errors.experienceLevel ? "border-red-500" : "border-input"
                    } rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all`}
                  >
                    <option value="">Select experience level</option>
                    <option value="fresher">Fresher</option>
                    <option value="0-1">0-1 Years</option>
                    <option value="1-2">1-2 Years</option>
                    <option value="2-3">2-3 Years</option>
                    <option value="3-5">3-5 Years</option>
                    <option value="5+">5+ Years</option>
                  </select>
                  {errors.experienceLevel && (
                    <p className="text-xs text-red-500 mt-1">
                      {errors.experienceLevel}
                    </p>
                  )}
                </div>

                {/* Job Type */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Job Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.jobType}
                    onChange={(e) => handleChange("jobType", e.target.value)}
                    className={`w-full px-3 py-1.5 border ${
                      errors.jobType ? "border-red-500" : "border-input"
                    } rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all`}
                  >
                    <option value="">Select job type</option>
                    <option value="full-time">Full-time</option>
                    <option value="part-time">Part-time</option>
                    <option value="contract">Contract</option>
                    <option value="internship">Internship</option>
                  </select>
                  {errors.jobType && (
                    <p className="text-xs text-red-500 mt-1">
                      {errors.jobType}
                    </p>
                  )}
                </div>

                {/* Compensation Type */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Compensation Type <span className="text-red-500">*</span>
                  </label>
                  <div className="flex gap-4">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="compensationType"
                        value="paid"
                        checked={formData.compensationType === "paid"}
                        onChange={(e) =>
                          handleChange("compensationType", e.target.value)
                        }
                        className="w-4 h-4 text-muted-foreground focus:ring-muted/50"
                      />
                      <span className="text-sm text-white">Paid</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="compensationType"
                        value="unpaid"
                        checked={formData.compensationType === "unpaid"}
                        onChange={(e) =>
                          handleChange("compensationType", e.target.value)
                        }
                        className="w-4 h-4 text-muted-foreground focus:ring-muted/50"
                      />
                      <span className="text-sm text-white">Unpaid</span>
                    </label>
                  </div>
                  {errors.compensationType && (
                    <p className="text-xs text-red-500 mt-1">
                      {errors.compensationType}
                    </p>
                  )}
                </div>

                {/* Location (Read-only) */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Location
                  </label>
                  <input
                    type="text"
                    value="Remote"
                    readOnly
                    className="w-full px-3 py-1.5 border border-input rounded-lg bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-muted-foreground cursor-not-allowed"
                  />
                  <p className="text-xs text-slate-300 mt-1">
                    All positions are remote (default)
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Job Description */}
            <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
              <CardHeader>
                <CardTitle className="text-base font-semibold">
                  Job Description
                </CardTitle>
                <CardDescription>
                  Detailed description of the role
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  className={
                    errors.description ? "ring-2 ring-red-500 rounded-lg" : ""
                  }
                >
                  <ReactQuill
                    theme="snow"
                    value={formData.description}
                    onChange={(value) => handleChange("description", value)}
                    modules={quillModules}
                    formats={quillFormats}
                    placeholder="Describe the role, team, and what makes this opportunity great..."
                    className="bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 rounded-lg"
                  />
                </div>
                {errors.description && (
                  <p className="text-xs text-red-500 mt-2">
                    {errors.description}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Company Information */}
            <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
              <CardHeader>
                <CardTitle className="text-base font-semibold">
                  Company Information
                </CardTitle>
                <CardDescription>
                  Tell candidates about your company
                </CardDescription>
              </CardHeader>
              <CardContent>
                <textarea
                  value={formData.companyInfo}
                  onChange={(e) => handleChange("companyInfo", e.target.value)}
                  placeholder="Describe your company, culture, mission, and what makes it a great place to work..."
                  rows={6}
                  maxLength={2000}
                  className={`w-full px-4 py-3 border ${
                    errors.companyInfo ? "border-red-500" : "border-input"
                  } rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 transition-all resize-none`}
                />
                <div className="flex justify-between items-center mt-1">
                  {errors.companyInfo ? (
                    <p className="text-xs text-red-500">{errors.companyInfo}</p>
                  ) : (
                    <p className="text-xs text-muted-foreground">
                      Max 2000 characters
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    {formData.companyInfo.length}/2000
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Responsibilities */}
            <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
              <CardHeader>
                <CardTitle className="text-base font-semibold">
                  Responsibilities <span className="text-red-500">*</span>
                </CardTitle>
                <CardDescription>
                  Key duties and day-to-day tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  className={
                    errors.responsibilities
                      ? "ring-2 ring-red-500 rounded-lg"
                      : ""
                  }
                >
                  <ReactQuill
                    theme="snow"
                    value={formData.responsibilities}
                    onChange={(value) =>
                      handleChange("responsibilities", value)
                    }
                    modules={quillModules}
                    formats={quillFormats}
                    placeholder="List the key responsibilities and tasks for this role..."
                    className="bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 rounded-lg"
                  />
                </div>
                {errors.responsibilities && (
                  <p className="text-xs text-red-500 mt-2">
                    {errors.responsibilities}
                  </p>
                )}
                <p className="text-xs text-slate-300 mt-2">
                  Max 3000 characters
                </p>
              </CardContent>
            </Card>

            {/* Visual Branding */}
            <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
              <CardHeader>
                <CardTitle className="text-base font-semibold">
                  Visual Branding (Optional)
                </CardTitle>
                <CardDescription>
                  Customize the look of your job application page
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Background Image */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Background Image
                  </label>
                  <p className="text-xs text-muted-foreground mb-3">
                    Subtle full-page background (will be blurred). Recommended:
                    1920x1080px
                  </p>

                  {backgroundPreview ? (
                    <div className="relative group">
                      <img
                        src={backgroundPreview}
                        alt="Background preview"
                        className="w-full h-40 object-cover rounded-lg border-2 border-border"
                      />
                      <button
                        type="button"
                        onClick={removeBackgroundImage}
                        className="absolute top-2 right-2 p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-border/50 rounded-lg cursor-pointer bg-card/50 hover:bg-card/70 transition-all">
                      <div className="flex flex-col items-center justify-center py-4">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-8 w-8 text-muted-foreground mb-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                        <p className="text-sm text-muted-foreground">
                          Click to upload background image
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          PNG, JPG, WEBP up to 5MB
                        </p>
                      </div>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleBackgroundImageChange}
                        className="hidden"
                      />
                    </label>
                  )}
                </div>

                {/* Banner Image */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Banner Image
                  </label>
                  <p className="text-xs text-muted-foreground mb-3">
                    Prominent top header banner. Recommended: 1200x400px
                  </p>

                  {bannerPreview ? (
                    <div className="relative group">
                      <img
                        src={bannerPreview}
                        alt="Banner preview"
                        className="w-full h-40 object-cover rounded-lg border-2 border-border"
                      />
                      <button
                        type="button"
                        onClick={removeBannerImage}
                        className="absolute top-2 right-2 p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-border/50 rounded-lg cursor-pointer bg-card/50 hover:bg-card/70 transition-all">
                      <div className="flex flex-col items-center justify-center py-4">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-8 w-8 text-muted-foreground mb-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                        <p className="text-sm text-muted-foreground">
                          Click to upload banner image
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          PNG, JPG, WEBP up to 5MB
                        </p>
                      </div>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleBannerImageChange}
                        className="hidden"
                      />
                    </label>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview Column */}
          {showPreview && (
            <div className="lg:sticky lg:top-8 h-fit">
              <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
                <CardHeader className="border-b border-border/50 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70">
                  <div className="flex items-center gap-3">
                    <div className="h-12 w-12 rounded-xl bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 backdrop-blur-sm flex items-center justify-center ring-1 ring-border/20">
                      <HiBriefcase className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-xl font-bold">
                        {formData.title || "Job Title"}
                      </CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-sm text-muted-foreground">
                          {formData.experienceLevel || "Experience Level"}
                        </span>
                        <span className="text-muted-foreground">•</span>
                        <span className="text-sm text-muted-foreground">
                          Remote
                        </span>
                        <span className="text-muted-foreground">•</span>
                        <span className="text-sm text-muted-foreground">
                          {formData.jobType || "Job Type"}
                        </span>
                        <span className="text-muted-foreground">•</span>
                        <span className="text-sm text-muted-foreground capitalize">
                          {formData.compensationType}
                        </span>
                      </div>
                    </div>
                    <StatusBadge status="draft" type="job" />
                  </div>
                </CardHeader>
                <CardContent className="pt-6 space-y-4">
                  {/* Company Info */}
                  {formData.companyInfo && (
                    <div>
                      <h3 className="text-sm font-semibold text-foreground mb-2">
                        About the Company
                      </h3>
                      <p className="text-sm text-slate-300 whitespace-pre-wrap">
                        {formData.companyInfo}
                      </p>
                    </div>
                  )}

                  {/* Description */}
                  <div>
                    <h3 className="text-sm font-semibold text-foreground mb-2">
                      About the Role
                    </h3>
                    <div
                      className="text-sm text-muted-foreground prose prose-sm dark:prose-invert max-w-none"
                      dangerouslySetInnerHTML={{
                        __html:
                          formData.description ||
                          "<p>No description yet...</p>",
                      }}
                    />
                  </div>

                  {/* Responsibilities */}
                  {formData.responsibilities && (
                    <div>
                      <h3 className="text-sm font-semibold text-foreground mb-2">
                        Responsibilities
                      </h3>
                      <div
                        className="text-sm text-muted-foreground prose prose-sm dark:prose-invert max-w-none"
                        dangerouslySetInnerHTML={{
                          __html: formData.responsibilities,
                        }}
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Success Modal */}
        {showSuccessModal && publishedJob && (
          <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-border rounded-xl shadow-2xl max-w-lg w-full">
              <div className="p-6 border-b border-border/50 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div className="h-14 w-14 rounded-xl bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 flex items-center justify-center ring-2 ring-border/20">
                      <HiCheckCircle className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white">
                        Job Published Successfully!
                      </h3>
                      <p className="text-sm text-slate-300 mt-1">
                        {publishedJob.title}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setShowSuccessModal(false);
                      navigate("/admin/hiring/jobs");
                    }}
                    className="p-2 hover:bg-card/90 hover:backdrop-blur hover:supports-[backdrop-filter]:bg-card/70 rounded-lg transition-colors"
                  >
                    <HiX className="w-4 h-4 text-muted-foreground" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-semibold text-foreground mb-3">
                    Application Link
                  </label>
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={`${window.location.origin}/apply/${publishedJob.slug}`}
                      readOnly
                      className="flex-1 px-4 py-3 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 border border-border rounded-lg text-foreground text-sm font-mono"
                    />
                    <button
                      onClick={copyToClipboard}
                      className="p-3 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 hover:bg-card/80 text-foreground border border-border/40 rounded-lg transition-colors"
                      title="Copy to clipboard"
                    >
                      <HiClipboardCopy className="w-4 h-4 text-muted-foreground" />
                    </button>
                    <a
                      href={`${window.location.origin}/apply/${publishedJob.slug}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-3 border border-border hover:bg-card/90 hover:backdrop-blur hover:supports-[backdrop-filter]:bg-card/70 text-foreground rounded-lg transition-colors"
                      title="Open in new tab"
                    >
                      <HiExternalLink className="w-4 h-4" />
                    </a>
                  </div>
                  {copied && (
                    <p className="text-xs text-emerald-600 dark:text-emerald-400 mt-2 font-medium">
                      ✓ Copied to clipboard!
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground mt-3">
                    Share this link with candidates to apply for this position.
                    The link is now live and accepting applications.
                  </p>
                </div>

                <div className="pt-4 border-t border-border/50 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    <span className="font-semibold text-white">Status:</span>{" "}
                    <StatusBadge status="published" type="job" />
                  </div>
                  <button
                    onClick={() => {
                      setShowSuccessModal(false);
                      navigate("/admin/hiring/jobs");
                    }}
                    className="px-3 py-1.5 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-foreground border border-border/40 rounded-lg transition-colors font-medium"
                  >
                    Go to Jobs List
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
