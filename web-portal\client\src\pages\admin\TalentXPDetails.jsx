import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useAuthStore } from "../../store/authStore";
import { getUserRole } from '../../utils/rolePermissions';
import AdminLayout from "../../components/admin/AdminLayout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";
import { Badge } from "../../components/ui/badge";
import { HiArrowLeft, HiCalendar, HiTrendingUp, HiFire } from "react-icons/hi";

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";

export default function TalentXPDetails() {
  const navigate = useNavigate();
  const { talentId } = useParams();
  const { user, accessToken, isInitializing } = useAuthStore();
  const [talentInfo, setTalentInfo] = useState(null);
  const [xpHistory, setXpHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedMonth, setSelectedMonth] = useState("");
  const [availableMonths, setAvailableMonths] = useState([]);

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;

    if (!user || !hasAdminAccess(user)) {
      navigate("/talent/dashboard");
    }
  }, [user, navigate, isInitializing]);

  // Fetch talent info and XP history
  useEffect(() => {
    if (hasAdminAccess(user) && talentId) {
      fetchTalentXPDetails();
    }
  }, [user, talentId, selectedMonth]);

  async function fetchTalentXPDetails() {
    try {
      setLoading(true);

      // Fetch talent basic info
      const talentResponse = await fetch(`${API_URL}/admin/talents/${talentId}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        credentials: "include",
      });

      if (!talentResponse.ok) {
        throw new Error("Failed to fetch talent info");
      }

      const talentData = await talentResponse.json();
      setTalentInfo(talentData.talent);

      // Fetch XP history from DailyXP collection with optional month filter
      let url = `${API_URL}/admin/talents/${talentId}/xp-history`;
      if (selectedMonth) {
        url += `?month=${selectedMonth}`;
      }

      const xpResponse = await fetch(url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        credentials: "include",
      });

      if (!xpResponse.ok) {
        throw new Error("Failed to fetch XP history");
      }

      const xpData = await xpResponse.json();
      setXpHistory(xpData.history || []);
      setAvailableMonths(xpData.availableMonths || []);
    } catch (err) {
      console.error("Error fetching talent XP details:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }

  // Show loading during initialization
  if (isInitializing || loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  // Show error if user doesn't have admin access
  if (!user || !hasAdminAccess(user)) {
    return null;
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="min-h-screen p-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-red-400 text-center py-10">
              Error: {error}
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  // Calculate totals from history
  const totalXP = talentInfo?.xp || 0;
  const totalCheckinXP = xpHistory.reduce((sum, day) => sum + (day.checkinXP || 0), 0);
  const totalReportXP = xpHistory.reduce((sum, day) => sum + (day.reportXP || 0), 0);
  const totalMeetingXP = xpHistory.reduce((sum, day) => sum + (day.meetingXP || 0), 0);
  const totalHoursBonus = xpHistory.reduce((sum, day) => sum + (day.hoursBonus || 0), 0);
  const totalStreakBonus = xpHistory.reduce((sum, day) => sum + (day.streakBonus || 0), 0);
  const totalBadgeXP = xpHistory.reduce((sum, day) => sum + (day.badgeXP || 0), 0);

  return (
    <AdminLayout>
      <div className="min-h-screen p-4">
        <div className="max-w-7xl mx-auto space-y-4">
          {/* Header (compact): back button only — main title moved into summary card */}
          <div className="flex items-center">
            <button
              onClick={() => navigate("/admin/leaderboard")}
              className="p-2 hover:bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 rounded-lg transition-colors"
            >
              <HiArrowLeft className="h-4 w-4" />
            </button>
          </div>

          {/* Talent Summary Card */}
          <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div
                  className="flex-shrink-0 w-20 h-20 rounded-full flex items-center justify-center overflow-hidden text-2xl font-semibold"
                  style={{
                    background: talentInfo?.profilePicture
                      ? 'transparent'
                      : 'rgba(255, 255, 255, 0.1)',
                  }}
                >
                  {talentInfo?.profilePicture ? (
                    <img
                      src={talentInfo.profilePicture}
                      alt={talentInfo.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-white">
                      {talentInfo?.name?.charAt(0).toUpperCase()}
                    </span>
                  )}
                </div>
                <div className="flex-1 min-w-0 grid grid-cols-3 gap-4">
                  <div className="min-w-0">
                    <p className="text-sm text-slate-300">Total XP</p>
                    <p className="text-lg md:text-xl font-bold text-primary break-words">{totalXP.toLocaleString()}</p>
                  </div>
                  <div className="min-w-0">
                    <p className="text-sm text-slate-300">Current Streak</p>
                    <div className="flex items-center gap-2">
                      <p className="text-lg md:text-xl font-bold break-words">{talentInfo?.streak || 0}</p>
                    </div>
                  </div>
                  <div className="min-w-0">
                    <p className="text-sm text-slate-300">Days Tracked</p>
                    <p className="text-lg md:text-xl font-bold break-words">{xpHistory.length}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* XP Breakdown Summary */}
          <div className="space-y-4">
            {selectedMonth && (
              <p className="text-sm text-slate-300">
                Showing XP breakdown for: <span className="font-semibold">
                  {new Date(selectedMonth + '-01').toLocaleDateString('en-US', {
                    month: 'long',
                    year: 'numeric'
                  })}
                </span>
              </p>
            )}
            <div className="grid grid-cols-2 gap-4 lg:grid-cols-6 ">
              <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xs font-medium text-muted-foreground">
                    Check-in XP
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-lg md:text-xl font-bold break-words min-w-0">{totalCheckinXP}</div>
                </CardContent>
              </Card>

              <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xs font-medium text-muted-foreground">
                    Report XP
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-lg md:text-xl font-bold break-words min-w-0">{totalReportXP}</div>
                </CardContent>
              </Card>

              <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xs font-medium text-muted-foreground">
                    Meeting XP
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-lg md:text-xl font-bold break-words min-w-0">{totalMeetingXP}</div>
                </CardContent>
              </Card>

              <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xs font-medium text-muted-foreground">
                    Hours Bonus
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-lg md:text-xl font-bold break-words min-w-0">{totalHoursBonus}</div>
                </CardContent>
              </Card>

              <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xs font-medium text-muted-foreground">
                    Streak Bonus
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-lg md:text-xl font-bold break-words min-w-0">{totalStreakBonus}</div>
                </CardContent>
              </Card>

              <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xs font-medium text-muted-foreground">
                    Badge XP
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-lg md:text-xl font-bold break-words min-w-0">{totalBadgeXP}</div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Daily XP History Table */}
          <Card className="border-border/40 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <HiCalendar className="hidden sm:inline-block h-4 w-4 text-primary" />
                  Daily XP History
                </CardTitle>
                <div className="flex items-center gap-2">
                  <label className="text-sm text-muted-foreground">Filter by Month:</label>
                  <select
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(e.target.value)}
                    className="px-3 py-1.5 text-sm rounded-md border border-border bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">All Months</option>
                    {availableMonths.map((month) => (
                      <option key={month} value={month}>
                        {new Date(month + '-01').toLocaleDateString('en-US', {
                          month: 'long',
                          year: 'numeric'
                        })}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {xpHistory.length === 0 ? (
                <div className="text-muted-foreground text-center py-10">
                  No XP history available yet.
                </div>
              ) : (
                <div className="rounded-md border border-border/40 max-h-[600px] overflow-y-auto">
                  <Table>
                    <TableHeader className="sticky top-0 bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 z-10">
                      <TableRow className="border-border/40 hover:bg-transparent">
                        <TableHead className="py-4 text-muted-foreground">Date</TableHead>
                        <TableHead className="text-center py-4 text-muted-foreground">Check-in</TableHead>
                        <TableHead className="text-center py-4 text-muted-foreground">Report</TableHead>
                        <TableHead className="text-center py-4 text-muted-foreground">Meeting</TableHead>
                        <TableHead className="text-center py-4 text-muted-foreground">Hours Bonus</TableHead>
                        <TableHead className="text-center py-4 text-muted-foreground">Streak Bonus</TableHead>
                        <TableHead className="text-center py-4 text-muted-foreground">Badge</TableHead>
                        <TableHead className="text-center py-4 text-muted-foreground">Daily Total</TableHead>
                        <TableHead className="text-center py-4 text-muted-foreground">Cumulative</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {xpHistory.map((day, index) => (
                        <TableRow
                          key={day._id || index}
                          className="border-border/40 hover:bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 transition-colors"
                        >
                          <TableCell className="font-medium py-4 text-foreground/90">
                            {new Date(day.date).toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric'
                            })}
                          </TableCell>
                          <TableCell className="text-center py-4 text-foreground/80">
                            {day.checkinXP || 0}
                          </TableCell>
                          <TableCell className="text-center py-4 text-foreground/80">
                            {day.reportXP || 0}
                          </TableCell>
                          <TableCell className="text-center py-4 text-foreground/80">
                            {day.meetingXP || 0}
                          </TableCell>
                          <TableCell className="text-center py-4 text-foreground/80">
                            {day.hoursBonus || 0}
                          </TableCell>
                          <TableCell className="text-center py-4 text-foreground/80">
                            {day.streakBonus || 0}
                          </TableCell>
                          <TableCell className="text-center py-4 text-foreground/80">
                            {day.badgeXP || 0}
                          </TableCell>
                          <TableCell className="text-center font-bold py-4 text-foreground/90">
                            {day.totalDailyXP || 0}
                          </TableCell>
                          <TableCell className="text-center font-bold py-4 text-foreground/90">
                            {day.cumulativeXP?.toLocaleString() || 0}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
