import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import AdminLayout from "../../../components/admin/AdminLayout";
import { useAuthStore } from "../../../store/authStore";
import { useAdminTaskStore } from "../../../store/adminTaskStore";
import { getUserRole } from "../../../utils/rolePermissions";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../../components/ui/table";
import { Badge } from "../../../components/ui/badge";
import { HiClipboardList, HiArchive, HiEye, HiTrash } from "react-icons/hi";

export default function ArchivedTasks() {
  const navigate = useNavigate();
  const { user, isInitializing, accessToken } = useAuthStore();
  const {
    tasks,
    loading,
    fetchAllTasks,
    pagination,
    setPage,
    setFilters,
    deleteTask,
  } = useAdminTaskStore();
  const [selectedProject, setSelectedProject] = useState("all");

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;
    const userRole = getUserRole(user);
    if (!userRole) {
      navigate("/talent/dashboard");
    }
  }, [user, isInitializing, navigate]);

  // Reset filters on mount
  useEffect(() => {
    setFilters({
      status: null,
      priority: null,
      complexity: null,
      sortBy: "createdAt",
      sortOrder: "desc",
    });
  }, []);

  // Fetch tasks
  useEffect(() => {
    const userRole = getUserRole(user);
    if (userRole && accessToken) {
      setFilters({ isArchived: true });
      fetchAllTasks({
        isArchived: true,
        project: selectedProject === "all" ? null : selectedProject,
      });
    }
  }, [user, accessToken, selectedProject]);

  const displayedTasks = tasks || [];

  return (
    <AdminLayout>
      <div className="flex-1 p-6">
        {/* Header with Horizontal Tabs */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Task Management
              </h1>
              <p className="text-muted-foreground mt-1">View archived tasks</p>
            </div>
            <div className="flex items-center gap-3">
              <select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="px-4 py-2 rounded-lg border border-border bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="all">All Projects</option>
                <option value="Talent Portal">Talent Portal</option>
                <option value="ModelSuite">ModelSuite</option>
              </select>
            </div>
          </div>

          {/* Horizontal Navigation Tabs */}
          <div className="flex gap-2 border-b border-border mt-4">
            <button
              onClick={() => navigate("/admin/tasks")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Dashboard
            </button>
            <button
              onClick={() => navigate("/admin/tasks/all")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Active Tasks
            </button>
            <button
              onClick={() => navigate("/admin/tasks/completed")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Completed
            </button>
            <button
              onClick={() => navigate("/admin/tasks/archived")}
              className="px-4 py-3 border-b-2 border-primary text-primary font-semibold transition-colors"
            >
              Archived
            </button>
            <button
              onClick={() => navigate("/admin/tasks/requests")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Requests
            </button>
            <button
              onClick={() => navigate("/admin/tasks/create")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Create Task
            </button>
          </div>
        </div>

        {/* Archived Tasks Table */}
        <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-slate-500/20 rounded-lg">
                  <HiArchive className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                </div>
                <div>
                  <CardTitle className="text-lg">Archived Tasks</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    {displayedTasks.length} tasks archived
                  </p>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-rose-500"></div>
              </div>
            ) : displayedTasks.length === 0 ? (
              <div className="text-center py-12">
                <HiClipboardList className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                <p className="text-muted-foreground">No archived tasks found</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-border/40">
                      <TableHead className="w-12">ID</TableHead>
                      <TableHead className="min-w-[300px]">Title</TableHead>
                      <TableHead>Project</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Assigned To</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {displayedTasks.map((task) => (
                      <TableRow
                        key={task._id}
                        className="border-border/40 hover:bg-accent/50 cursor-pointer"
                        onClick={() => navigate(`/admin/tasks/${task._id}`)}
                      >
                        <TableCell className="py-2">
                          <span className="text-xs font-mono text-muted-foreground">
                            #{task.taskNumber}
                          </span>
                        </TableCell>
                        <TableCell className="py-2">
                          <div className="flex flex-col gap-1">
                            <span className="font-medium text-sm">
                              {task.title}
                            </span>
                            {task.description && (
                              <span className="text-xs text-muted-foreground line-clamp-1">
                                {task.description}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="py-2">
                          <Badge
                            variant={
                              task.category === "Talent Portal"
                                ? "default"
                                : "secondary"
                            }
                            className="text-xs"
                          >
                            {task.category || "N/A"}
                          </Badge>
                        </TableCell>
                        <TableCell className="py-2">
                          <Badge
                            variant="outline"
                            className={`text-xs capitalize ${
                              task.status === "completed"
                                ? "border-green-500/50 text-green-600"
                                : task.status === "in-progress"
                                ? "border-blue-500/50 text-blue-600"
                                : "border-slate-500/50 text-slate-600"
                            }`}
                          >
                            {task.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="py-2">
                          {task.assignedTo ? (
                            <div className="flex items-center gap-2">
                              <div className="w-5 h-5 rounded-full bg-blue-500/20 flex items-center justify-center text-xs font-semibold text-blue-400">
                                {task.assignedTo.name?.charAt(0).toUpperCase()}
                              </div>
                              <span className="text-xs">
                                {task.assignedTo.name}
                              </span>
                            </div>
                          ) : (
                            <span className="text-xs text-muted-foreground">
                              Unassigned
                            </span>
                          )}
                        </TableCell>
                        <TableCell className="py-2 text-right">
                          <div className="flex items-center justify-end gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                navigate(`/admin/tasks/${task._id}`);
                              }}
                              className="p-2 hover:bg-accent rounded-lg transition-colors"
                              title="View Details"
                            >
                              <HiEye className="w-4 h-4 text-muted-foreground hover:text-foreground" />
                            </button>
                            <button
                              onClick={async (e) => {
                                e.stopPropagation();
                                if (
                                  window.confirm(
                                    `Are you sure you want to permanently delete "${task.title}"? This action cannot be undone.`
                                  )
                                ) {
                                  try {
                                    await deleteTask(task._id, true);
                                    // Refresh the list
                                    fetchAllTasks({
                                      isArchived: true,
                                      project:
                                        selectedProject === "all"
                                          ? null
                                          : selectedProject,
                                    });
                                  } catch (error) {
                                    console.error("Delete error:", error);
                                  }
                                }
                              }}
                              className="p-2 hover:bg-red-500/10 rounded-lg transition-colors"
                              title="Permanently Delete"
                            >
                              <HiTrash className="w-4 h-4 text-red-500 hover:text-red-600" />
                            </button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {pagination && pagination.totalPages > 1 && (
                  <div className="flex items-center justify-between mt-4 pt-4 border-t border-border/40">
                    <p className="text-sm text-muted-foreground">
                      Showing{" "}
                      {(pagination.currentPage - 1) * pagination.limit + 1} to{" "}
                      {Math.min(
                        pagination.currentPage * pagination.limit,
                        pagination.totalTasks
                      )}{" "}
                      of {pagination.totalTasks} tasks
                    </p>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setPage(pagination.currentPage - 1)}
                        disabled={pagination.currentPage === 1}
                        className="px-4 py-2 rounded-lg border border-border bg-card text-foreground hover:bg-accent transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>
                      <button
                        onClick={() => setPage(pagination.currentPage + 1)}
                        disabled={
                          pagination.currentPage >= pagination.totalPages
                        }
                        className="px-4 py-2 rounded-lg border border-border bg-card text-foreground hover:bg-accent transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
