import { create } from "zustand";

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";

export const useJobStore = create((set, get) => ({
  // State
  jobs: [],
  selectedJob: null,
  jobStats: null,
  loading: false,
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalJobs: 0,
    limit: 20,
    hasNextPage: false,
    hasPrevPage: false,
  },
  filters: {
    status: "",
    search: "",
    sortBy: "createdAt",
    sortOrder: "desc",
    includeArchived: false,
  },

  // Set filters
  setFilters: (newFilters) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
    }));
  },

  // Fetch all jobs (Admin)
  fetchJobs: async (accessToken, page = 1) => {
    set({ loading: true, error: null });
    try {
      const { status, search, sortBy, sortOrder, includeArchived } =
        get().filters;
      const limit = get().pagination.limit;

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        sortOrder,
        includeArchived: includeArchived.toString(),
      });

      if (status) params.append("status", status);
      if (search) params.append("search", search);

      const response = await fetch(
        `${API_URL}/api/hiring/admin/jobs?${params}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch jobs");
      }

      const data = await response.json();

      // Filter jobs based on includeArchived flag
      let filteredJobs = data.jobs;
      if (includeArchived) {
        // Show ONLY archived jobs
        filteredJobs = data.jobs.filter((job) => job.isArchived === true);
      } else {
        // Show ONLY non-archived jobs (active jobs)
        filteredJobs = data.jobs.filter((job) => job.isArchived !== true);
      }

      set({
        jobs: filteredJobs,
        pagination: {
          ...data.pagination,
          totalJobs: filteredJobs.length,
        },
        loading: false,
      });
    } catch (error) {
      console.error("Fetch jobs error:", error);
      set({ error: error.message, loading: false });
    }
  },

  // Fetch job by ID (Admin)
  fetchJobById: async (accessToken, jobId) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(
        `${API_URL}/api/hiring/admin/jobs/${jobId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch job");
      }

      const data = await response.json();
      set({
        selectedJob: data.job,
        loading: false,
      });
      return data.job;
    } catch (error) {
      console.error("Fetch job error:", error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  // Fetch job by slug (Public)
  fetchJobBySlug: async (slug) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`${API_URL}/api/hiring/jobs/apply/${slug}`, {
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch job");
      }

      const data = await response.json();
      set({
        selectedJob: data.job,
        loading: false,
      });
      return data.job;
    } catch (error) {
      console.error("Fetch job by slug error:", error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  // Increment view count
  incrementViewCount: async (slug) => {
    try {
      await fetch(`${API_URL}/api/hiring/jobs/${slug}/view`, {
        method: "POST",
        credentials: "include",
      });
    } catch (error) {
      console.error("Increment view count error:", error);
    }
  },

  // Create job
  createJob: async (accessToken, jobData, isFormData = false) => {
    set({ loading: true, error: null });
    try {
      const headers = {
        Authorization: `Bearer ${accessToken}`,
      };

      // Don't set Content-Type for FormData - browser will set it with boundary
      if (!isFormData) {
        headers["Content-Type"] = "application/json";
      }

      const response = await fetch(`${API_URL}/api/hiring/admin/jobs`, {
        method: "POST",
        headers,
        credentials: "include",
        body: isFormData ? jobData : JSON.stringify(jobData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create job");
      }

      const data = await response.json();
      set({ loading: false });
      return data.job;
    } catch (error) {
      console.error("Create job error:", error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  // Update job
  updateJob: async (accessToken, jobId, jobData) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(
        `${API_URL}/api/hiring/admin/jobs/${jobId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
          body: JSON.stringify(jobData),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update job");
      }

      const data = await response.json();
      set({ loading: false });
      return data.job;
    } catch (error) {
      console.error("Update job error:", error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  // Publish job
  publishJob: async (accessToken, jobId) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(
        `${API_URL}/api/hiring/admin/jobs/${jobId}/publish`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to publish job");
      }

      const data = await response.json();
      set({ loading: false });

      // Update job in list if exists
      const jobs = get().jobs;
      const updatedJobs = jobs.map((job) =>
        job._id === jobId
          ? { ...job, status: "published", publishedAt: data.job.publishedAt }
          : job
      );
      set({ jobs: updatedJobs });

      return data.job;
    } catch (error) {
      console.error("Publish job error:", error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  // Close job
  closeJob: async (accessToken, jobId) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(
        `${API_URL}/api/hiring/admin/jobs/${jobId}/close`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to close job");
      }

      const data = await response.json();
      set({ loading: false });

      // Update job in list if exists
      const jobs = get().jobs;
      const updatedJobs = jobs.map((job) =>
        job._id === jobId
          ? { ...job, status: "closed", closedAt: data.job.closedAt }
          : job
      );
      set({ jobs: updatedJobs });

      return data.job;
    } catch (error) {
      console.error("Close job error:", error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  // Archive job
  archiveJob: async (accessToken, jobId) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(
        `${API_URL}/api/hiring/admin/jobs/${jobId}/archive`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to archive job");
      }

      const data = await response.json();
      set({ loading: false });

      // Remove job from list or mark as archived
      const jobs = get().jobs;
      const updatedJobs = jobs.filter((job) => job._id !== jobId);
      set({ jobs: updatedJobs });

      return data.job;
    } catch (error) {
      console.error("Archive job error:", error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  // Duplicate job
  duplicateJob: async (accessToken, jobId) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(
        `${API_URL}/api/hiring/admin/jobs/${jobId}/duplicate`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to duplicate job");
      }

      const data = await response.json();
      set({ loading: false });

      // Add duplicated job to list
      const jobs = get().jobs;
      set({ jobs: [data.job, ...jobs] });

      return data.job;
    } catch (error) {
      console.error("Duplicate job error:", error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  // Fetch job statistics
  fetchJobStats: async (accessToken) => {
    try {
      const response = await fetch(`${API_URL}/api/hiring/admin/jobs/stats`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch job stats");
      }

      const data = await response.json();
      set({ jobStats: data.stats });
      return data.stats;
    } catch (error) {
      console.error("Fetch job stats error:", error);
      throw error;
    }
  },

  // Fetch job by slug (Public - no auth)
  fetchJobBySlug: async (slug) => {
    set({ loading: true, error: null, selectedJob: null });
    try {
      const response = await fetch(`${API_URL}/api/hiring/jobs/apply/${slug}`);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch job");
      }

      const data = await response.json();
      set({ selectedJob: data.job, loading: false });
      return data.job;
    } catch (error) {
      console.error("Fetch job by slug error:", error);
      set({ loading: false, error: error.message, selectedJob: null });
      return null;
    }
  },

  // Clear selected job
  clearSelectedJob: () => set({ selectedJob: null }),

  // Clear error
  clearError: () => set({ error: null }),
}));
