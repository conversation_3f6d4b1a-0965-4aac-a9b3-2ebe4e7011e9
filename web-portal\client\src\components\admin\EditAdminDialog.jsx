import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, Shield, Users, UserCheck, Briefcase, Award, Mail, Calendar, Clock, Settings, BarChart3, FileText, Video, DollarSign, TrendingUp, Bell, MessageSquare, CheckCircle2, ChevronDown, ChevronRight } from 'lucide-react';
import { PERMISSION_MATRIX, COMPREHENSIVE_ROLES } from '../../utils/permissionMatrix';

export default function EditAdminDialog({ open, onOpenChange, admin, onSubmit, availableRoles }) {
  const [formData, setFormData] = useState({
    roleType: '',
    canAccessTalentPortal: false,
    customPermissions: [], // Array of permission strings like 'employee.talents.view'
    selectedModules: [] // For UI organization
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [permissionMode, setPermissionMode] = useState('role'); // 'role' or 'custom'
  const [expandedModules, setExpandedModules] = useState({}); // For collapsible modules

  // Initialize form data when admin changes
  useEffect(() => {
    if (admin && open) {
      console.log('🔧 EditAdminDialog - Initializing with:', { admin, availableRoles });
      setFormData({
        roleType: admin.roleType || '',
        canAccessTalentPortal: admin.canAccessTalentPortal || false,
        customPermissions: admin.customPermissions || [],
        selectedModules: []
      });
      setPermissionMode(admin.permissionMode || (admin.customPermissions?.length > 0 ? 'custom' : 'role'));
      setExpandedModules({ employee: true }); // Start with employee module expanded
      setErrors({});
    }
  }, [admin, open, availableRoles]);

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (permissionMode === 'role' && !formData.roleType) {
      newErrors.roleType = 'Role is required';
    }
    
    if (permissionMode === 'custom' && (!formData.customPermissions || formData.customPermissions.length === 0)) {
      newErrors.customPermissions = 'At least one feature permission is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm() || !admin) {
      return;
    }

    try {
      setIsSubmitting(true);
      
      // Prepare submission data based on permission mode
      const submissionData = {
        permissionMode,
        canAccessTalentPortal: formData.canAccessTalentPortal,
        ...(permissionMode === 'role' 
          ? { roleType: formData.roleType }
          : { customPermissions: formData.customPermissions }
        )
      };
      
      console.log('🚀 Submitting admin update:', submissionData);
      await onSubmit(admin._id, submissionData);
    } catch (error) {
      console.error('Error updating admin:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input changes
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user makes changes
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  // Get role icon
  const getRoleIcon = (roleType) => {
    const icons = {
      super_admin: Shield,
      full_admin: UserCheck,
      hiring_manager: Briefcase,
      employee_manager: Users,
      team_lead: Award
    };
    
    return icons[roleType] || Users;
  };

  // Get role color
  const getRoleColor = (roleType) => {
    const colors = {
      super_admin: 'text-red-600 bg-red-50 border-red-200',
      full_admin: 'text-purple-600 bg-purple-50 border-purple-200',
      hiring_manager: 'text-blue-600 bg-blue-50 border-blue-200',
      employee_manager: 'text-green-600 bg-green-50 border-green-200',
      team_lead: 'text-orange-600 bg-orange-50 border-orange-200'
    };
    
    return colors[roleType] || 'text-gray-600 bg-gray-50 border-gray-200';
  };

  // Check if changes were made
  const hasChanges = admin && (
    formData.roleType !== admin.roleType || 
    formData.canAccessTalentPortal !== admin.canAccessTalentPortal ||
    JSON.stringify(formData.customPermissions || []) !== JSON.stringify(admin.customPermissions || [])
  );

  // Debug logging
  if (admin && open) {
    console.log('🔧 EditAdminDialog Debug:', {
      adminId: admin._id,
      adminName: admin.name,
      currentRole: admin.roleType,
      availableRoles: Object.keys(availableRoles || {}),
      formData,
      hasChanges
    });
  }

  if (!admin) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="text-blue-600" size={20} />
            Edit Admin User
          </DialogTitle>
          <DialogDescription>
            Update role permissions and access settings for {admin.name}.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Current Admin Info */}
          <Card>
            <CardContent className="p-4 space-y-3">
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">{admin.name}</h4>
                  <p className="text-sm text-gray-500 flex items-center gap-1 mt-1">
                    <Mail size={12} />
                    {admin.email}
                  </p>
                </div>
                <div className="flex flex-col gap-1">
                  {admin.isActive ? (
                    <Badge className="bg-green-100 text-green-800 border-green-200 text-xs">Active</Badge>
                  ) : (
                    <Badge className="bg-gray-100 text-gray-800 border-gray-200 text-xs">Inactive</Badge>
                  )}
                  {admin.mustChangePassword && (
                    <Badge className="bg-orange-100 text-orange-800 border-orange-200 text-xs">
                      Must change password
                    </Badge>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-1">
                  <p className="text-gray-500 flex items-center gap-1">
                    <Calendar size={12} />
                    Created
                  </p>
                  <p className="font-medium">{new Date(admin.createdAt).toLocaleDateString()}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-gray-500 flex items-center gap-1">
                    <Clock size={12} />
                    Last Login
                  </p>
                  <p className="font-medium">{admin.lastLogin ? new Date(admin.lastLogin).toLocaleDateString() : 'Never'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Role Display */}
          <div className="space-y-2">
            <Label>Current Role</Label>
            <div className={`p-3 rounded-lg border ${getRoleColor(admin.roleType)}`}>
              <div className="flex items-center gap-2 mb-1">
                {React.createElement(getRoleIcon(admin.roleType), { size: 16 })}
                <span className="font-medium">
                  {admin.roleInfo?.name || admin.roleType || 'No Role Assigned'}
                </span>
              </div>
              <p className="text-sm opacity-80">
                {admin.roleInfo?.description || 'No description available'}
              </p>
              {admin.permissionMode === 'custom' && (
                <div className="mt-2 text-xs text-blue-600">
                  Custom Permissions: {admin.customPermissions?.length || 0} permissions
                </div>
              )}
            </div>
          </div>

          {/* Permission Mode Selection */}
          <div className="space-y-3 border-t pt-4">
            <Label>Permission Mode</Label>
            <div className="flex gap-2">
              <Button
                type="button"
                variant={permissionMode === 'role' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPermissionMode('role')}
                className="flex items-center gap-2"
              >
                <Award size={14} />
                Use Role Template
              </Button>
              <Button
                type="button"
                variant={permissionMode === 'custom' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPermissionMode('custom')}
                className="flex items-center gap-2"
              >
                <Settings size={14} />
                Custom Permissions
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              {permissionMode === 'role' 
                ? 'Use predefined role templates with standard permissions'
                : 'Customize specific feature access for this admin'
              }
            </p>
          </div>

          {/* Role Selection (only shown in role mode) */}
          {permissionMode === 'role' && (
            <div className="space-y-2">
              <Label>New Role *</Label>
              <Select 
                value={formData.roleType} 
                onValueChange={(value) => handleChange('roleType', value)}
              >
                <SelectTrigger className={errors.roleType ? 'border-red-300 focus:border-red-500' : ''}>
                  <SelectValue placeholder="Select new admin role" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(COMPREHENSIVE_ROLES).map(([key, role]) => {
                    const Icon = getRoleIcon(key);
                    const isCurrentRole = key === admin.roleType;
                    return (
                      <SelectItem key={key} value={key} className="p-3">
                        <div className="flex items-center gap-3">
                          <Icon size={16} className={getRoleColor(key).split(' ')[0]} />
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <p className="font-medium">{role.name}</p>
                              {isCurrentRole && (
                                <Badge className="text-xs bg-blue-100 text-blue-800 border-blue-200">Current</Badge>
                              )}
                            </div>
                            <p className="text-xs text-gray-500">{role.description}</p>
                            <p className="text-xs text-gray-400">{role.permissions.length} permissions</p>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              {errors.roleType && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle size={12} />
                  {errors.roleType}
                </p>
              )}
            </div>
          )}

          {/* Custom Permissions Selection - Fixed UI */}
          {permissionMode === 'custom' && (
            <div className="space-y-4">
              <div>
                <Label className="text-base font-semibold">Custom Permission Management</Label>
                <p className="text-sm text-gray-500 mt-1">
                  Select specific permissions by module. Click on actions to enable/disable them.
                </p>
                <div className="mt-2 p-2 bg-blue-50 rounded-lg border">
                  <div className="text-sm">
                    <strong>Selected:</strong> {formData.customPermissions?.length || 0} permissions
                  </div>
                </div>
              </div>
              
              <div className="border rounded-lg max-h-96 overflow-y-auto bg-white">
                {Object.entries(PERMISSION_MATRIX).map(([moduleKey, module]) => {
                  const isExpanded = expandedModules[moduleKey];
                  const modulePermissionCount = Object.values(module.features).reduce((count, feature) => 
                    count + Object.keys(feature.actions).length, 0
                  );
                  const selectedCount = (formData.customPermissions || []).filter(p => 
                    p.startsWith(`${moduleKey}.`)
                  ).length;
                  
                  return (
                    <div key={moduleKey} className="border-b last:border-b-0">
                      {/* Module Header */}
                      <div 
                        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => setExpandedModules(prev => ({
                          ...prev,
                          [moduleKey]: !prev[moduleKey]
                        }))}
                      >
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${
                            moduleKey === 'employee' ? 'bg-blue-100' :
                            moduleKey === 'hiring' ? 'bg-green-100' : 'bg-red-100'
                          }`}>
                            {moduleKey === 'employee' && <Users size={16} className="text-blue-600" />}
                            {moduleKey === 'hiring' && <Briefcase size={16} className="text-green-600" />}
                            {moduleKey === 'admin' && <Shield size={16} className="text-red-600" />}
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{module.name}</h4>
                            <p className="text-sm text-gray-500">
                              {selectedCount}/{modulePermissionCount} permissions
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={selectedCount > 0 ? 'default' : 'secondary'} className="text-xs">
                            {selectedCount > 0 ? `${selectedCount} Active` : 'None'}
                          </Badge>
                          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                        </div>
                      </div>

                      {/* Module Features */}
                      {isExpanded && (
                        <div className="px-4 pb-4 space-y-3 bg-gray-50">
                          {Object.entries(module.features).map(([featureKey, feature]) => {
                            const featurePermissions = Object.keys(feature.actions).map(action => 
                              `${moduleKey}.${featureKey}.${action}`
                            );
                            const selectedFeatureCount = featurePermissions.filter(p => 
                              (formData.customPermissions || []).includes(p)
                            ).length;
                            
                            return (
                              <div key={featureKey} className="bg-white rounded-lg border p-3">
                                <div className="flex items-center justify-between mb-3">
                                  <div>
                                    <h5 className="font-medium text-sm text-gray-900">{feature.name}</h5>
                                    <p className="text-xs text-gray-500 mt-1">{feature.description}</p>
                                  </div>
                                  <div className="flex gap-1">
                                    <Button
                                      type="button"
                                      size="sm"
                                      variant="outline"
                                      className="h-6 text-xs px-2"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        const currentPermissions = formData.customPermissions || [];
                                        const newPermissions = [...new Set([...currentPermissions, ...featurePermissions])];
                                        handleChange('customPermissions', newPermissions);
                                      }}
                                    >
                                      All
                                    </Button>
                                    <Button
                                      type="button"
                                      size="sm"
                                      variant="outline"
                                      className="h-6 text-xs px-2"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        const currentPermissions = formData.customPermissions || [];
                                        const newPermissions = currentPermissions.filter(p => 
                                          !featurePermissions.includes(p)
                                        );
                                        handleChange('customPermissions', newPermissions);
                                      }}
                                    >
                                      None
                                    </Button>
                                  </div>
                                </div>
                                
                                {/* Action Permissions */}
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-1.5">
                                  {Object.entries(feature.actions).map(([actionKey, actionDescription]) => {
                                    const permission = `${moduleKey}.${featureKey}.${actionKey}`;
                                    const isSelected = (formData.customPermissions || []).includes(permission);
                                    
                                    const getActionStyle = (action) => {
                                      if (isSelected) {
                                        const styles = {
                                          view: 'bg-green-500 text-white border-green-500',
                                          create: 'bg-blue-500 text-white border-blue-500', 
                                          edit: 'bg-yellow-500 text-white border-yellow-500',
                                          delete: 'bg-red-500 text-white border-red-500',
                                          approve: 'bg-purple-500 text-white border-purple-500',
                                          manage: 'bg-indigo-500 text-white border-indigo-500'
                                        };
                                        return styles[action] || 'bg-gray-500 text-white border-gray-500';
                                      } else {
                                        return 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50';
                                      }
                                    };
                                    
                                    return (
                                      <button
                                        key={actionKey}
                                        type="button"
                                        className={`p-1.5 rounded border text-xs font-medium transition-all cursor-pointer ${getActionStyle(actionKey)}`}
                                        onClick={() => {
                                          const currentPermissions = formData.customPermissions || [];
                                          const newPermissions = isSelected
                                            ? currentPermissions.filter(p => p !== permission)
                                            : [...currentPermissions, permission];
                                          handleChange('customPermissions', newPermissions);
                                        }}
                                        title={actionDescription}
                                      >
                                        <div className="flex items-center justify-between gap-1">
                                          <span className="capitalize truncate">{actionKey}</span>
                                          {isSelected && <CheckCircle2 size={10} />}
                                        </div>
                                      </button>
                                    );
                                  })}
                                </div>
                                
                                <div className="mt-2 text-xs text-gray-500">
                                  {selectedFeatureCount}/{featurePermissions.length} actions selected
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
              
              {errors.customPermissions && (
                <div className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle size={12} />
                  {errors.customPermissions}
                </div>
              )}
            </div>
          )}

          {/* Selected Role Info (if different from current) */}
          {formData.roleType && formData.roleType !== admin.roleType && COMPREHENSIVE_ROLES[formData.roleType] && (
            <div className={`p-3 rounded-lg border ${getRoleColor(formData.roleType)}`}>
              <div className="flex items-center gap-2 mb-2">
                {React.createElement(getRoleIcon(formData.roleType), { size: 16 })}
                <span className="font-medium">{COMPREHENSIVE_ROLES[formData.roleType].name}</span>
                <Badge className="text-xs bg-green-100 text-green-800 border-green-200">New Role</Badge>
              </div>
              <p className="text-sm opacity-80 mb-3">{COMPREHENSIVE_ROLES[formData.roleType].description}</p>
              <div className="space-y-2">
                <p className="text-xs font-medium opacity-70">Access Summary:</p>
                <div className="text-xs space-y-1">
                  <div>• <strong>Total Permissions:</strong> {COMPREHENSIVE_ROLES[formData.roleType].permissions.length}</div>
                  <div>• <strong>Employee Module:</strong> {COMPREHENSIVE_ROLES[formData.roleType].permissions.filter(p => p.startsWith('employee.')).length} permissions</div>
                  <div>• <strong>Hiring Module:</strong> {COMPREHENSIVE_ROLES[formData.roleType].permissions.filter(p => p.startsWith('hiring.')).length} permissions</div>
                  <div>• <strong>Admin Module:</strong> {COMPREHENSIVE_ROLES[formData.roleType].permissions.filter(p => p.startsWith('admin.')).length} permissions</div>
                </div>
              </div>
            </div>
          )}

          {/* Additional Options */}
          <div className="space-y-4 border-t pt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Talent Portal Access</Label>
                <p className="text-xs text-gray-500">
                  Allow this admin to also access the talent portal (dual access)
                </p>
              </div>
              <Switch
                checked={formData.canAccessTalentPortal}
                onCheckedChange={(checked) => handleChange('canAccessTalentPortal', checked)}
              />
            </div>
          </div>

          {/* Change Summary */}
          {hasChanges && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <AlertCircle className="text-blue-600 mt-0.5" size={16} />
                <div className="text-sm">
                  <p className="font-medium text-blue-800">Changes Summary</p>
                  <ul className="text-blue-700 mt-1 space-y-1">
                    {permissionMode === 'role' && formData.roleType !== admin.roleType && (
                      <li>• Role: {admin.roleInfo?.name} → {COMPREHENSIVE_ROLES[formData.roleType]?.name}</li>
                    )}
                    {permissionMode === 'custom' && JSON.stringify(formData.customPermissions || []) !== JSON.stringify(admin.customPermissions || []) && (
                      <li>• Custom Permissions: {formData.customPermissions?.length || 0} total permissions selected</li>
                    )}
                    {formData.canAccessTalentPortal !== admin.canAccessTalentPortal && (
                      <li>• Talent Portal Access: {admin.canAccessTalentPortal ? 'Enabled' : 'Disabled'} → {formData.canAccessTalentPortal ? 'Enabled' : 'Disabled'}</li>
                    )}
                    <li>• Permission Mode: {permissionMode === 'role' ? 'Using role template' : 'Custom permissions'}</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Warning for permission changes */}
          {(permissionMode === 'role' && formData.roleType !== admin.roleType) || 
           (permissionMode === 'custom' && JSON.stringify(formData.customPermissions || []) !== JSON.stringify(admin.customPermissions || [])) && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <AlertCircle className="text-amber-600 mt-0.5" size={16} />
                <div className="text-sm">
                  <p className="font-medium text-amber-800">Permission Change Warning</p>
                  <p className="text-amber-700 mt-1">
                    Changing permissions will immediately update their access rights. 
                    They may lose access to certain features and will need to refresh their session.
                  </p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting || !hasChanges}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Updating...
                </div>
              ) : (
                'Update Admin'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}