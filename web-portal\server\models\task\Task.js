import mongoose from "mongoose";

const RevisionIssueSchema = new mongoose.Schema({
  description: {
    type: String,
    required: true,
    trim: true,
  },
  priority: {
    type: String,
    enum: ["low", "medium", "high", "urgent"],
    default: "medium",
  },
  screenshot: {
    url: String,
    publicId: String,
    filename: String,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Schema for individual checklist item revision history
const ChecklistRevisionEntrySchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ["talent_submission", "admin_revision"],
    required: true,
  },
  comment: {
    type: String,
    trim: true,
    required: true,
  },
  images: [
    {
      url: String,
      publicId: String,
      filename: String,
    },
  ],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: "checklistItems.revisionHistory.createdByModel",
  },
  createdByModel: {
    type: String,
    enum: ["Admin", "Talent"],
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

const SubmissionHistorySchema = new mongoose.Schema({
  attemptNumber: {
    type: Number,
    required: true,
  },
  prLink: {
    type: String,
    trim: true,
  },
  screenshots: [
    {
      url: String,
      publicId: String,
      filename: String,
      uploadedAt: {
        type: Date,
        default: Date.now,
      },
      // Annotated version (marked up by admin)
      annotatedUrl: String,
      annotatedPublicId: String,
      annotatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Admin",
      },
      annotatedAt: Date,
    },
  ],
  submittedAt: {
    type: Date,
    default: Date.now,
  },
  status: {
    type: String,
    enum: ["pending-review", "approved", "rejected"],
    required: true,
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Admin",
  },
  reviewedAt: Date,
  feedback: String,
  issues: [RevisionIssueSchema],
});

const SectionSchema = new mongoose.Schema({
  number: {
    type: Number,
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  status: {
    type: String,
    enum: [
      "locked",
      "unlocked",
      "pending-review",
      "completed",
      "needs-revision",
    ],
    default: "locked",
  },
  prLink: {
    type: String,
    trim: true,
    validate: {
      validator: function (v) {
        if (!v) return true; // Optional field
        // Basic URL validation
        return /^https?:\/\/.+/.test(v);
      },
      message: "PR link must be a valid URL",
    },
  },
  screenshots: [
    {
      url: String,
      publicId: String,
      filename: String,
      uploadedAt: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  submittedAt: Date,
  completedAt: Date,
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Admin",
  },
  approvedAt: Date,
  feedback: String, // Overall admin feedback for current revision
  revisionNote: String,
  issues: [RevisionIssueSchema], // Current revision issues
  submissionHistory: [SubmissionHistorySchema], // Track all submission attempts
  currentAttempt: {
    type: Number,
    default: 1,
  },
});

const ChecklistItemSchema = new mongoose.Schema({
  number: {
    type: Number,
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
    default: "",
  },
  completed: {
    type: Boolean,
    default: false,
  },
  // Status for individual checklist item
  status: {
    type: String,
    enum: ["pending", "submitted", "approved", "needs-revision"],
    default: "pending",
  },
  // Reference images attached by admin (bug screenshots, design mockups, etc.)
  referenceImages: [
    {
      url: String,
      publicId: String,
      filename: String,
      uploadedAt: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  // Screenshots uploaded by talent when completing the item
  screenshots: [
    {
      url: String,
      publicId: String,
      uploadedAt: {
        type: Date,
        default: Date.now,
      },
      // Annotated version (marked up by admin)
      annotatedUrl: String,
      annotatedPublicId: String,
      annotatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Admin",
      },
      annotatedAt: Date,
    },
  ],
  note: String,
  completedAt: Date,
  // PR/test link for this checklist item
  prLink: {
    type: String,
    trim: true,
  },
  // Note provided when completing the item
  completionNote: {
    type: String,
    trim: true,
  },
  // Revision fields (similar to sections)
  needsRevision: {
    type: Boolean,
    default: false,
  },
  feedback: {
    type: String,
    trim: true,
  },
  revisionNote: {
    type: String,
    trim: true,
  },
  issues: [RevisionIssueSchema], // Current revision issues
  // Revision history for this checklist item
  revisionHistory: [ChecklistRevisionEntrySchema],
});

const CommentSchema = new mongoose.Schema({
  author: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: "comments.authorModel",
    required: true,
  },
  authorModel: {
    type: String,
    enum: ["Talent", "Admin"],
    required: true,
  },
  message: {
    type: String,
    required: true,
    trim: true,
    maxlength: [1000, "Comment cannot exceed 1000 characters"],
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

const TaskSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Task title is required"],
      trim: true,
      maxlength: [200, "Title cannot exceed 200 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [2000, "Description cannot exceed 2000 characters"],
    },
    category: {
      type: String,
      enum: {
        values: [
          "Currently Working on",
          "Finished",
          "Future",
          "Merged",
          "Needs Fixing",
          "Open",
          "Redo",
        ],
        message: "Invalid category",
      },
      required: [true, "Category is required"],
      index: true,
    },
    project: {
      type: String,
      enum: {
        values: ["Talent Portal", "ModelSuite"],
        message: "Invalid project",
      },
      required: [true, "Project is required"],
      index: true,
    },
    taskType: {
      type: String,
      enum: {
        values: ["section-based", "checklist-based"],
        message:
          "Task type must be either 'section-based' or 'checklist-based'",
      },
      required: [true, "Task type is required"],
    },
    hasSections: {
      type: Boolean,
      default: false,
    },
    sections: [SectionSchema],
    hasChecklist: {
      type: Boolean,
      default: false,
    },
    checklist: [ChecklistItemSchema],
    status: {
      type: String,
      enum: {
        values: [
          "stored",
          "available",
          "pending-approval",
          "in-progress",
          "pending-review",
          "needs-revision",
          "completed",
          "cancelled",
        ],
        message: "Invalid status value",
      },
      default: "available",
      index: true,
    },
    priority: {
      type: String,
      enum: {
        values: ["low", "medium", "high", "urgent"],
        message: "Invalid priority value",
      },
      default: "medium",
      index: true,
    },
    tags: {
      type: [String],
      default: [],
      validate: {
        validator: function (v) {
          return v.every((tag) => tag.trim().length > 0 && tag.length <= 30);
        },
        message: "Each tag must be 1-30 characters",
      },
      index: true,
    },
    complexity: {
      type: String,
      enum: {
        values: ["small", "medium", "large"],
        message: "Invalid complexity value",
      },
      default: "medium",
    },
    xpValue: {
      type: Number,
      default: function () {
        // Auto-calculate XP based on complexity
        const xpMap = { small: 5, medium: 10, large: 20 };
        return xpMap[this.complexity] || 10;
      },
    },
    dueDate: {
      type: Date,
      index: true,
    },
    isOverdue: {
      type: Boolean,
      default: false,
    },
    isUrgentToday: {
      type: Boolean,
      default: false,
      index: true,
    },
    imageUrl: {
      type: String, // Cloudinary URL for main task screenshot
      trim: true,
    },
    imagePublicId: {
      type: String, // Cloudinary public ID for deletion
      trim: true,
    },
    // Attachments (PDFs and images)
    attachments: [
      {
        url: {
          type: String,
          required: true,
        },
        publicId: {
          type: String,
          required: true,
        },
        filename: {
          type: String,
          required: true,
        },
        fileType: {
          type: String,
          enum: ["pdf", "image"],
          required: true,
        },
        mimeType: String,
        fileSize: Number, // in bytes
        uploadedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    // Assignment tracking
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Talent",
      index: true,
    },
    requestedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Talent",
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Admin",
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Admin",
      required: [true, "Creator is required"],
    },
    // Timestamps for lifecycle
    startedAt: Date,
    completedAt: Date,
    // Admin-only fields
    adminNotes: {
      type: String,
      trim: true,
      maxlength: [1000, "Admin notes cannot exceed 1000 characters"],
    },
    // Archiving
    isArchived: {
      type: Boolean,
      default: false,
      index: true,
    },
    // Comments for talent-admin communication
    comments: [CommentSchema],
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for performance
TaskSchema.index({ category: 1, status: 1 }); // Project filtering
TaskSchema.index({ status: 1, dueDate: 1 }); // Deadline queries
TaskSchema.index({ status: 1, createdAt: -1 }); // Sorting
TaskSchema.index({ assignedTo: 1, status: 1 }); // User tasks

// Virtual: Days since task was created
TaskSchema.virtual("daysOpen").get(function () {
  if (!this.createdAt) return 0;
  const now = new Date();
  const diff = now - this.createdAt;
  return Math.floor(diff / (1000 * 60 * 60 * 24));
});

// Virtual: Days since task was started (in-progress)
TaskSchema.virtual("daysInProgress").get(function () {
  if (!this.startedAt) return 0;
  const now = new Date();
  const diff = now - this.startedAt;
  return Math.floor(diff / (1000 * 60 * 60 * 24));
});

// Virtual: Days until due date
TaskSchema.virtual("daysUntilDue").get(function () {
  if (!this.dueDate) return null;
  const now = new Date();
  const diff = this.dueDate - now;
  return Math.ceil(diff / (1000 * 60 * 60 * 24));
});

// Virtual: Deadline alert level
TaskSchema.virtual("deadlineAlert").get(function () {
  const days = this.daysUntilDue;
  if (days === null) return "none";
  if (days < 0) return "overdue";
  if (days <= 1) return "urgent";
  if (days <= 3) return "warning";
  if (days <= 7) return "normal";
  return "none";
});

// Virtual: Section progress percentage
TaskSchema.virtual("sectionProgress").get(function () {
  if (!this.hasSections || this.sections.length === 0) return 0;
  const completed = this.sections.filter(
    (s) => s.status === "completed"
  ).length;
  return Math.round((completed / this.sections.length) * 100);
});

// Virtual: Checklist progress percentage
TaskSchema.virtual("checklistProgress").get(function () {
  if (!this.hasChecklist || this.checklist.length === 0) return 0;
  const completed = this.checklist.filter((item) => item.completed).length;
  return Math.round((completed / this.checklist.length) * 100);
});

// Virtual: Days until due
TaskSchema.virtual("daysUntilDue").get(function () {
  if (!this.dueDate) return null;
  const now = new Date();
  const due = new Date(this.dueDate);
  const diffTime = due - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual: Deadline alert level
TaskSchema.virtual("deadlineAlert").get(function () {
  if (
    !this.dueDate ||
    this.status === "completed" ||
    this.status === "cancelled"
  ) {
    return "none";
  }

  const daysUntil = this.daysUntilDue;

  if (daysUntil < 0) return "overdue";
  if (daysUntil === 0) return "today";
  if (daysUntil <= 7) return "week";
  return "none";
});

// Pre-save hook: Update isOverdue flag
TaskSchema.pre("save", function (next) {
  if (this.dueDate) {
    this.isOverdue =
      this.dueDate < new Date() &&
      this.status !== "completed" &&
      this.status !== "cancelled";
  }
  next();
});

// Pre-save hook: Validate task type consistency
TaskSchema.pre("save", function (next) {
  if (this.taskType === "section-based" && !this.hasSections) {
    return next(new Error("Section-based tasks must have hasSections=true"));
  }
  if (this.taskType === "checklist-based" && !this.hasChecklist) {
    return next(new Error("Checklist-based tasks must have hasChecklist=true"));
  }
  next();
});

// Pre-save hook: Section unlocking logic
TaskSchema.pre("save", function (next) {
  if (this.hasSections && this.sections.length > 0) {
    // First section always unlocked when task is in-progress
    if (this.status === "in-progress" && this.sections[0].status === "locked") {
      this.sections[0].status = "available";
    }

    // Unlock next section when previous is completed
    for (let i = 0; i < this.sections.length - 1; i++) {
      if (this.sections[i].status === "completed") {
        // If next section is still locked, unlock it
        if (this.sections[i + 1].status === "locked") {
          this.sections[i + 1].status = "available";
        }
      }
    }
  }
  next();
});

export default mongoose.model("Task", TaskSchema);
