import Task from "../../models/task/Task.js";
import TaskRequest from "../../models/task/TaskRequest.js";
import Talent from "../../models/Talent.js";
import mongoose from "mongoose";
import { createNotification } from "../../utils/notificationService.js";
import cloudinary from "../../config/cloudinary.js";

// ===========================
// HELPER: Check Full Access
// ===========================
// Users with hasFullAccess flag can see BOTH projects
const hasFullAccess = (user) => {
  return (
    user.userType === "admin" ||
    user.hasFullAccess === true ||
    (user.roles &&
      (user.roles.includes("Admin") || user.roles.includes("Super-Admin")))
  );
};

// ===========================
// @desc    Get available tasks (Inbox)
// @route   GET /api/tasks/available
// @access  Private (Talent + Admin)
// ===========================
export const getAvailableTasks = async (req, res) => {
  try {
    const { category, priority, complexity, sortBy = "createdAt" } = req.query;

    console.log("🔍 GET /api/tasks/available - User details:", {
      userId: req.user.userId,
      email: req.user.email,
      userType: req.user.userType,
      assignedProject: req.user.assignedProject,
      hasFullAccess: hasFullAccess(req.user),
    });

    let filter = {
      status: "available",
      isArchived: false,
    };

    // Project filtering logic
    if (!hasFullAccess(req.user)) {
      // Regular talents: only see their assigned project
      filter.category = req.user.assignedProject;
      console.log(
        "👤 Regular talent - filtering by project:",
        req.user.assignedProject
      );
    } else if (category && category !== "all") {
      // Admin/Full access: can filter by specific project
      filter.category = category;
      console.log("👑 Full access user - filtering by category:", category);
    } else {
      console.log("👑 Full access user - showing all projects");
    }

    // Additional filters
    if (priority && priority !== "all") filter.priority = priority;
    if (complexity && complexity !== "all") filter.complexity = complexity;

    console.log("🔍 Final filter:", filter);

    const tasks = await Task.find(filter)
      .populate("createdBy", "name email")
      .sort({ isUrgentToday: -1, [sortBy]: -1 })
      .lean();

    console.log(`✅ Found ${tasks.length} available tasks`);

    res.json({
      success: true,
      count: tasks.length,
      tasks,
    });
  } catch (error) {
    console.error("❌ Get available tasks error:", error);
    res.status(500).json({ error: "Failed to fetch available tasks" });
  }
};

// ===========================
// @desc    Get tasks assigned to user (My Queue)
// @route   GET /api/tasks/my-queue
// @access  Private (Talent)
// ===========================
export const getMyQueueTasks = async (req, res) => {
  try {
    const tasks = await Task.find({
      assignedTo: req.user.userId,
      status: { $in: ["in-progress", "needs-revision"] },
      isArchived: false,
    })
      .populate("createdBy", "name email")
      .populate("approvedBy", "name email")
      .sort({ isUrgentToday: -1, createdAt: -1 })
      .lean();

    res.json({
      success: true,
      count: tasks.length,
      tasks,
    });
  } catch (error) {
    console.error("Get my queue error:", error);
    res.status(500).json({ error: "Failed to fetch my queue tasks" });
  }
};

// ===========================
// @desc    Get completed tasks (Archive)
// @route   GET /api/tasks/archive
// @access  Private
// ===========================
export const getArchivedTasks = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    let filter = { status: "completed", isArchived: false };

    // Regular talents: only see their own completed tasks
    if (!hasFullAccess(req.user)) {
      filter.assignedTo = req.user.userId;
    }

    const tasks = await Task.find(filter)
      .populate("assignedTo", "name email")
      .populate("createdBy", "name email")
      .sort({ completedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    const total = await Task.countDocuments(filter);

    res.json({
      success: true,
      count: tasks.length,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit),
      tasks,
    });
  } catch (error) {
    console.error("Get archive error:", error);
    res.status(500).json({ error: "Failed to fetch archived tasks" });
  }
};

// ===========================
// @desc    Request to work on a task
// @route   POST /api/tasks/:id/request
// @access  Private (Talent)
// ===========================
export const requestTask = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate task exists
    const task = await Task.findById(id);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Check task is available
    if (task.status !== "available") {
      return res.status(400).json({
        error: `Task is not available (current status: ${task.status})`,
      });
    }

    // Check project match (unless user has full access)
    if (
      !hasFullAccess(req.user) &&
      task.category !== req.user.assignedProject
    ) {
      return res.status(403).json({
        error: `You can only request tasks from ${req.user.assignedProject} project`,
      });
    }

    // Check if user already has a pending request for this task
    const existingRequest = await TaskRequest.findOne({
      taskId: id,
      requestedBy: req.user.userId,
      status: "pending",
    });

    if (existingRequest) {
      return res.status(400).json({
        error: "You already have a pending request for this task",
      });
    }

    // Verify talent exists
    const Talent = mongoose.model("Talent");
    const talent = await Talent.findById(req.user.userId);
    if (!talent) {
      return res.status(404).json({
        error: "Talent profile not found. Please re-login and try again.",
      });
    }

    // Create request
    const taskRequest = await TaskRequest.create({
      taskId: id,
      requestedBy: req.user.userId,
      status: "pending",
    });

    // Update task status
    task.status = "pending-approval";
    task.requestedBy = req.user.userId;
    await task.save();

    // Populate request for response
    await taskRequest.populate("requestedBy", "name email talentId");

    // Send notification to all admins
    try {
      const Admin = mongoose.model("Admin");
      const admins = await Admin.find({}).select("_id");

      // Send notification to each admin
      for (const admin of admins) {
        await createNotification(
          {
            recipient: admin._id,
            type: "admin_message",
            title: "New Task Request",
            message: `${
              req.user.name || req.user.email
            } requested to work on "${task.title}"`,
            actionUrl: `/admin/tasks/pending-requests`,
            metadata: { taskId: task._id, requestId: taskRequest._id },
          },
          req.app.get("io")
        );
      }
    } catch (notifError) {
      console.error("Failed to send admin notifications:", notifError);
      // Don't fail the request if notification fails
    }

    res.status(201).json({
      success: true,
      message: "Task request submitted successfully",
      request: taskRequest,
    });
  } catch (error) {
    console.error("Request task error:", error);
    res.status(500).json({ error: "Failed to submit task request" });
  }
};

// ===========================
// @desc    Approve task request
// @route   POST /api/tasks/:id/approve-request
// @access  Private (Admin)
// ===========================
export const approveTaskRequest = async (req, res) => {
  try {
    const { id: taskId } = req.params;

    // Find pending request
    const taskRequest = await TaskRequest.findOne({
      taskId,
      status: "pending",
    }).populate("requestedBy", "name email");

    if (!taskRequest) {
      return res
        .status(404)
        .json({ error: "No pending request found for this task" });
    }

    // Update task
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    task.status = "in-progress";
    task.assignedTo = taskRequest.requestedBy._id;
    task.approvedBy = req.user.userId;
    task.startedAt = new Date();

    // Unlock first section if section-based
    if (task.hasSections && task.sections.length > 0) {
      task.sections[0].status = "unlocked";
    }

    await task.save();

    // Update request
    taskRequest.status = "approved";
    taskRequest.approvedBy = req.user.userId;
    taskRequest.approvedAt = new Date();
    await taskRequest.save();

    // Send notification to talent
    try {
      await createNotification(
        {
          recipient: taskRequest.requestedBy._id,
          type: "task_assigned",
          title: "Task Request Approved",
          message: `Your request for "${task.title}" has been approved. You can start working on it now!`,
          actionUrl: `/tasks/my-queue`,
          metadata: { taskId: task._id },
        },
        req.app.get("io")
      );
    } catch (notifError) {
      console.error("Failed to send approval notification:", notifError);
      // Don't fail the approval if notification fails
    }

    res.json({
      success: true,
      message: "Task request approved successfully",
      task,
    });
  } catch (error) {
    console.error("Approve task error:", error);
    res.status(500).json({ error: "Failed to approve task request" });
  }
};

// ===========================
// @desc    Deny task request
// @route   POST /api/tasks/:id/deny-request
// @access  Private (Admin)
// ===========================
export const denyTaskRequest = async (req, res) => {
  try {
    const { id: taskId } = req.params;
    const { denialReason } = req.body;

    // Find pending request
    const taskRequest = await TaskRequest.findOne({
      taskId,
      status: "pending",
    }).populate("requestedBy", "name email");

    if (!taskRequest) {
      return res
        .status(404)
        .json({ error: "No pending request found for this task" });
    }

    // Update task back to available
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    task.status = "available";
    task.requestedBy = null;
    await task.save();

    // Update request
    taskRequest.status = "denied";
    taskRequest.deniedBy = req.user.userId;
    taskRequest.deniedAt = new Date();
    taskRequest.denialReason = denialReason || "Request denied by admin";
    await taskRequest.save();

    // Send notification to talent
    try {
      await createNotification(
        {
          recipient: taskRequest.requestedBy._id,
          type: "admin_message",
          title: "Task Request Denied",
          message: `Your request for "${task.title}" was denied. ${
            denialReason || ""
          }`,
          actionUrl: `/tasks/inbox`,
          metadata: { taskId: task._id },
        },
        req.app.get("io")
      );
    } catch (notifError) {
      console.error("⚠️ Failed to send denial notification:", notifError);
    }

    res.json({
      success: true,
      message: "Task request denied",
      task,
    });
  } catch (error) {
    console.error("Deny task error:", error);
    res.status(500).json({ error: "Failed to deny task request" });
  }
};

// ===========================
// @desc    Create new task (Admin only)
// @route   POST /api/tasks
// @access  Private (Admin)
// ===========================
export const createTask = async (req, res) => {
  try {
    console.log("📝 createTask received status:", req.body.status);
    console.log("📝 createTask received assignedTo:", req.body.assignedTo);

    const taskData = {
      ...req.body,
      createdBy: req.user.userId,
      // Respect status from request body (for stored tasks), default to 'available'
      status: req.body.status || "available",
    };

    console.log("📝 Initial taskData.status:", taskData.status);

    // Parse JSON strings from FormData
    if (typeof taskData.sections === "string") {
      taskData.sections = JSON.parse(taskData.sections);
    }
    if (typeof taskData.checklist === "string") {
      taskData.checklist = JSON.parse(taskData.checklist);
    }
    if (typeof taskData.tags === "string") {
      taskData.tags = JSON.parse(taskData.tags);
    }
    if (typeof taskData.hasSections === "string") {
      taskData.hasSections = taskData.hasSections === "true";
    }
    if (typeof taskData.hasChecklist === "string") {
      taskData.hasChecklist = taskData.hasChecklist === "true";
    }

    // Handle file uploads to Cloudinary
    if (req.files && req.files.length > 0) {
      console.log(`📎 Processing ${req.files.length} file uploads...`);
      const attachments = [];
      const checklistImagesMap = {}; // Map of checklist item index to images

      for (const file of req.files) {
        try {
          // Check if this is a checklist image (fieldname like "checklistImages_0")
          const checklistMatch = file.fieldname.match(
            /^checklistImages_(\d+)$/
          );

          if (checklistMatch) {
            // This is a checklist reference image
            const itemIndex = parseInt(checklistMatch[1]);
            const folder = `tasks/checklist-references`;

            const result = await new Promise((resolve, reject) => {
              const uploadOptions = {
                folder: folder,
                resource_type: "image",
                allowed_formats: ["jpg", "jpeg", "png", "gif", "webp"],
              };

              const uploadStream = cloudinary.uploader.upload_stream(
                uploadOptions,
                (error, result) => {
                  if (error) reject(error);
                  else resolve(result);
                }
              );

              uploadStream.end(file.buffer);
            });

            if (!checklistImagesMap[itemIndex]) {
              checklistImagesMap[itemIndex] = [];
            }

            checklistImagesMap[itemIndex].push({
              url: result.secure_url,
              publicId: result.public_id,
              filename: file.originalname,
            });
          } else if (file.fieldname === "attachments") {
            // Regular task attachment
            const fileType =
              file.mimetype === "application/pdf" ? "pdf" : "image";
            const folder = `tasks/attachments/${fileType}s`;

            const result = await new Promise((resolve, reject) => {
              const uploadOptions = {
                folder: folder,
                resource_type: fileType === "pdf" ? "raw" : "image",
                allowed_formats:
                  fileType === "pdf"
                    ? ["pdf"]
                    : ["jpg", "jpeg", "png", "gif", "webp"],
              };

              const uploadStream = cloudinary.uploader.upload_stream(
                uploadOptions,
                (error, result) => {
                  if (error) reject(error);
                  else resolve(result);
                }
              );

              uploadStream.end(file.buffer);
            });

            attachments.push({
              url: result.secure_url,
              publicId: result.public_id,
              filename: file.originalname,
              fileType: fileType,
              mimeType: file.mimetype,
              fileSize: file.size,
            });
          }
        } catch (uploadError) {
          console.error(
            `❌ Failed to upload ${file.originalname}:`,
            uploadError
          );
          // Continue with other files
        }
      }

      // Add attachments to task data
      if (attachments.length > 0) {
        taskData.attachments = attachments;
      }

      // Add reference images to checklist items
      if (Object.keys(checklistImagesMap).length > 0 && taskData.checklist) {
        taskData.checklist = taskData.checklist.map((item, index) => {
          if (checklistImagesMap[index]) {
            return {
              ...item,
              referenceImages: checklistImagesMap[index],
            };
          }
          return item;
        });
      }
    }

    // If assignedTo is provided, set status to in-progress
    if (taskData.assignedTo) {
      console.log("📝 assignedTo found, overriding status to in-progress");
      taskData.status = "in-progress";
      taskData.startedAt = new Date();
      taskData.approvedBy = req.user.userId;

      // Unlock first section if section-based
      if (
        taskData.hasSections &&
        taskData.sections &&
        taskData.sections.length > 0
      ) {
        taskData.sections[0].status = "available";
      }
    }

    console.log("📝 Final status before creating task:", taskData.status);
    const task = await Task.create(taskData);

    // Populate for response
    await task.populate("createdBy", "name email");
    if (task.assignedTo) {
      await task.populate("assignedTo", "name email");
    }

    // Send notification if directly assigned
    if (task.assignedTo) {
      await createNotification(
        {
          recipient: task.assignedTo._id,
          type: "task_assigned",
          title: "New Task Assigned",
          message: `You've been assigned a new task: "${task.title}"`,
          actionUrl: `/tasks/my-queue`,
          metadata: { taskId: task._id },
        },
        req.app.get("io")
      );
    }

    res.status(201).json({
      success: true,
      message: "Task created successfully",
      task,
    });
  } catch (error) {
    console.error("❌ Create task error:", error);
    console.error("Error details:", error.message);
    res
      .status(500)
      .json({ error: "Failed to create task", details: error.message });
  }
};

// ===========================
// @desc    Get task by ID
// @route   GET /api/tasks/:id
// @access  Private
// ===========================
export const getTaskById = async (req, res) => {
  try {
    const { id } = req.params;

    const task = await Task.findById(id)
      .populate("createdBy", "name email")
      .populate("assignedTo", "name email")
      .populate("requestedBy", "name email")
      .populate("approvedBy", "name email")
      .lean();

    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Check access permissions
    const canAccess =
      hasFullAccess(req.user) ||
      task.category === req.user.assignedProject ||
      task.assignedTo?._id.toString() === req.user.userId;

    if (!canAccess) {
      return res.status(403).json({ error: "Access denied to this task" });
    }

    res.json({ success: true, task });
  } catch (error) {
    console.error("Get task by ID error:", error);
    res.status(500).json({ error: "Failed to fetch task" });
  }
};

// ===========================
// @desc    Get pending task requests (Admin)
// @route   GET /api/tasks/pending-requests
// @access  Private (Admin)
// ===========================
export const getPendingRequests = async (req, res) => {
  try {
    const { status } = req.query;

    // Build filter - default to "pending" if no status provided
    const filter = {};
    const requestedStatus = status || "pending"; // Default to "pending"

    // Validate status value
    const validStatuses = [
      "pending",
      "approved",
      "denied",
      "cancelled",
      "expired",
      "all",
    ];
    if (!validStatuses.includes(requestedStatus)) {
      return res.status(400).json({
        error: `Invalid status. Must be one of: ${validStatuses.join(", ")}`,
      });
    }

    // Apply filter only if not "all"
    if (requestedStatus !== "all") {
      filter.status = requestedStatus;
    }

    const requests = await TaskRequest.find(filter)
      .populate("taskId", "title category priority xpValue status")
      .populate("requestedBy", "name email talentId")
      .sort({ createdAt: -1 })
      .lean();

    // Handle cases where requestedBy might not populate
    const formattedRequests = requests.map((req) => ({
      ...req,
      requestedBy: req.requestedBy || {
        _id: req.requestedBy?._id || "unknown",
        name: "Unknown Talent",
        email: "N/A",
        talentId: "N/A",
      },
    }));

    res.json({
      success: true,
      count: formattedRequests.length,
      filter: requestedStatus,
      requests: formattedRequests,
    });
  } catch (error) {
    console.error("Get pending requests error:", error);
    res.status(500).json({ error: "Failed to fetch pending requests" });
  }
};

// ===========================
// @desc    Get my pending requests (Talent)
// @route   GET /api/tasks/my-pending-requests
// @access  Private (Talent)
// ===========================
export const getMyPendingRequests = async (req, res) => {
  try {
    const requests = await TaskRequest.find({
      requestedBy: req.user.userId,
      status: "pending",
    })
      .populate("taskId", "title category priority complexity xpValue deadline")
      .sort({ createdAt: -1 })
      .lean();

    res.json({
      success: true,
      count: requests.length,
      requests,
    });
  } catch (error) {
    console.error("Get my pending requests error:", error);
    res.status(500).json({ error: "Failed to fetch your pending requests" });
  }
};

// ===========================
// @desc    Mark task as complete (Talent)
// @route   POST /api/tasks/:id/complete
// @access  Private (Talent)
// ===========================
export const completeTask = async (req, res) => {
  try {
    const { id } = req.params;

    const task = await Task.findById(id);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Verify talent is assigned to this task
    if (task.assignedTo.toString() !== req.user.userId) {
      return res
        .status(403)
        .json({ error: "You are not assigned to this task" });
    }

    // Check task is in-progress (NOT pending-review for checklist tasks)
    if (task.status !== "in-progress") {
      return res.status(400).json({
        error: `Cannot complete task with status: ${task.status}. Checklist tasks in "pending-review" must be completed by admin.`,
      });
    }

    // For section-based tasks, check all sections completed
    if (task.hasSections) {
      const allSectionsCompleted = task.sections.every(
        (s) => s.status === "completed"
      );
      if (!allSectionsCompleted) {
        return res.status(400).json({
          error:
            "All sections must be completed before marking task as complete",
        });
      }
    }

    // For checklist-based tasks, check all items completed
    if (task.hasChecklist) {
      const allItemsCompleted = task.checklist.every((item) => item.completed);
      if (!allItemsCompleted) {
        return res.status(400).json({
          error:
            "All checklist items must be completed before marking task as complete",
        });
      }
    }

    // Update task
    task.status = "completed";
    task.completedAt = new Date();
    await task.save();

    // Award XP to talent
    const talent = await Talent.findById(req.user.userId);
    if (talent) {
      talent.xp += task.xpValue;
      await talent.save();

      console.log(
        `✅ Awarded ${task.xpValue} XP to ${talent.name} for completing "${task.title}"`
      );
    }

    // Send notification to admin
    try {
      await createNotification(
        {
          recipient: null, // Broadcast to admins
          type: "task_completed",
          title: "Task Completed",
          message: `${talent?.name || req.user.email} completed "${
            task.title
          }" and earned ${task.xpValue} XP`,
          actionUrl: `/admin/tasks`,
          metadata: { taskId: task._id, xpAwarded: task.xpValue },
        },
        req.app.get("io")
      );
    } catch (notifError) {
      console.error("⚠️ Failed to send completion notification:", notifError);
    }

    res.json({
      success: true,
      message: `Task completed! You earned ${task.xpValue} XP`,
      task,
      xpAwarded: task.xpValue,
    });
  } catch (error) {
    console.error("Complete task error:", error);
    res.status(500).json({ error: "Failed to complete task" });
  }
};

// ===========================
// @desc    Admin completes task (for pending-review tasks)
// @route   POST /api/tasks/:id/admin-complete
// @access  Private (Admin)
// ===========================
export const adminCompleteTask = async (req, res) => {
  try {
    const { id: taskId } = req.params;
    const { feedback } = req.body;

    const task = await Task.findById(taskId).populate(
      "assignedTo",
      "name email xp"
    );

    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Check if all sections are completed (for section-based tasks)
    const allSectionsCompleted =
      task.taskType === "section-based" &&
      task.sections?.length > 0 &&
      task.sections.every((s) => s.status === "completed");

    // Verify task is in pending-review status OR all sections are completed
    if (task.status !== "pending-review" && !allSectionsCompleted) {
      return res.status(400).json({
        error: `Cannot complete task with status: ${task.status}. Must be in "pending-review" status or all sections must be completed.`,
      });
    }

    // For checklist tasks, verify all items completed
    if (task.hasChecklist) {
      const allItemsCompleted = task.checklist.every((item) => item.completed);
      if (!allItemsCompleted) {
        return res.status(400).json({
          error: "All checklist items must be completed",
        });
      }
    }

    // Update task - use findByIdAndUpdate to avoid re-validating unchanged fields
    const updateData = {
      status: "completed",
      completedAt: new Date(),
      completedBy: req.user.userId,
    };
    if (feedback) updateData.feedback = feedback;

    const updatedTask = await Task.findByIdAndUpdate(taskId, updateData, {
      new: true,
      runValidators: false, // Skip validation since we're only updating specific fields
    }).populate("assignedTo", "name email xp");

    if (!updatedTask) {
      return res.status(404).json({ error: "Failed to update task" });
    }

    // Award XP to assigned talent using findByIdAndUpdate to avoid validation issues
    const talent = await Talent.findByIdAndUpdate(
      updatedTask.assignedTo._id,
      { $inc: { xp: updatedTask.xpValue } },
      { new: true, runValidators: false }
    );
    if (talent) {
      console.log(
        `✅ Admin completed task "${updatedTask.title}" - Awarded ${updatedTask.xpValue} XP to ${talent.name}`
      );

      // Send notification to talent
      try {
        await createNotification(
          {
            recipient: talent._id,
            type: "task_completed",
            title: "Task Approved & Completed!",
            message: `Your task "${
              updatedTask.title
            }" has been approved by admin. You earned ${
              updatedTask.xpValue
            } XP!${feedback ? ` Feedback: ${feedback}` : ""}`,
            actionUrl: `/tasks/${updatedTask._id}`,
            metadata: {
              taskId: updatedTask._id,
              xpAwarded: updatedTask.xpValue,
            },
          },
          req.app.get("io")
        );
      } catch (notifError) {
        console.error("⚠️ Failed to send completion notification:", notifError);
      }
    }

    res.json({
      success: true,
      message: `Task completed successfully. ${
        updatedTask.xpValue
      } XP awarded to ${talent?.name || "talent"}`,
      task: updatedTask,
      xpAwarded: updatedTask.xpValue,
    });
  } catch (error) {
    console.error("Admin complete task error:", error);
    res.status(500).json({ error: "Failed to complete task" });
  }
};

// ===========================
// @desc    Cancel task request (Talent)
// @route   DELETE /api/tasks/:id/cancel-request
// @access  Private (Talent)
// ===========================
export const cancelTaskRequest = async (req, res) => {
  try {
    const { id: taskId } = req.params;

    // Find user's pending request
    const taskRequest = await TaskRequest.findOne({
      taskId,
      requestedBy: req.user.userId,
      status: "pending",
    });

    if (!taskRequest) {
      return res.status(404).json({ error: "No pending request found" });
    }

    // Update task back to available
    const task = await Task.findById(taskId);
    if (task) {
      task.status = "available";
      task.requestedBy = null;
      await task.save();
    }

    // Delete the request
    await taskRequest.deleteOne();

    res.json({
      success: true,
      message: "Request cancelled successfully",
    });
  } catch (error) {
    console.error("Cancel request error:", error);
    res.status(500).json({ error: "Failed to cancel request" });
  }
};

// ===========================
// PHASE 3: SECTION MANAGEMENT
// ===========================

// ===========================
// @desc    Submit section work with PR link
// @route   POST /api/tasks/:id/sections/:sectionNum/submit
// @access  Private (Talent - must be assigned)
// ===========================
export const submitSection = async (req, res) => {
  try {
    const { id, sectionNum } = req.params;
    const { prLink } = req.body;

    console.log("📸 submitSection received:", {
      prLink,
      files: req.files?.length || 0,
      body: req.body,
    });

    // Validate PR link format
    if (!prLink || !prLink.startsWith("http")) {
      return res.status(400).json({
        error: "Valid PR link is required (must start with http)",
      });
    }

    const task = await Task.findById(id);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Verify talent is assigned
    if (task.assignedTo.toString() !== req.user.userId) {
      return res
        .status(403)
        .json({ error: "You are not assigned to this task" });
    }

    // Verify task is in valid status (allow resubmission for needs-revision)
    if (!["in-progress", "needs-revision"].includes(task.status)) {
      return res.status(400).json({
        error: `Cannot submit section for task with status: ${task.status}`,
      });
    }

    // Find section
    const section = task.sections.find(
      (s) => s.number === parseInt(sectionNum)
    );
    if (!section) {
      return res.status(404).json({ error: "Section not found" });
    }

    // Verify section is unlocked or needs-revision
    if (section.status === "locked") {
      return res.status(400).json({
        error: "This section is locked. Complete previous sections first.",
      });
    }

    // Handle screenshot uploads to Cloudinary
    const uploadedScreenshots = [];
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        try {
          const b64 = Buffer.from(file.buffer).toString("base64");
          const dataURI = `data:${file.mimetype};base64,${b64}`;

          const uploadResult = await cloudinary.uploader.upload(dataURI, {
            folder: `tasks/${id}/sections/${sectionNum}`,
            resource_type: "image",
            transformation: [
              { width: 1200, height: 1200, crop: "limit" },
              { quality: "auto" },
            ],
          });

          uploadedScreenshots.push({
            url: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            filename: file.originalname,
            uploadedAt: new Date(),
          });
        } catch (uploadError) {
          console.error("Screenshot upload error:", uploadError);
        }
      }
    }

    // Initialize submission history if not exists
    if (!section.submissionHistory) {
      section.submissionHistory = [];
    }
    if (!section.currentAttempt) {
      section.currentAttempt = 1;
    }

    // Add to submission history
    section.submissionHistory.push({
      attemptNumber: section.currentAttempt,
      prLink: prLink,
      screenshots: uploadedScreenshots,
      submittedAt: new Date(),
      status: "pending-review",
    });

    // Update section
    section.status = "pending-review";
    section.prLink = prLink;
    section.screenshots = uploadedScreenshots;
    section.submittedAt = new Date();

    // Clear previous revision issues when resubmitting
    if (section.issues && section.issues.length > 0) {
      section.issues = [];
    }
    if (section.feedback) {
      section.feedback = null;
    }

    // If task was "needs-revision", change it back to "in-progress"
    if (task.status === "needs-revision") {
      task.status = "in-progress";
    }

    await task.save();

    // Notify admin
    try {
      await createNotification(
        {
          recipient: task.createdBy,
          type: "admin_message",
          title: "Section Submitted for Review",
          message: `${req.user.name} submitted Section ${sectionNum} of "${task.title}" for review`,
          actionUrl: `/admin/tasks/${task._id}`,
          metadata: { taskId: task._id, sectionNumber: parseInt(sectionNum) },
        },
        req.app.get("io")
      );
    } catch (notifError) {
      console.error(
        "⚠️ Failed to send section submission notification:",
        notifError
      );
    }

    res.json({
      success: true,
      message: "Section submitted for review",
      task,
    });
  } catch (error) {
    console.error("Submit section error:", error);
    res.status(500).json({ error: "Failed to submit section" });
  }
};

// ===========================
// @desc    Approve section (Admin)
// @route   POST /api/tasks/:id/sections/:sectionNum/approve
// @access  Private (Admin only)
// ===========================
export const approveSection = async (req, res) => {
  try {
    const { id, sectionNum } = req.params;

    const task = await Task.findById(id).populate(
      "assignedTo",
      "name email xp"
    );
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Find section
    const sectionIndex = task.sections.findIndex(
      (s) => s.number === parseInt(sectionNum)
    );
    if (sectionIndex === -1) {
      return res.status(404).json({ error: "Section not found" });
    }

    const section = task.sections[sectionIndex];

    // Verify section is pending review
    if (section.status !== "pending-review") {
      return res.status(400).json({
        error: `Cannot approve section with status: ${section.status}`,
      });
    }

    // Update the last submission history entry with approval
    if (section.submissionHistory && section.submissionHistory.length > 0) {
      const lastSubmission =
        section.submissionHistory[section.submissionHistory.length - 1];
      lastSubmission.status = "approved";
      lastSubmission.reviewedBy = req.user.userId;
      lastSubmission.reviewedAt = new Date();
    }

    // Update section
    section.status = "completed";
    section.approvedBy = req.user.userId;
    section.approvedAt = new Date();

    // Check if all sections are now completed
    const allSectionsCompleted = task.sections.every(
      (s) => s.status === "completed"
    );

    if (allSectionsCompleted) {
      // Change task status to pending-review for admin to mark complete
      task.status = "pending-review";

      // Notify talent that all sections are done
      try {
        await createNotification(
          {
            recipient: task.assignedTo._id,
            type: "admin_message",
            title: "All Sections Completed",
            message: `All sections of "${task.title}" have been approved! Waiting for admin to mark task as complete.`,
            actionUrl: `/tasks/${task._id}`,
            metadata: { taskId: task._id },
          },
          req.app.get("io")
        );
      } catch (notifError) {
        console.error(
          "⚠️ Failed to send all sections completed notification:",
          notifError
        );
      }
    } else if (sectionIndex + 1 < task.sections.length) {
      // Unlock next section if exists
      const nextSection = task.sections[sectionIndex + 1];
      if (nextSection.status === "locked") {
        nextSection.status = "unlocked";

        // Notify talent about next section unlock
        try {
          await createNotification(
            {
              recipient: task.assignedTo._id,
              type: "task_assigned",
              title: "Next Section Unlocked",
              message: `Section ${nextSection.number} of "${task.title}" is now unlocked. You can begin work!`,
              actionUrl: `/tasks/${task._id}`,
              metadata: { taskId: task._id, sectionNumber: nextSection.number },
            },
            req.app.get("io")
          );
        } catch (notifError) {
          console.error(
            "⚠️ Failed to send section unlock notification:",
            notifError
          );
        }
      }
    }

    // Award proportional XP for section completion
    const sectionXP = Math.floor(task.xpValue / task.sections.length);
    if (task.assignedTo && sectionXP > 0) {
      task.assignedTo.xp += sectionXP;
      await task.assignedTo.save();

      console.log(
        `✅ Awarded ${sectionXP} XP to ${task.assignedTo.name} for completing section ${sectionNum} of "${task.title}"`
      );
    }

    await task.save();

    // Notify talent of approval
    try {
      await createNotification(
        {
          recipient: task.assignedTo._id,
          type: "admin_message",
          title: "Section Approved",
          message: `Your Section ${sectionNum} of "${task.title}" has been approved! You earned ${sectionXP} XP`,
          actionUrl: `/tasks/${task._id}`,
          metadata: {
            taskId: task._id,
            sectionNumber: parseInt(sectionNum),
            xpAwarded: sectionXP,
          },
        },
        req.app.get("io")
      );
    } catch (notifError) {
      console.error(
        "⚠️ Failed to send section approval notification:",
        notifError
      );
    }

    res.json({
      success: true,
      message: "Section approved successfully",
      xpAwarded: sectionXP,
      task,
    });
  } catch (error) {
    console.error("Approve section error:", error);
    res.status(500).json({ error: "Failed to approve section" });
  }
};

// ===========================
// @desc    Reject section (Admin)
// @route   POST /api/tasks/:id/sections/:sectionNum/reject
// @access  Private (Admin only)
// ===========================
export const rejectSection = async (req, res) => {
  try {
    const { id, sectionNum } = req.params;
    const { feedback } = req.body;

    // Parse issues from JSON string (sent as form-data)
    let issues = [];
    try {
      issues = req.body.issues ? JSON.parse(req.body.issues) : [];
    } catch (parseError) {
      return res.status(400).json({
        error: "Invalid issues format. Must be valid JSON array",
      });
    }

    // Validate issues array
    if (!issues || !Array.isArray(issues) || issues.length === 0) {
      return res.status(400).json({
        error: "At least one issue is required when rejecting a section",
      });
    }

    // Validate each issue has required fields
    for (const issue of issues) {
      if (!issue.description || !issue.priority) {
        return res.status(400).json({
          error: "Each issue must have a description and priority",
        });
      }
    }

    // Handle screenshot uploads for each issue
    const files = req.files || [];
    if (files.length > 0) {
      const { uploadToCloudinary } = await import("../../utils/cloudinary.js");

      // Upload each screenshot and map to issues by index
      for (let i = 0; i < files.length && i < issues.length; i++) {
        const file = files[i];
        try {
          const uploadResult = await uploadToCloudinary(file.buffer, {
            folder: "tasks/revision-screenshots",
            resourceType: "image",
            format: file.mimetype.split("/")[1],
          });

          issues[i].screenshot = {
            url: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            filename: file.originalname,
          };
        } catch (uploadError) {
          console.error(`Failed to upload screenshot ${i}:`, uploadError);
          // Continue without screenshot for this issue
        }
      }
    }

    const task = await Task.findById(id).populate("assignedTo", "name email");
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Find section
    const section = task.sections.find(
      (s) => s.number === parseInt(sectionNum)
    );
    if (!section) {
      return res.status(404).json({ error: "Section not found" });
    }

    // Verify section is pending review
    if (section.status !== "pending-review") {
      return res.status(400).json({
        error: `Cannot reject section with status: ${section.status}`,
      });
    }

    // Update the last submission history entry with rejection details
    if (section.submissionHistory && section.submissionHistory.length > 0) {
      const lastSubmission =
        section.submissionHistory[section.submissionHistory.length - 1];
      lastSubmission.status = "rejected";
      lastSubmission.reviewedBy = req.user.userId;
      lastSubmission.reviewedAt = new Date();
      lastSubmission.feedback = feedback;
      lastSubmission.issues = issues;
    }

    // Update section status to needs-revision
    section.status = "needs-revision";
    section.feedback = feedback || "Please review and fix the issues below";
    section.issues = issues;

    // Increment attempt number for next submission
    section.currentAttempt = (section.currentAttempt || 1) + 1;

    // Update task status to needs-revision if not already
    if (task.status !== "needs-revision") {
      task.status = "needs-revision";
    }

    await task.save();

    // Notify talent
    try {
      const issueCount = issues.length;
      const highPriorityCount = issues.filter(
        (i) => i.priority === "high"
      ).length;

      await createNotification(
        {
          recipient: task.assignedTo._id,
          type: "admin_message",
          title: "Section Needs Revision",
          message: `Section ${sectionNum} of "${
            task.title
          }" needs revision. ${issueCount} issue${
            issueCount > 1 ? "s" : ""
          } found${
            highPriorityCount > 0 ? ` (${highPriorityCount} high priority)` : ""
          }`,
          actionUrl: `/tasks/${task._id}`,
          metadata: {
            taskId: task._id,
            sectionNumber: parseInt(sectionNum),
            issueCount,
            highPriorityCount,
          },
        },
        req.app.get("io")
      );
    } catch (notifError) {
      console.error(
        "⚠️ Failed to send section rejection notification:",
        notifError
      );
    }

    res.json({
      success: true,
      message: "Section sent back for revision",
      task,
    });
  } catch (error) {
    console.error("Reject section error:", error);
    res.status(500).json({ error: "Failed to reject section" });
  }
};

// ===========================
// PHASE 3: CHECKLIST MANAGEMENT
// ===========================

// ===========================
// @desc    Mark checklist item as complete
// @route   PUT /api/tasks/:id/checklist/:itemNum/complete
// @access  Private (Talent - must be assigned)
// ===========================
export const completeChecklistItem = async (req, res) => {
  try {
    const { id, itemNum } = req.params;
    const { note } = req.body;

    const task = await Task.findById(id);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Verify talent is assigned
    if (task.assignedTo.toString() !== req.user.userId) {
      return res
        .status(403)
        .json({ error: "You are not assigned to this task" });
    }

    // Verify task is in-progress
    if (task.status !== "in-progress") {
      return res.status(400).json({
        error: `Cannot update checklist for task with status: ${task.status}`,
      });
    }

    // Find checklist item
    const item = task.checklist.find((c) => c.number === parseInt(itemNum));
    if (!item) {
      return res.status(404).json({ error: "Checklist item not found" });
    }

    // Require at least one screenshot
    if (!req.files || req.files.length === 0) {
      return res
        .status(400)
        .json({ error: "At least one screenshot is required" });
    }

    // Handle file uploads to Cloudinary
    const uploadedScreenshots = [];
    if (req.files && req.files.length > 0) {
      const screenshotFiles = req.files.slice(0, 5); // Max 5 screenshots

      for (const file of screenshotFiles) {
        try {
          // Convert buffer to base64
          const b64 = Buffer.from(file.buffer).toString("base64");
          const dataURI = `data:${file.mimetype};base64,${b64}`;

          // Upload to Cloudinary
          const uploadResult = await cloudinary.uploader.upload(dataURI, {
            folder: `tasks/${id}/checklist/${itemNum}`,
            resource_type: "auto",
            transformation: [
              { width: 1200, height: 1200, crop: "limit" },
              { quality: "auto" },
            ],
          });

          uploadedScreenshots.push({
            url: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            uploadedAt: new Date(),
          });
        } catch (uploadError) {
          console.error("Screenshot upload error:", uploadError);
          // Continue with other uploads even if one fails
        }
      }
    }

    // Update item
    item.completed = true;
    item.completedAt = new Date();
    if (note) item.completionNote = note.trim();
    item.screenshots = uploadedScreenshots;

    await task.save();

    console.log(
      `✅ Checklist item ${itemNum} completed with ${uploadedScreenshots.length} screenshots`
    );

    // Calculate completion percentage
    const completedCount = task.checklist.filter((c) => c.completed).length;
    const totalCount = task.checklist.length;
    const percentage = Math.round((completedCount / totalCount) * 100);

    res.json({
      success: true,
      message: "Checklist item marked complete",
      progress: {
        completed: completedCount,
        total: totalCount,
        percentage,
      },
      task,
    });
  } catch (error) {
    console.error("Complete checklist item error:", error);
    res.status(500).json({ error: "Failed to complete checklist item" });
  }
};

// ===========================
// @desc    Submit entire checklist task for review
// @route   POST /api/tasks/:id/checklist/submit
// @access  Private (Talent - must be assigned)
// ===========================
export const submitChecklistTask = async (req, res) => {
  try {
    const { id } = req.params;
    const { prLink } = req.body;

    // Validate PR link is required
    if (!prLink || !prLink.trim()) {
      return res
        .status(400)
        .json({ error: "PR/test link is required for submission" });
    }

    const task = await Task.findById(id);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Verify talent is assigned
    if (task.assignedTo.toString() !== req.user.userId) {
      return res
        .status(403)
        .json({ error: "You are not assigned to this task" });
    }

    // Verify task is in-progress
    if (task.status !== "in-progress") {
      return res.status(400).json({
        error: `Cannot submit task with status: ${task.status}`,
      });
    }

    // Verify all checklist items completed
    const allCompleted = task.checklist.every((item) => item.completed);
    if (!allCompleted) {
      return res.status(400).json({
        error: "All checklist items must be completed before submission",
      });
    }

    // Store PR link
    task.prLink = prLink.trim();

    // Update task status to pending-review
    task.status = "pending-review";

    await task.save();

    // Notify admin
    try {
      await createNotification(
        {
          recipient: task.createdBy,
          type: "admin_message",
          title: "Checklist Task Submitted",
          message: `${req.user.name} completed all items and submitted "${task.title}" for review`,
          actionUrl: `/admin/tasks/${task._id}`,
          metadata: { taskId: task._id },
        },
        req.app.get("io")
      );
    } catch (notifError) {
      console.error(
        "⚠️ Failed to send checklist submission notification:",
        notifError
      );
    }

    res.json({
      success: true,
      message: "Task submitted for review",
      task,
    });
  } catch (error) {
    console.error("Submit checklist task error:", error);
    res.status(500).json({ error: "Failed to submit task" });
  }
};

// ===========================
// PHASE 4: DEADLINE DETECTION & ALERTS
// ===========================

// ===========================
// @desc    Get overdue tasks
// @route   GET /api/tasks/overdue
// @access  Private
// ===========================
export const getOverdueTasks = async (req, res) => {
  try {
    const now = new Date();

    let filter = {
      dueDate: { $lt: now },
      status: {
        $in: [
          "available",
          "pending-approval",
          "in-progress",
          "needs-revision",
          "pending-review",
        ],
      },
      isArchived: false,
    };

    // Talents only see their assigned tasks
    if (!hasFullAccess(req.user)) {
      filter.assignedTo = req.user.userId;
    }

    const tasks = await Task.find(filter)
      .populate("assignedTo", "name email")
      .populate("createdBy", "name email")
      .sort({ dueDate: 1 })
      .lean();

    res.json({
      success: true,
      count: tasks.length,
      tasks,
    });
  } catch (error) {
    console.error("Get overdue tasks error:", error);
    res.status(500).json({ error: "Failed to fetch overdue tasks" });
  }
};

// ===========================
// @desc    Get tasks due today
// @route   GET /api/tasks/due-today
// @access  Private
// ===========================
export const getDueTodayTasks = async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    let filter = {
      dueDate: { $gte: today, $lt: tomorrow },
      status: {
        $in: [
          "available",
          "pending-approval",
          "in-progress",
          "needs-revision",
          "pending-review",
        ],
      },
      isArchived: false,
    };

    // Talents only see their assigned tasks
    if (!hasFullAccess(req.user)) {
      filter.assignedTo = req.user.userId;
    }

    const tasks = await Task.find(filter)
      .populate("assignedTo", "name email")
      .populate("createdBy", "name email")
      .sort({ dueDate: 1 })
      .lean();

    res.json({
      success: true,
      count: tasks.length,
      tasks,
    });
  } catch (error) {
    console.error("Get due today tasks error:", error);
    res.status(500).json({ error: "Failed to fetch tasks due today" });
  }
};

// ===========================
// @desc    Get tasks due this week
// @route   GET /api/tasks/due-this-week
// @access  Private
// ===========================
export const getDueThisWeekTasks = async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    let filter = {
      dueDate: { $gte: today, $lte: nextWeek },
      status: {
        $in: [
          "available",
          "pending-approval",
          "in-progress",
          "needs-revision",
          "pending-review",
        ],
      },
      isArchived: false,
    };

    // Talents only see their assigned tasks
    if (!hasFullAccess(req.user)) {
      filter.assignedTo = req.user.userId;
    }

    const tasks = await Task.find(filter)
      .populate("assignedTo", "name email")
      .populate("createdBy", "name email")
      .sort({ dueDate: 1 })
      .lean();

    res.json({
      success: true,
      count: tasks.length,
      tasks,
    });
  } catch (error) {
    console.error("Get due this week tasks error:", error);
    res.status(500).json({ error: "Failed to fetch tasks due this week" });
  }
};

// ===========================
// ADMIN ENDPOINTS: TASK UPDATES & MANAGEMENT
// ===========================

// ===========================
// @desc    Update task details
// @route   PUT /api/tasks/:id
// @access  Private (Admin only)
// ===========================
export const updateTask = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = { ...req.body };

    // Find task
    const task = await Task.findById(id);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Prevent updating certain fields
    const protectedFields = ["_id", "createdBy", "createdAt", "xpValue"];
    protectedFields.forEach((field) => delete updates[field]);

    // Parse JSON fields if they exist
    if (updates.sections && typeof updates.sections === "string") {
      updates.sections = JSON.parse(updates.sections);
    }
    if (updates.checklist && typeof updates.checklist === "string") {
      updates.checklist = JSON.parse(updates.checklist);
    }
    if (
      updates.deletedAttachments &&
      typeof updates.deletedAttachments === "string"
    ) {
      updates.deletedAttachments = JSON.parse(updates.deletedAttachments);
    }

    // Handle deleted attachments
    if (
      updates.deletedAttachments &&
      Array.isArray(updates.deletedAttachments)
    ) {
      task.attachments = task.attachments.filter(
        (att) => !updates.deletedAttachments.includes(att._id.toString())
      );
      delete updates.deletedAttachments;
    }

    // Handle new file attachments
    if (req.files && Array.isArray(req.files)) {
      const attachmentFiles = req.files.filter(
        (file) => file.fieldname === "attachments"
      );

      for (const file of attachmentFiles) {
        let folder = "tasks/attachments/images";
        if (file.mimetype === "application/pdf") {
          folder = "tasks/attachments/pdfs";
        }

        const uploadResult = await new Promise((resolve, reject) => {
          const uploadStream = cloudinary.uploader.upload_stream(
            { folder, resource_type: "auto" },
            (error, result) => {
              if (error) reject(error);
              else resolve(result);
            }
          );
          uploadStream.end(file.buffer);
        });

        task.attachments.push({
          filename: file.originalname,
          fileUrl: uploadResult.secure_url,
          mimeType: file.mimetype,
          fileSize: file.size,
          uploadedBy: req.user.userId,
        });
      }
    }

    // If updating sections, preserve existing statuses
    if (updates.sections && task.taskType === "section-based") {
      updates.sections = updates.sections.map((newSection, idx) => ({
        ...newSection,
        status: task.sections[idx]?.status || "locked",
        completedAt: task.sections[idx]?.completedAt,
        approvedBy: task.sections[idx]?.approvedBy,
      }));
    }

    // If updating checklist, preserve completion status and handle new reference images
    if (updates.checklist && task.taskType === "checklist-based") {
      updates.checklist = await Promise.all(
        updates.checklist.map(async (newItem, idx) => {
          const existingItem = task.checklist[idx] || {};

          // Handle new reference images for this checklist item
          const checklistImageFiles =
            req.files?.filter(
              (file) => file.fieldname === `checklistImages_${idx}`
            ) || [];

          const referenceImages = existingItem.referenceImages || [];

          for (const file of checklistImageFiles) {
            const uploadResult = await new Promise((resolve, reject) => {
              const uploadStream = cloudinary.uploader.upload_stream(
                {
                  folder: "tasks/checklist-references",
                  resource_type: "image",
                },
                (error, result) => {
                  if (error) reject(error);
                  else resolve(result);
                }
              );
              uploadStream.end(file.buffer);
            });

            referenceImages.push({
              url: uploadResult.secure_url,
              publicId: uploadResult.public_id,
              filename: file.originalname,
              uploadedAt: new Date(),
            });
          }

          return {
            ...newItem,
            number: newItem.number || idx + 1,
            isCompleted: existingItem.isCompleted || false,
            completedAt: existingItem.completedAt,
            referenceImages,
            screenshots: existingItem.screenshots || [],
          };
        })
      );
    }

    // Update task
    Object.assign(task, updates);
    await task.save();

    const updatedTask = await Task.findById(id)
      .populate("assignedTo", "name email")
      .populate("createdBy", "name email");

    res.json({
      success: true,
      message: "Task updated successfully",
      task: updatedTask,
    });
  } catch (error) {
    console.error("Update task error:", error);
    res.status(500).json({ error: "Failed to update task" });
  }
};

// ===========================
// @desc    Delete/Archive task
// @route   DELETE /api/tasks/:id
// @access  Private (Admin only)
// ===========================
export const deleteTask = async (req, res) => {
  try {
    const { id } = req.params;
    const { permanent = false } = req.query;

    const task = await Task.findById(id);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    if (permanent === "true") {
      // Permanent delete
      await Task.findByIdAndDelete(id);
      await TaskRequest.deleteMany({ taskId: id });

      res.json({
        success: true,
        message: "Task permanently deleted",
      });
    } else {
      // Soft delete (archive) - use findByIdAndUpdate to avoid validation issues
      const updatedTask = await Task.findByIdAndUpdate(
        id,
        {
          $set: {
            isArchived: true,
            status: "cancelled",
          },
        },
        { new: true, runValidators: false }
      );

      res.json({
        success: true,
        message: "Task archived successfully",
        task: updatedTask,
      });
    }
  } catch (error) {
    console.error("Delete task error:", error);
    res.status(500).json({ error: "Failed to delete task" });
  }
};

// ===========================
// @desc    Direct assign task to talent (bypass request workflow)
// @route   POST /api/tasks/:id/assign
// @access  Private (Admin only)
// ===========================
export const assignTask = async (req, res) => {
  try {
    const { id } = req.params;
    const { talentId } = req.body;

    if (!talentId) {
      return res.status(400).json({ error: "Talent ID required" });
    }

    // Find task
    const task = await Task.findById(id);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Validate talent exists
    const talent = await Talent.findById(talentId);
    if (!talent) {
      return res.status(404).json({ error: "Talent not found" });
    }

    // Validate project match (unless talent has full access)
    if (
      talent.assignedProject !== task.category &&
      talent.email !== "<EMAIL>"
    ) {
      return res.status(400).json({
        error: `Talent's project (${talent.assignedProject}) doesn't match task category (${task.category})`,
      });
    }

    // Check if already assigned
    if (task.assignedTo && task.assignedTo.toString() === talentId) {
      return res
        .status(400)
        .json({ error: "Task already assigned to this talent" });
    }

    // Assign task
    task.assignedTo = talentId;
    task.status = "in-progress";
    task.startedAt = new Date();

    // Unlock first section if section-based
    if (task.hasSections && task.sections.length > 0) {
      task.sections[0].status = "unlocked";
    }

    await task.save();

    // Send notification to talent
    const io = req.app.get("io");
    await createNotification(
      {
        recipient: talentId,
        type: "task_assigned",
        title: "Task Assigned to You",
        message: `Admin directly assigned you the task: "${task.title}"`,
        priority: "medium",
        actionUrl: `/tasks/${task._id}`,
      },
      io
    );

    const updatedTask = await Task.findById(id)
      .populate("assignedTo", "name email")
      .populate("createdBy", "name email");

    res.json({
      success: true,
      message: "Task assigned successfully",
      task: updatedTask,
    });
  } catch (error) {
    console.error("Assign task error:", error);
    res.status(500).json({ error: "Failed to assign task" });
  }
};

// ===========================
// @desc    Reassign task to different talent
// @route   POST /api/tasks/:id/reassign
// @access  Private (Admin only)
// ===========================
export const reassignTask = async (req, res) => {
  try {
    const { id } = req.params;
    const { newTalentId } = req.body;

    if (!newTalentId) {
      return res.status(400).json({ error: "New talent ID required" });
    }

    // Find task
    const task = await Task.findById(id).populate("assignedTo", "name email");
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    if (!task.assignedTo) {
      return res.status(400).json({ error: "Task is not assigned to anyone" });
    }

    // Validate new talent exists
    const newTalent = await Talent.findById(newTalentId);
    if (!newTalent) {
      return res.status(404).json({ error: "New talent not found" });
    }

    // Validate project match
    if (
      newTalent.assignedProject !== task.category &&
      newTalent.email !== "<EMAIL>"
    ) {
      return res.status(400).json({
        error: `New talent's project (${newTalent.assignedProject}) doesn't match task category (${task.category})`,
      });
    }

    const oldTalentId = task.assignedTo._id;
    const oldTalentName = task.assignedTo.name;

    // Reassign
    task.assignedTo = newTalentId;
    task.startedAt = new Date(); // Reset start time
    await task.save();

    // Notify old talent
    const io = req.app.get("io");
    await createNotification(
      {
        recipient: oldTalentId,
        type: "admin_message",
        title: "Task Reassigned",
        message: `The task "${task.title}" has been reassigned to another talent.`,
        priority: "medium",
      },
      io
    );

    // Notify new talent
    await createNotification(
      {
        recipient: newTalentId,
        type: "task_assigned",
        title: "New Task Assigned",
        message: `You've been assigned the task: "${task.title}" (reassigned from ${oldTalentName})`,
        priority: "medium",
        actionUrl: `/tasks/${task._id}`,
      },
      io
    );

    const updatedTask = await Task.findById(id)
      .populate("assignedTo", "name email")
      .populate("createdBy", "name email");

    res.json({
      success: true,
      message: "Task reassigned successfully",
      task: updatedTask,
    });
  } catch (error) {
    console.error("Reassign task error:", error);
    res.status(500).json({ error: "Failed to reassign task" });
  }
};

// ===========================
// @desc    Update admin notes (private)
// @route   PUT /api/tasks/:id/admin-notes
// @access  Private (Admin only)
// ===========================
export const updateAdminNotes = async (req, res) => {
  try {
    const { id } = req.params;
    const { adminNotes } = req.body;

    const task = await Task.findById(id);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    task.adminNotes = adminNotes || "";
    await task.save();

    res.json({
      success: true,
      message: "Admin notes updated",
      adminNotes: task.adminNotes,
    });
  } catch (error) {
    console.error("Update admin notes error:", error);
    res.status(500).json({ error: "Failed to update admin notes" });
  }
};

// ===========================
// @desc    Get task statistics for admin dashboard
// @route   GET /api/tasks/admin/stats
// @access  Private (Admin only)
// ===========================
export const getTaskStats = async (req, res) => {
  try {
    const { project } = req.query;

    let filter = { isArchived: false };
    if (project && project !== "all") {
      filter.category = project;
    }

    // Count by project (for the total display)
    const talentPortalTasks = await Task.countDocuments({
      isArchived: false,
      category: "Talent Portal",
    });
    const modelSuiteTasks = await Task.countDocuments({
      isArchived: false,
      category: "ModelSuite",
    });

    // Count by status
    const totalTasks = await Task.countDocuments(filter);
    const availableTasks = await Task.countDocuments({
      ...filter,
      status: "available",
    });
    const inProgressTasks = await Task.countDocuments({
      ...filter,
      status: "in-progress",
    });
    const completedTasks = await Task.countDocuments({
      ...filter,
      status: "completed",
    });
    const needsRevisionTasks = await Task.countDocuments({
      ...filter,
      status: "needs-revision",
    });
    const storedTasks = await Task.countDocuments({
      ...filter,
      status: "stored",
    });

    // Pending requests
    const pendingRequests = await TaskRequest.countDocuments({
      status: "pending",
    });

    // Overdue tasks
    const now = new Date();
    const overdueTasks = await Task.countDocuments({
      ...filter,
      dueDate: { $lt: now },
      status: { $in: ["available", "in-progress", "needs-revision"] },
    });

    // Due this week
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    const dueThisWeek = await Task.countDocuments({
      ...filter,
      dueDate: { $gte: now, $lte: nextWeek },
      status: { $in: ["available", "in-progress", "needs-revision"] },
    });

    // Completion rate (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const completedLast30Days = await Task.countDocuments({
      ...filter,
      status: "completed",
      completedAt: { $gte: thirtyDaysAgo },
    });
    const createdLast30Days = await Task.countDocuments({
      ...filter,
      createdAt: { $gte: thirtyDaysAgo },
    });
    const completionRate =
      createdLast30Days > 0
        ? ((completedLast30Days / createdLast30Days) * 100).toFixed(1)
        : 0;

    res.json({
      success: true,
      stats: {
        totalTasks,
        talentPortalTasks,
        modelSuiteTasks,
        byStatus: {
          available: availableTasks,
          "in-progress": inProgressTasks,
          completed: completedTasks,
          "needs-revision": needsRevisionTasks,
          stored: storedTasks,
        },
        pendingRequests,
        overdueTasks,
        dueThisWeek,
        completionRate: parseFloat(completionRate),
        completedThisMonth: completedLast30Days,
        last30Days: {
          completed: completedLast30Days,
          created: createdLast30Days,
        },
      },
    });
  } catch (error) {
    console.error("Get task stats error:", error);
    res.status(500).json({ error: "Failed to fetch task statistics" });
  }
};

// ===========================
// @desc    Get all tasks with advanced filtering
// @route   GET /api/tasks/admin/all
// @access  Private (Admin only)
// ===========================
export const getAllTasks = async (req, res) => {
  try {
    const {
      project,
      category,
      status,
      priority,
      complexity,
      assignedTo,
      overdue,
      isArchived,
      page = 1,
      limit = 20,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = req.query;

    // Build filter - allow viewing archived tasks if specified
    let filter = {};
    if (isArchived === "true") {
      filter.isArchived = true;
    } else if (isArchived === "false" || isArchived === undefined) {
      filter.isArchived = false;
    }
    // If isArchived === "all", don't add filter (show both)

    if (project && project !== "all") {
      filter.project = project;
    }

    if (category && category !== "all") {
      filter.category = category;
    }

    if (status && status !== "all") {
      filter.status = status;
    } else if (!status || status === "all") {
      // Exclude 'stored' and 'completed' tasks from active tasks list
      filter.status = { $nin: ["stored", "completed"] };
    }

    if (priority && priority !== "all") {
      filter.priority = priority;
    }

    if (complexity && complexity !== "all") {
      filter.complexity = complexity;
    }

    if (assignedTo && assignedTo !== "all") {
      filter.assignedTo = assignedTo;
    }

    if (overdue === "true") {
      filter.dueDate = { $lt: new Date() };
      filter.status = { $in: ["available", "in-progress", "needs-revision"] };
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Execute query
    const tasks = await Task.find(filter)
      .populate("assignedTo", "name email talentId")
      .populate("requestedBy", "name email talentId")
      .populate("createdBy", "name email")
      .populate("approvedBy", "name email")
      .sort(sort)
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .lean();

    // Calculate progress for each task
    const tasksWithProgress = tasks.map((task) => {
      let progress = 0;

      // Calculate based on checklist if available
      if (task.hasChecklist && task.checklist && task.checklist.length > 0) {
        const completedItems = task.checklist.filter(
          (item) => item.completed
        ).length;
        progress = Math.round((completedItems / task.checklist.length) * 100);
      }
      // Calculate based on sections if available
      else if (task.sections && task.sections.length > 0) {
        const completedSections = task.sections.filter(
          (s) => s.status === "completed"
        ).length;
        progress = Math.round((completedSections / task.sections.length) * 100);
      }

      return { ...task, progress };
    });

    const total = await Task.countDocuments(filter);

    res.json({
      success: true,
      tasks: tasksWithProgress,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error("Get all tasks error:", error);
    res.status(500).json({ error: "Failed to fetch tasks" });
  }
};

// ===========================
// BULK OPERATIONS
// ===========================

// ===========================
// @desc    Bulk archive tasks
// @route   POST /api/tasks/bulk/archive
// @access  Private (Admin only)
// ===========================
export const bulkArchiveTasks = async (req, res) => {
  try {
    const { taskIds } = req.body;

    if (!Array.isArray(taskIds) || taskIds.length === 0) {
      return res.status(400).json({ error: "Task IDs array required" });
    }

    const result = await Task.updateMany(
      { _id: { $in: taskIds } },
      { $set: { isArchived: true, status: "cancelled" } },
      { runValidators: false }
    );

    res.json({
      success: true,
      message: `${result.modifiedCount} tasks archived successfully`,
      modifiedCount: result.modifiedCount,
    });
  } catch (error) {
    console.error("Bulk archive error:", error);
    res.status(500).json({ error: "Failed to archive tasks" });
  }
};

// ===========================
// @desc    Bulk reassign tasks
// @route   POST /api/tasks/bulk/reassign
// @access  Private (Admin only)
// ===========================
export const bulkReassignTasks = async (req, res) => {
  try {
    const { taskIds, newTalentId } = req.body;

    if (!Array.isArray(taskIds) || taskIds.length === 0) {
      return res.status(400).json({ error: "Task IDs array required" });
    }

    if (!newTalentId) {
      return res.status(400).json({ error: "New talent ID required" });
    }

    // Validate new talent exists
    const newTalent = await Talent.findById(newTalentId);
    if (!newTalent) {
      return res.status(404).json({ error: "New talent not found" });
    }

    // Get tasks to check project match
    const tasks = await Task.find({ _id: { $in: taskIds } });

    // Check project mismatch
    const mismatchedTasks = tasks.filter(
      (task) =>
        task.category !== newTalent.assignedProject &&
        newTalent.email !== "<EMAIL>"
    );

    if (mismatchedTasks.length > 0) {
      return res.status(400).json({
        error: `${mismatchedTasks.length} tasks have different project category than talent`,
        mismatchedTaskIds: mismatchedTasks.map((t) => t._id),
      });
    }

    // Get old assignees for notifications
    const oldAssignees = [
      ...new Set(
        tasks.filter((t) => t.assignedTo).map((t) => t.assignedTo.toString())
      ),
    ];

    // Bulk reassign
    const result = await Task.updateMany(
      { _id: { $in: taskIds } },
      { $set: { assignedTo: newTalentId, startedAt: new Date() } }
    );

    // Send notifications
    const io = req.app.get("io");

    // Notify old assignees
    for (const oldAssigneeId of oldAssignees) {
      if (oldAssigneeId !== newTalentId) {
        await createNotification(
          {
            recipient: oldAssigneeId,
            type: "admin_message",
            title: "Tasks Reassigned",
            message: `${result.modifiedCount} of your tasks have been reassigned to another talent.`,
            priority: "medium",
          },
          io
        );
      }
    }

    // Notify new talent
    await createNotification(
      {
        recipient: newTalentId,
        type: "task_assigned",
        title: "Multiple Tasks Assigned",
        message: `You've been assigned ${result.modifiedCount} tasks.`,
        priority: "high",
        actionUrl: "/tasks/my-queue",
      },
      io
    );

    res.json({
      success: true,
      message: `${result.modifiedCount} tasks reassigned successfully`,
      modifiedCount: result.modifiedCount,
    });
  } catch (error) {
    console.error("Bulk reassign error:", error);
    res.status(500).json({ error: "Failed to reassign tasks" });
  }
};

// ===========================
// @desc    Bulk permanently delete tasks
// @route   POST /api/tasks/bulk/delete
// @access  Private (Admin only)
// ===========================
export const bulkDeleteTasks = async (req, res) => {
  try {
    const { taskIds } = req.body;

    if (!Array.isArray(taskIds) || taskIds.length === 0) {
      return res.status(400).json({ error: "Task IDs array required" });
    }

    const result = await Task.deleteMany({ _id: { $in: taskIds } });

    res.json({
      success: true,
      message: `${result.deletedCount} tasks permanently deleted`,
      deletedCount: result.deletedCount,
    });
  } catch (error) {
    console.error("Bulk delete error:", error);
    res.status(500).json({ error: "Failed to delete tasks" });
  }
};

// ===========================
// @desc    Bulk restore archived tasks
// @route   POST /api/tasks/bulk/restore
// @access  Private (Admin only)
// ===========================
export const bulkRestoreTasks = async (req, res) => {
  try {
    const { taskIds } = req.body;

    if (!Array.isArray(taskIds) || taskIds.length === 0) {
      return res.status(400).json({ error: "Task IDs array required" });
    }

    const result = await Task.updateMany(
      { _id: { $in: taskIds } },
      { $set: { isArchived: false, status: "available" } }
    );

    res.json({
      success: true,
      message: `${result.modifiedCount} tasks restored successfully`,
      modifiedCount: result.modifiedCount,
    });
  } catch (error) {
    console.error("Bulk restore error:", error);
    res.status(500).json({ error: "Failed to restore tasks" });
  }
};

// ===========================
// @desc    Get talent-specific task statistics
// @route   GET /api/tasks/stats
// @access  Private (Talent)
// ===========================
export const getTalentStats = async (req, res) => {
  try {
    const talentId = req.user.userId;
    const now = new Date();

    // Available tasks (filtered by project)
    let availableFilter = { status: "available", isArchived: false };
    if (!hasFullAccess(req.user)) {
      availableFilter.category = req.user.assignedProject;
    }
    const available = await Task.countDocuments(availableFilter);

    // My queue tasks
    const myQueueFilter = {
      assignedTo: talentId,
      status: { $in: ["in-progress", "needs-revision"] },
      isArchived: false,
    };
    const myQueueTasks = await Task.find(myQueueFilter).lean();

    const inProgress = myQueueTasks.filter(
      (t) => t.status === "in-progress"
    ).length;
    const needsRevision = myQueueTasks.filter(
      (t) => t.status === "needs-revision"
    ).length;

    // Completed tasks
    const completed = await Task.countDocuments({
      assignedTo: talentId,
      status: "completed",
      isArchived: false,
    });

    // Deadline-based counts
    const overdue = myQueueTasks.filter(
      (t) => t.dueDate && new Date(t.dueDate) < now
    ).length;

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dueToday = myQueueTasks.filter((t) => {
      if (!t.dueDate) return false;
      const dueDate = new Date(t.dueDate);
      return dueDate >= today && dueDate < tomorrow;
    }).length;

    const weekFromNow = new Date(now);
    weekFromNow.setDate(weekFromNow.getDate() + 7);
    const dueThisWeek = myQueueTasks.filter((t) => {
      if (!t.dueDate) return false;
      const dueDate = new Date(t.dueDate);
      return dueDate >= now && dueDate <= weekFromNow;
    }).length;

    res.json({
      success: true,
      stats: {
        available,
        inProgress,
        completed,
        needsRevision,
        overdue,
        dueToday,
        dueThisWeek,
      },
    });
  } catch (error) {
    console.error("Get talent stats error:", error);
    res.status(500).json({ error: "Failed to fetch statistics" });
  }
};

// ===========================
// @desc    Get employee task summary (for By Employee view)
// @route   GET /api/tasks/admin/employee-summary
// @access  Private (Admin only)
// ===========================
export const getEmployeeTaskSummary = async (req, res) => {
  try {
    // Get all active talents
    const talents = await Talent.find({
      status: "active",
    })
      .select("_id name email talentId")
      .lean();

    // Get task counts for each talent
    const employeeSummary = await Promise.all(
      talents.map(async (talent) => {
        const tasks = await Task.find({
          assignedTo: talent._id,
          isArchived: false,
        }).lean();

        const totalTasks = tasks.length;
        const inProgress = tasks.filter(
          (t) => t.status === "in-progress"
        ).length;
        const needsReview = tasks.filter(
          (t) => t.status === "pending-review"
        ).length;
        const needsRevision = tasks.filter(
          (t) => t.status === "needs-revision"
        ).length;

        const now = new Date();
        const overdue = tasks.filter((t) => {
          if (!t.dueDate || ["completed", "cancelled"].includes(t.status))
            return false;
          return new Date(t.dueDate) < now;
        }).length;

        // Calculate approaching deadline (within 2 days)
        const twoDaysFromNow = new Date(now);
        twoDaysFromNow.setDate(twoDaysFromNow.getDate() + 2);
        const approachingDeadline = tasks.filter((t) => {
          if (!t.dueDate || ["completed", "cancelled"].includes(t.status))
            return false;
          const dueDate = new Date(t.dueDate);
          return dueDate >= now && dueDate <= twoDaysFromNow;
        }).length;

        return {
          talentId: talent._id,
          talentIdString: talent.talentId,
          name: talent.name,
          email: talent.email,
          totalTasks,
          inProgress,
          needsReview,
          needsRevision,
          overdue,
          approachingDeadline,
          status:
            overdue > 0
              ? "critical"
              : approachingDeadline > 0
              ? "warning"
              : "good",
        };
      })
    );

    // Sort by total tasks descending
    employeeSummary.sort((a, b) => b.totalTasks - a.totalTasks);

    res.json({
      success: true,
      employees: employeeSummary,
      totalEmployees: employeeSummary.length,
    });
  } catch (error) {
    console.error("Get employee summary error:", error);
    res.status(500).json({ error: "Failed to fetch employee summary" });
  }
};

// ===========================
// @desc    Get tasks for specific employee with stats
// @route   GET /api/tasks/admin/by-employee/:talentId
// @access  Private (Admin only)
// ===========================
export const getTasksByEmployee = async (req, res) => {
  try {
    const { talentId } = req.params;
    const {
      status,
      priority,
      complexity,
      page = 1,
      limit = 20,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = req.query;

    // Verify talent exists
    const talent = await Talent.findById(talentId).select(
      "name email talentId"
    );
    if (!talent) {
      return res.status(404).json({ error: "Talent not found" });
    }

    // Build filter
    let filter = {
      assignedTo: talentId,
      isArchived: false,
    };

    if (status && status !== "all") {
      filter.status = status;
    }

    if (priority && priority !== "all") {
      filter.priority = priority;
    }

    if (complexity && complexity !== "all") {
      filter.complexity = complexity;
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Get tasks
    const tasks = await Task.find(filter)
      .populate("createdBy", "name email")
      .populate("approvedBy", "name email")
      .sort(sort)
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .lean();

    const total = await Task.countDocuments(filter);

    // Calculate stats for this employee
    const allTasks = await Task.find({
      assignedTo: talentId,
      isArchived: false,
    }).lean();

    const stats = {
      totalTasks: allTasks.length,
      byStatus: {
        inProgress: allTasks.filter((t) => t.status === "in-progress").length,
        needsReview: allTasks.filter((t) => t.status === "pending-review")
          .length,
        needsRevision: allTasks.filter((t) => t.status === "needs-revision")
          .length,
      },
      overdue: allTasks.filter((t) => {
        if (!t.dueDate || ["completed", "cancelled"].includes(t.status))
          return false;
        return new Date(t.dueDate) < new Date();
      }).length,
    };

    // Calculate completed tasks in last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const completedLast30Days = await Task.countDocuments({
      assignedTo: talentId,
      status: "completed",
      completedAt: { $gte: thirtyDaysAgo },
    });

    res.json({
      success: true,
      talent: {
        id: talent._id,
        name: talent.name,
        email: talent.email,
        talentId: talent.talentId,
      },
      tasks,
      stats: {
        ...stats,
        completedLast30Days,
      },
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error("Get tasks by employee error:", error);
    res.status(500).json({ error: "Failed to fetch employee tasks" });
  }
};

// ===========================
// GET TASKS NEEDING ADMIN ACTION
// ===========================
// @route   GET /api/tasks/admin/needs-action
// @desc    Get all tasks that need admin attention (in-review, needs-revision resubmitted, overdue)
// @access  Admin only
export const getTasksNeedingAction = async (req, res) => {
  try {
    const { filter: filterType = "all" } = req.query;

    const now = new Date();

    // Build queries for different action types
    const queries = {
      // Tasks with pending assignment requests from talents
      assignmentRequests: {
        status: "pending-approval", // Status set when talent requests task
        requestedBy: { $exists: true, $ne: null },
        assignedTo: null, // Not yet assigned
        isArchived: false,
      },

      // Tasks submitted for review (includes section-based tasks with pending sections)
      inReview: {
        $or: [
          { status: "in-review", isArchived: false },
          {
            "sections.status": "pending-review",
            status: "in-progress",
            isArchived: false,
          },
        ],
      },

      // Tasks that were sent back for revision and have been updated since
      needsRevisionResubmitted: {
        status: "needs-revision",
        isArchived: false,
        // Check if task was updated after the last review comment/action
        $expr: {
          $gt: ["$updatedAt", "$reviewedAt"], // Task updated after last review
        },
      },

      // Overdue tasks that are still active
      overdue: {
        dueDate: { $lt: now },
        status: { $in: ["available", "in-progress", "needs-revision"] },
        isArchived: false,
      },
    };

    let tasks = [];

    if (filterType === "all" || filterType === "assignment-requests") {
      const assignmentRequestTasks = await Task.find(queries.assignmentRequests)
        .populate("requestedBy", "name email talentId")
        .populate("createdBy", "name email")
        .sort({ updatedAt: -1 });

      console.log(
        `[DEBUG] Found ${assignmentRequestTasks.length} assignment requests`
      );
      if (assignmentRequestTasks.length > 0) {
        console.log("[DEBUG] First request:", {
          id: assignmentRequestTasks[0]._id,
          title: assignmentRequestTasks[0].title,
          requestedBy: assignmentRequestTasks[0].requestedBy?.name,
          approvedBy: assignmentRequestTasks[0].approvedBy,
          assignedTo: assignmentRequestTasks[0].assignedTo,
          status: assignmentRequestTasks[0].status,
        });
      }

      tasks.push(
        ...assignmentRequestTasks.map((task) => ({
          ...task.toObject(),
          actionType: "assignment-requests",
        }))
      );
    }

    if (filterType === "all" || filterType === "in-review") {
      const inReviewTasks = await Task.find(queries.inReview)
        .populate("assignedTo", "name email talentId")
        .populate("createdBy", "name email")
        .sort({ updatedAt: -1 });
      tasks.push(
        ...inReviewTasks.map((task) => ({
          ...task.toObject(),
          actionType: "in-review",
        }))
      );
    }

    if (filterType === "all" || filterType === "needs-revision-resubmitted") {
      const resubmittedTasks = await Task.find(queries.needsRevisionResubmitted)
        .populate("assignedTo", "name email talentId")
        .populate("createdBy", "name email")
        .sort({ updatedAt: -1 });
      tasks.push(
        ...resubmittedTasks.map((task) => ({
          ...task.toObject(),
          actionType: "needs-revision-resubmitted",
        }))
      );
    }

    if (filterType === "all" || filterType === "overdue") {
      const overdueTasks = await Task.find(queries.overdue)
        .populate("assignedTo", "name email talentId")
        .populate("createdBy", "name email")
        .sort({ dueDate: 1 }); // Oldest overdue first
      tasks.push(
        ...overdueTasks.map((task) => ({
          ...task.toObject(),
          actionType: "overdue",
        }))
      );
    }

    // Calculate counts
    const counts = {
      total: tasks.length,
      assignmentRequests: tasks.filter(
        (t) => t.actionType === "assignment-requests"
      ).length,
      inReview: tasks.filter((t) => t.actionType === "in-review").length,
      needsRevisionResubmitted: tasks.filter(
        (t) => t.actionType === "needs-revision-resubmitted"
      ).length,
      overdue: tasks.filter((t) => t.actionType === "overdue").length,
    };

    res.json({
      success: true,
      tasks,
      counts,
    });
  } catch (error) {
    console.error("Get tasks needing action error:", error);
    res.status(500).json({ error: "Failed to fetch tasks needing action" });
  }
};

// ===========================
// @desc    Download task attachment
// @route   GET /api/tasks/:taskId/attachment/:attachmentId
// @access  Private (Talent + Admin)
// ===========================
export const downloadTaskAttachment = async (req, res) => {
  try {
    const { taskId, attachmentId } = req.params;

    console.log("📎 Download attachment request:", {
      taskId,
      attachmentId,
      userId: req.user.userId,
      userType: req.user.userType,
    });

    // Find task
    const task = await Task.findById(taskId);
    if (!task) {
      console.log("❌ Task not found");
      return res.status(404).json({ error: "Task not found" });
    }

    console.log("📋 Task found:", {
      taskId: task._id,
      status: task.status,
      assignedTo: task.assignedTo,
      attachmentsCount: task.attachments?.length || 0,
    });

    // Check access - admin can access all, talent can access assigned tasks OR available tasks
    const isAdmin = req.user.userType === "admin";
    const isAssignedToUser = task.assignedTo?.toString() === req.user.userId;
    const isAvailable = task.status === "available";

    if (!isAdmin && !isAssignedToUser && !isAvailable) {
      console.log("❌ Access denied:", {
        isAdmin,
        isAssignedToUser,
        isAvailable,
      });
      return res.status(403).json({ error: "Access denied" });
    }

    console.log("✅ Access granted:", {
      isAdmin,
      isAssignedToUser,
      isAvailable,
    });

    // Find attachment
    const attachment = task.attachments.find(
      (att) => att._id.toString() === attachmentId
    );

    if (!attachment) {
      return res.status(404).json({ error: "Attachment not found" });
    }

    // Fetch file from Cloudinary
    const fetch = (await import("node-fetch")).default;
    const response = await fetch(attachment.url);

    if (!response.ok) {
      throw new Error(
        `Failed to fetch file from Cloudinary: ${response.status}`
      );
    }

    // Get the file buffer
    const buffer = await response.buffer();

    // Determine content type
    const mimeTypes = {
      pdf: "application/pdf",
      png: "image/png",
      jpg: "image/jpeg",
      jpeg: "image/jpeg",
      gif: "image/gif",
      webp: "image/webp",
    };
    const contentType =
      mimeTypes[attachment.fileType] || "application/octet-stream";

    // Set headers for inline viewing
    res.set({
      "Content-Type": contentType,
      "Content-Disposition": `inline; filename="${attachment.filename}"`,
      "Content-Length": buffer.length,
      "Cache-Control": "private, max-age=3600",
      "X-Content-Type-Options": "nosniff",
    });

    // Send the file
    res.send(buffer);
  } catch (error) {
    console.error("Download task attachment error:", error);
    res.status(500).json({ error: "Failed to download attachment" });
  }
};

// ===========================
// @desc    Activate a stored task
// @route   PUT /api/tasks/:id/activate
// @access  Private (Admin)
// ===========================
export const activateStoredTask = async (req, res) => {
  try {
    const { id } = req.params;

    const task = await Task.findById(id);

    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Verify task is in stored status
    if (task.status !== "stored") {
      return res.status(400).json({
        error: `Cannot activate task with status: ${task.status}. Only stored tasks can be activated.`,
      });
    }

    // Update status to available
    task.status = "available";
    await task.save();

    console.log(`✅ Activated stored task: ${task.title} (ID: ${task._id})`);

    res.json({
      success: true,
      message: "Task activated successfully",
      task,
    });
  } catch (error) {
    console.error("Activate stored task error:", error);
    res.status(500).json({ error: "Failed to activate task" });
  }
};

// ===========================
// @desc    Admin request revision on individual checklist item
// @route   POST /api/tasks/:taskId/checklist/:itemNumber/revision
// @access  Private (Admin only)
// ===========================
export const requestChecklistItemRevision = async (req, res) => {
  try {
    const { taskId, itemNumber } = req.params;

    // Support both old format (comment) and new format (issues)
    const { comment, images = [], feedback, issues: issuesJson } = req.body;

    // Parse issues if provided (new format)
    let issues = [];
    if (issuesJson) {
      try {
        issues = JSON.parse(issuesJson);
      } catch (e) {
        return res.status(400).json({ error: "Invalid issues format" });
      }
    }

    // Validate: either comment (old) or issues (new) must be provided
    if (!comment && (!issues || issues.length === 0)) {
      return res
        .status(400)
        .json({ error: "Either comment or at least one issue is required" });
    }

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    if (task.taskType !== "checklist-based") {
      return res
        .status(400)
        .json({ error: "This endpoint only works for checklist-based tasks" });
    }

    const checklistItem = task.checklist.find(
      (item) => item.number === parseInt(itemNumber)
    );

    if (!checklistItem) {
      return res.status(404).json({ error: "Checklist item not found" });
    }

    // Handle screenshot uploads (new format with issues)
    if (issues && issues.length > 0 && req.files) {
      for (let i = 0; i < issues.length; i++) {
        const fileKey = `issue_${i}_screenshot`;
        const file = req.files[fileKey];

        if (file && file[0]) {
          // Upload to Cloudinary
          const result = await new Promise((resolve, reject) => {
            const uploadStream = cloudinary.uploader.upload_stream(
              {
                folder: `tasks/${taskId}/checklist_revisions`,
                resource_type: "auto",
              },
              (error, result) => {
                if (error) reject(error);
                else resolve(result);
              }
            );
            uploadStream.end(file[0].buffer);
          });

          issues[i].screenshot = {
            url: result.secure_url,
            publicId: result.public_id,
          };
        }
      }
    }

    // Store issues in checklist item (new format)
    if (issues && issues.length > 0) {
      checklistItem.issues = issues;
      checklistItem.feedback = feedback || "";
    } else {
      // Old format fallback - store as single issue
      checklistItem.issues = [
        {
          description: comment,
          priority: "medium",
          screenshot: images.length > 0 ? { url: images[0] } : undefined,
        },
      ];
    }

    // Update checklist item status
    checklistItem.status = "needs-revision";
    checklistItem.needsRevision = true;

    // Clear old revisionHistory to prevent displaying old format
    // The new issues format should be the single source of truth
    checklistItem.revisionHistory = [];

    await task.save();

    // Debug: Log what was saved
    console.log("🔍 Checklist Item After Save:", {
      number: checklistItem.number,
      needsRevision: checklistItem.needsRevision,
      status: checklistItem.status,
      issuesCount: checklistItem.issues?.length,
      issues: checklistItem.issues,
    });

    // Send notification to talent
    if (task.assignedTo) {
      await createNotification(
        {
          recipient: task.assignedTo,
          type: "task_revision_requested",
          title: "Revision Requested on Checklist Item",
          message: `Admin requested revision on "${checklistItem.title}" in task "${task.title}"`,
          actionUrl: `/tasks/${task._id}`,
        },
        req.app.get("io")
      );
    }

    console.log(
      `✅ Admin requested revision on checklist item #${itemNumber} for task: ${task.title}`
    );

    res.json({
      success: true,
      message: "Revision requested successfully",
      checklistItem,
    });
  } catch (error) {
    console.error("Request checklist item revision error:", error);
    res.status(500).json({ error: "Failed to request revision" });
  }
};

// ===========================
// @desc    Talent respond to checklist item revision
// @route   POST /api/tasks/:taskId/checklist/:itemNumber/respond
// @access  Private (Talent only)
// ===========================
export const respondToChecklistItemRevision = async (req, res) => {
  try {
    const { taskId, itemNumber } = req.params;
    const { comment, images = [] } = req.body;

    if (!comment || comment.trim() === "") {
      return res
        .status(400)
        .json({ error: "Comment is required for response" });
    }

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Verify task is assigned to this talent
    if (task.assignedTo.toString() !== req.user.userId) {
      return res
        .status(403)
        .json({ error: "You are not assigned to this task" });
    }

    if (task.taskType !== "checklist-based") {
      return res
        .status(400)
        .json({ error: "This endpoint only works for checklist-based tasks" });
    }

    const checklistItem = task.checklist.find(
      (item) => item.number === parseInt(itemNumber)
    );

    if (!checklistItem) {
      return res.status(404).json({ error: "Checklist item not found" });
    }

    // Add response entry to history
    checklistItem.revisionHistory.push({
      type: "talent_submission",
      comment: comment.trim(),
      images: images,
      createdBy: req.user.userId,
      createdByModel: "Talent",
      createdAt: new Date(),
    });

    // Update checklist item status back to submitted
    checklistItem.status = "submitted";

    await task.save();

    console.log(
      `✅ Talent responded to revision on checklist item #${itemNumber} for task: ${task.title}`
    );

    res.json({
      success: true,
      message: "Response submitted successfully",
      checklistItem,
    });
  } catch (error) {
    console.error("Respond to checklist item revision error:", error);
    res.status(500).json({ error: "Failed to submit response" });
  }
};

// ===========================
// @desc    Upload image for checklist item revision
// @route   POST /api/tasks/upload-revision-image
// @access  Private (Admin & Talent)
// ===========================
export const uploadRevisionImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No image file provided" });
    }

    // Upload to Cloudinary
    const result = await new Promise((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder: "task-revisions",
          resource_type: "image",
          quality: "auto:best",
        },
        (error, result) => {
          if (error) reject(error);
          else resolve(result);
        }
      );
      uploadStream.end(req.file.buffer);
    });

    res.json({
      success: true,
      image: {
        url: result.secure_url,
        publicId: result.public_id,
        filename: req.file.originalname,
      },
    });
  } catch (error) {
    console.error("Upload revision image error:", error);
    res.status(500).json({ error: "Failed to upload image" });
  }
};

// ===========================
// @desc    Upload annotated image and link to original screenshot
// @route   POST /api/tasks/:taskId/annotate-screenshot
// @access  Private (Admin only)
// ===========================
export const uploadAnnotatedScreenshot = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { screenshotIndex, sectionNumber, checklistItemNumber, itemType } =
      req.body;

    if (!req.file) {
      return res.status(400).json({ error: "No image file provided" });
    }

    // Validate admin access
    if (req.user.userType !== "admin") {
      return res.status(403).json({ error: "Admin access required" });
    }

    // Find task
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Upload to Cloudinary
    const result = await new Promise((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder: `tasks/${taskId}/annotated`,
          resource_type: "auto",
        },
        (error, result) => {
          if (error) reject(error);
          else resolve(result);
        }
      );
      uploadStream.end(req.file.buffer);
    });

    // Update the appropriate screenshot based on item type
    let updated = false;

    if (itemType === "section" && sectionNumber !== undefined) {
      // Update section screenshot (both in main screenshots and submission history)
      const section = task.sections[sectionNumber - 1];
      if (
        section &&
        section.screenshots &&
        section.screenshots[screenshotIndex]
      ) {
        // Update main screenshots array
        if (typeof section.screenshots[screenshotIndex] === "string") {
          // Convert string to object if needed
          section.screenshots[screenshotIndex] = {
            url: section.screenshots[screenshotIndex],
            annotatedUrl: result.secure_url,
            annotatedPublicId: result.public_id,
            annotatedBy: req.user.userId,
            annotatedAt: new Date(),
          };
        } else {
          section.screenshots[screenshotIndex].annotatedUrl = result.secure_url;
          section.screenshots[screenshotIndex].annotatedPublicId =
            result.public_id;
          section.screenshots[screenshotIndex].annotatedBy = req.user.userId;
          section.screenshots[screenshotIndex].annotatedAt = new Date();
        }
        updated = true;
      }
    } else if (itemType === "checklist" && checklistItemNumber !== undefined) {
      // Update checklist item screenshot
      const checklistItem = task.checklist[checklistItemNumber - 1];
      if (
        checklistItem &&
        checklistItem.screenshots &&
        checklistItem.screenshots[screenshotIndex]
      ) {
        checklistItem.screenshots[screenshotIndex].annotatedUrl =
          result.secure_url;
        checklistItem.screenshots[screenshotIndex].annotatedPublicId =
          result.public_id;
        checklistItem.screenshots[screenshotIndex].annotatedBy =
          req.user.userId;
        checklistItem.screenshots[screenshotIndex].annotatedAt = new Date();
        updated = true;
      }
    }

    if (!updated) {
      return res
        .status(400)
        .json({ error: "Screenshot not found at specified location" });
    }

    await task.save();

    res.json({
      success: true,
      annotatedImage: {
        url: result.secure_url,
        publicId: result.public_id,
      },
    });
  } catch (error) {
    console.error("Upload annotated screenshot error:", error);
    res.status(500).json({ error: "Failed to upload annotated image" });
  }
};
