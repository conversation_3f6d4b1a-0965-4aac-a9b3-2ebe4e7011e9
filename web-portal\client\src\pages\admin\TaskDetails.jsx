import React, { useState, useEffect } from "react";
import { useN<PERSON><PERSON>, useParams } from "react-router-dom";
import AdminLayout from "../../components/admin/AdminLayout";
import { useAdminTaskStore } from "../../store/adminTaskStore";
import { useAuthStore } from "../../store/authStore";
import AssignTaskModal from "../../components/tasks/AssignTaskModal";
import RejectSectionModal from "../../components/tasks/RejectSectionModal";
import RejectChecklistModal from "../../components/tasks/RejectChecklistModal";
import ImageAnnotationModal from "../../components/tasks/ImageAnnotationModal";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import {
  <PERSON><PERSON>rrow<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>alendar,
  HiCheckCircle,
  HiXCircle,
  HiPencil,
  HiTrash,
  HiUserAdd,
  HiChat,
  HiDocumentText,
  HiClipboardList,
  HiPhotograph,
  HiDownload,
  HiChatAlt2,
  HiStar,
  HiExclamation,
} from "react-icons/hi";
import { X, Upload } from "lucide-react";
import ConfirmationModal from "../../components/ui/ConfirmationModal";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";

export default function TaskDetails() {
  const navigate = useNavigate();
  const { id } = useParams();
  const { accessToken } = useAuthStore();
  const {
    fetchTaskById,
    loading,
    fetchTalents,
    assignTask,
    talents,
    talentsLoading,
    deleteTask,
    submitLoading,
    approveSection,
    rejectSection,
    adminCompleteTask,
  } = useAdminTaskStore();

  const [task, setTask] = useState(null);
  const [activeTab, setActiveTab] = useState("details");
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [showSectionRejectModal, setShowSectionRejectModal] = useState(false);
  const [selectedSectionNum, setSelectedSectionNum] = useState(null);
  const [showChecklistItemRejectModal, setShowChecklistItemRejectModal] =
    useState(false);
  const [selectedChecklistItem, setSelectedChecklistItem] = useState(null);
  const [showChecklistRejectModal, setShowChecklistRejectModal] =
    useState(false);
  const [checklistRevisionItems, setChecklistRevisionItems] = useState([]);

  // State for revision dialog
  const [revisionDialog, setRevisionDialog] = useState({
    isOpen: false,
    itemNumber: null,
    itemTitle: "",
  });

  // State for inline revision forms for each checklist item
  const [itemRevisionData, setItemRevisionData] = useState({});
  // Structure: { [itemId]: { description: "", priority: "medium", screenshot: null, screenshotPreview: null } }

  // State for collapsible checklist descriptions
  const [expandedItems, setExpandedItems] = useState({});

  // State for checklist item revision history expansion
  const [expandedRevisionHistory, setExpandedRevisionHistory] = useState({});

  // State for showing revision form per checklist item
  const [showRevisionForm, setShowRevisionForm] = useState({});

  // State for revision form data per item
  const [revisionFormData, setRevisionFormData] = useState({});
  // Structure: { [itemNumber]: { comment: "", images: [], imagePreviews: [] } }

  // State for submitting revision
  const [submittingRevision, setSubmittingRevision] = useState({});

  // State for image annotation modal
  const [annotationModal, setAnnotationModal] = useState({
    isOpen: false,
    imageUrl: null,
    screenshotIndex: null,
    sectionNumber: null,
    checklistItemNumber: null,
    itemType: null, // "section" or "checklist"
    fileName: null,
  });

  // State for image preview modal
  const [imageModal, setImageModal] = useState({
    isOpen: false,
    images: [],
    currentIndex: 0,
  });

  // Fetch task on mount
  useEffect(() => {
    if (accessToken && id) {
      loadTask();
    }
  }, [accessToken, id]);

  const handleOpenAssignModal = async () => {
    if (talents.length === 0) {
      await fetchTalents(accessToken);
    }
    setShowAssignModal(true);
  };

  const handleAssignTask = async (taskId, talentId) => {
    try {
      await assignTask(taskId, talentId);
      toast.success(
        task.assignedTo
          ? "Task reassigned successfully"
          : "Task assigned successfully"
      );
      await loadTask(); // Refresh task data
    } catch (error) {
      toast.error(error.message || "Failed to assign task");
      throw error;
    }
  };

  const handleDeleteTask = async () => {
    try {
      await deleteTask(task._id, false); // Soft delete
      toast.success("Task deleted successfully!");
      setIsDeleteModalOpen(false);
      navigate("/admin/tasks/all");
    } catch (error) {
      console.error("Delete task error:", error);
      toast.error("Failed to delete task");
    }
  };

  const handleApproveSection = async (sectionNum) => {
    try {
      await approveSection(task._id, sectionNum);
      toast.success(`Section ${sectionNum} approved!`);
      await loadTask();
    } catch (error) {
      toast.error(error.message || "Failed to approve section");
    }
  };

  const handleRejectSection = async (revisionData) => {
    try {
      await rejectSection(task._id, selectedSectionNum, revisionData);
      const issueCount = revisionData.issues.length;
      toast.success(
        `Section ${selectedSectionNum} rejected with ${issueCount} issue${
          issueCount > 1 ? "s" : ""
        }`
      );
      setShowSectionRejectModal(false);
      setSelectedSectionNum(null);
      await loadTask();
    } catch (error) {
      toast.error(error.message || "Failed to reject section");
    }
  };

  const handleRequestItemRevision = async (itemNumber, revisionData) => {
    try {
      // Create FormData for multipart request
      const formData = new FormData();

      // Add feedback if provided
      if (revisionData.feedback) {
        formData.append("revisionNote", revisionData.feedback);
      }

      // Prepare issues without screenshot files for JSON
      const issuesForJson = revisionData.issues.map((issue) => ({
        description: issue.description,
        priority: issue.priority,
      }));

      // Add issues as JSON string
      formData.append("issues", JSON.stringify(issuesForJson));

      // Add screenshot files
      revisionData.issues.forEach((issue, index) => {
        if (issue.screenshot) {
          formData.append(`issue_${index}_screenshot`, issue.screenshot);
        }
      });

      const token = useAuthStore.getState().accessToken;
      const response = await fetch(
        `http://localhost:5000/api/tasks/${task._id}/checklist/${itemNumber}/revision`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
          credentials: "include",
          body: formData,
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to request revision");
      }

      const result = await response.json();
      console.log("🔍 Backend Response:", result);

      const issueCount = revisionData.issues.length;
      toast.success(
        `Checklist item ${itemNumber} revision requested with ${issueCount} issue${
          issueCount > 1 ? "s" : ""
        }`
      );
      setShowChecklistItemRejectModal(false);
      setSelectedChecklistItem(null);
      await loadTask();

      // Log the refreshed task data
      console.log("🔍 Task After Reload:", task);
    } catch (error) {
      toast.error(error.message || "Failed to request revision");
    }
  };

  // Image annotation handlers
  const handleOpenAnnotation = (
    imageUrl,
    screenshotIndex,
    sectionNumber,
    checklistItemNumber,
    itemType
  ) => {
    setAnnotationModal({
      isOpen: true,
      imageUrl,
      screenshotIndex,
      sectionNumber,
      checklistItemNumber,
      itemType,
      fileName: `task-${task._id}-${itemType}-${
        sectionNumber || checklistItemNumber
      }-screenshot-${screenshotIndex}`,
    });
  };

  const handleSaveAnnotation = async (annotatedBlob) => {
    try {
      // When annotating from revision modal, we don't save to server yet
      // Instead, we pass it back to the modal to attach to an issue
      // The revision modal will handle the upload when submitting

      // Close annotation editor
      setAnnotationModal({
        isOpen: false,
        imageUrl: null,
        screenshotIndex: null,
        sectionNumber: null,
        checklistItemNumber: null,
        itemType: null,
        fileName: null,
      });

      // Create a File object from the blob for the modal
      const annotatedFile = new File(
        [annotatedBlob],
        `annotated-${Date.now()}.png`,
        { type: "image/png" }
      );

      // Create preview URL
      const previewUrl = URL.createObjectURL(annotatedBlob);

      // Show success message
      toast.success(
        "✅ Annotated image ready! It will be attached to your revision feedback.",
        { duration: 3000 }
      );

      // Store the annotated image to be added to the next issue
      // This will be picked up by the revision modal
      window.annotatedImageForRevision = {
        file: annotatedFile,
        preview: previewUrl,
        screenshotIndex: annotationModal.screenshotIndex,
      };

      // Dispatch custom event to notify the modal
      window.dispatchEvent(
        new CustomEvent("annotatedImageReady", {
          detail: { file: annotatedFile, preview: previewUrl },
        })
      );
    } catch (error) {
      console.error("Save annotation error:", error);
      toast.error(error.message || "Failed to prepare annotation");
    }
  };

  const handleAdminComplete = async () => {
    // For section-based tasks, check if all sections are completed
    const allSectionsCompleted =
      task.taskType === "section-based" &&
      task.sections?.length > 0 &&
      task.sections.every((s) => s.status === "completed");

    // Allow completion if:
    // 1. Task is in pending-review/in-review status, OR
    // 2. All sections are completed (for section-based tasks)
    if (
      task.status !== "pending-review" &&
      task.status !== "in-review" &&
      !allSectionsCompleted
    ) {
      toast.error(`Cannot complete task with status: ${task.status}`);
      await loadTask(); // Refresh to get latest state
      return;
    }

    try {
      // If all sections are completed but status isn't pending-review, update it first
      if (allSectionsCompleted && task.status !== "pending-review") {
        // The backend will handle this, but we'll proceed anyway
      }

      await adminCompleteTask(task._id);
      toast.success("Task marked as completed!");
      await loadTask();
    } catch (error) {
      toast.error(error.message || "Failed to complete task");
      await loadTask(); // Refresh task state even on error
    }
  };

  const handleRejectChecklist = () => {
    // Initialize revision items for all completed checklist items
    const initialRevisionItems = task.checklist
      .filter((item) => item.completed)
      .map((item) => ({
        itemNumber: item.number,
        itemTitle: item.title,
        selected: false,
        issues: [],
      }));
    setChecklistRevisionItems(initialRevisionItems);
    setShowChecklistRejectModal(true);
  };

  const handleSubmitChecklistRejection = async (revisionData) => {
    try {
      const response = await fetch(
        `http://localhost:5000/api/tasks/${task._id}/reject-checklist`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
          body: JSON.stringify(revisionData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to reject task");
      }

      toast.success("Task sent back for revision");
      setShowChecklistRejectModal(false);
      await loadTask();
    } catch (error) {
      toast.error(error.message || "Failed to reject task");
      throw error;
    }
  };

  const loadTask = async () => {
    try {
      const fetchedTask = await fetchTaskById(accessToken, id);
      console.log("🔍 Fetched Task Data:", fetchedTask);
      console.log("🔍 Checklist Items:", fetchedTask.checklist);
      setTask(fetchedTask);
    } catch (error) {
      console.error("Failed to fetch task:", error);
      toast.error("Failed to load task details");
    }
  };

  // Handle inline revision for a specific checklist item
  const handleInlineRevision = async (itemNumber) => {
    const itemData = itemRevisionData[itemNumber];

    if (!itemData || !itemData.description || !itemData.description.trim()) {
      toast.error("Please enter an issue description");
      return;
    }

    try {
      // Upload screenshot to backend first if present
      let uploadedImage = null;
      if (itemData.screenshot) {
        const uploadFormData = new FormData();
        uploadFormData.append("image", itemData.screenshot);

        const uploadResponse = await fetch(
          `http://localhost:5000/api/tasks/upload-revision-image`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
            credentials: "include",
            body: uploadFormData,
          }
        );

        if (uploadResponse.ok) {
          const uploadData = await uploadResponse.json();
          uploadedImage = uploadData.image;
        }
      }

      // Send revision request to new API endpoint
      const response = await fetch(
        `http://localhost:5000/api/tasks/${task._id}/checklist/${itemNumber}/revision`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
          body: JSON.stringify({
            comment: itemData.description.trim(),
            images: uploadedImage ? [uploadedImage] : [],
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to request revision");
      }

      toast.success("Revision requested for this item");

      // Clear data
      setItemRevisionData((prev) => ({
        ...prev,
        [itemNumber]: {
          description: "",
          priority: "medium",
          screenshot: null,
          screenshotPreview: null,
        },
      }));

      await loadTask();
    } catch (error) {
      toast.error(error.message || "Failed to request revision");
    }
  };

  // Update revision data for a specific item
  const updateItemRevisionData = (itemNumber, field, value) => {
    setItemRevisionData((prev) => ({
      ...prev,
      [itemNumber]: {
        ...(prev[itemNumber] || {
          description: "",
          priority: "medium",
          screenshot: null,
          screenshotPreview: null,
        }),
        [field]: value,
      },
    }));
  };

  // Handle screenshot upload for revision
  const handleRevisionScreenshot = (itemNumber, file) => {
    if (file) {
      if (!file.type.startsWith("image/")) {
        toast.error("Please upload an image file");
        return;
      }
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Image size must be less than 5MB");
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        updateItemRevisionData(
          itemNumber,
          "screenshotPreview",
          e.target.result
        );
      };
      reader.readAsDataURL(file);
      updateItemRevisionData(itemNumber, "screenshot", file);
    }
  };

  // Toggle description visibility
  const toggleDescription = (itemId) => {
    setExpandedItems((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  // Open image modal
  const openImageModal = (images, startIndex = 0) => {
    setImageModal({
      isOpen: true,
      images,
      currentIndex: startIndex,
    });
  };

  // Close image modal
  const closeImageModal = () => {
    setImageModal({
      isOpen: false,
      images: [],
      currentIndex: 0,
    });
  };

  // Navigate images in modal
  const nextImage = () => {
    setImageModal((prev) => ({
      ...prev,
      currentIndex: (prev.currentIndex + 1) % prev.images.length,
    }));
  };

  const prevImage = () => {
    setImageModal((prev) => ({
      ...prev,
      currentIndex:
        prev.currentIndex === 0
          ? prev.images.length - 1
          : prev.currentIndex - 1,
    }));
  };

  // Toggle revision history for checklist item
  const toggleRevisionHistory = (itemNumber) => {
    setExpandedRevisionHistory((prev) => ({
      ...prev,
      [itemNumber]: !prev[itemNumber],
    }));
  };

  // Toggle revision form for checklist item
  const toggleRevisionForm = (itemNumber) => {
    setShowRevisionForm((prev) => ({
      ...prev,
      [itemNumber]: !prev[itemNumber],
    }));

    // Initialize form data if not exists
    if (!revisionFormData[itemNumber]) {
      setRevisionFormData((prev) => ({
        ...prev,
        [itemNumber]: { comment: "", images: [], imagePreviews: [] },
      }));
    }
  };

  // Handle revision form input change
  const handleRevisionInputChange = (itemNumber, value) => {
    setRevisionFormData((prev) => ({
      ...prev,
      [itemNumber]: { ...prev[itemNumber], comment: value },
    }));
  };

  // Handle revision image upload
  const handleRevisionImageUpload = (itemNumber, files) => {
    const fileArray = Array.from(files);
    const previews = fileArray.map((file) => URL.createObjectURL(file));

    setRevisionFormData((prev) => ({
      ...prev,
      [itemNumber]: {
        ...prev[itemNumber],
        images: [...(prev[itemNumber]?.images || []), ...fileArray],
        imagePreviews: [
          ...(prev[itemNumber]?.imagePreviews || []),
          ...previews,
        ],
      },
    }));
  };

  // Remove revision image
  const removeRevisionImage = (itemNumber, index) => {
    setRevisionFormData((prev) => {
      const updated = { ...prev[itemNumber] };
      updated.images = updated.images.filter((_, i) => i !== index);
      updated.imagePreviews = updated.imagePreviews.filter(
        (_, i) => i !== index
      );
      return { ...prev, [itemNumber]: updated };
    });
  };

  // Submit revision request for individual checklist item
  const submitChecklistItemRevision = async (itemNumber) => {
    const formData = revisionFormData[itemNumber];

    if (!formData || !formData.comment.trim()) {
      toast.error("Please enter a comment for the revision request");
      return;
    }

    setSubmittingRevision((prev) => ({ ...prev, [itemNumber]: true }));

    try {
      // Upload images to backend first
      const uploadedImages = [];
      for (const image of formData.images) {
        const uploadFormData = new FormData();
        uploadFormData.append("image", image);

        const uploadResponse = await fetch(
          `http://localhost:5000/api/tasks/upload-revision-image`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
            credentials: "include",
            body: uploadFormData,
          }
        );

        if (uploadResponse.ok) {
          const uploadData = await uploadResponse.json();
          uploadedImages.push(uploadData.image);
        } else {
          throw new Error("Failed to upload image");
        }
      }

      // Send revision request to backend
      const response = await fetch(
        `http://localhost:5000/api/tasks/${task._id}/checklist/${itemNumber}/revision`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          credentials: "include",
          body: JSON.stringify({
            comment: formData.comment.trim(),
            images: uploadedImages,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to request revision");
      }

      toast.success("Revision requested successfully");

      // Clear form and close
      setRevisionFormData((prev) => ({
        ...prev,
        [itemNumber]: { comment: "", images: [], imagePreviews: [] },
      }));
      setShowRevisionForm((prev) => ({ ...prev, [itemNumber]: false }));

      // Reload task to show updated revision history
      await loadTask();
    } catch (error) {
      console.error("Submit revision error:", error);
      toast.error(error.message || "Failed to submit revision");
    } finally {
      setSubmittingRevision((prev) => ({ ...prev, [itemNumber]: false }));
    }
  };

  // Helper functions
  const getStatusBadge = (status) => {
    const colors = {
      available: "bg-blue-500",
      requested: "bg-yellow-500",
      "in-progress": "bg-purple-500",
      "in-review": "bg-orange-500",
      completed: "bg-green-500",
      revision: "bg-red-500",
    };
    return colors[status] || "bg-gray-500";
  };

  const getPriorityBadge = (priority) => {
    const colors = {
      low: "bg-gray-500",
      medium: "bg-blue-500",
      high: "bg-orange-500",
      urgent: "bg-red-500",
    };
    return colors[priority] || "bg-gray-500";
  };

  const formatDate = (dateString) => {
    if (!dateString) return "No due date";
    const date = new Date(dateString);
    const now = new Date();
    const isOverdue = date < now;
    const formatted = date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
    return { formatted, isOverdue };
  };

  if (loading && !task) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!task) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-foreground">
              Task not found
            </h3>
            <button
              onClick={() => navigate("/admin/tasks")}
              className="mt-4 px-4 py-2 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg transition-colors"
            >
              Back to Tasks
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const dueDate = formatDate(task.dueDate);

  return (
    <AdminLayout>
      <div className="flex-1 p-8">
        {/* Back Button */}
        <button
          onClick={() => navigate("/admin/tasks/all")}
          className="flex items-center gap-2 text-muted-foreground hover:text-foreground mb-6 transition-colors"
        >
          <HiArrowLeft className="w-5 h-5" />
          <span>Back to Tasks</span>
        </button>

        {/* Header Card */}
        <Card className="border border-border/40 backdrop-blur-sm bg-card/95 mb-6">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <Badge
                    variant="outline"
                    className="bg-blue-600/20 text-blue-400 border-blue-600/30"
                  >
                    {task.status}
                  </Badge>
                  <Badge
                    variant="outline"
                    className={`${
                      task.priority === "high"
                        ? "bg-orange-600/20 text-orange-400 border-orange-600/30"
                        : task.priority === "medium"
                        ? "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
                        : "bg-gray-600/20 text-gray-400 border-gray-600/30"
                    }`}
                  >
                    {task.priority}
                  </Badge>
                  <Badge variant="outline">{task.category}</Badge>
                  <Badge variant="outline" className="capitalize">
                    {task.complexity}
                  </Badge>
                </div>
                <h1 className="text-3xl font-bold text-foreground mb-2">
                  {task.title}
                </h1>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                {/* Show Mark Complete button for:
                    1. Tasks in pending-review/in-review status, OR
                    2. Section-based tasks where all sections are completed */}
                {(task.status === "pending-review" ||
                  task.status === "in-review" ||
                  (task.taskType === "section-based" &&
                    task.sections?.length > 0 &&
                    task.sections.every((s) => s.status === "completed"))) && (
                  <>
                    <button
                      onClick={handleAdminComplete}
                      disabled={submitLoading}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center gap-2 transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <HiCheckCircle className="w-5 h-5" />
                      Mark Complete
                    </button>
                  </>
                )}
                {/* Show Request Revision for checklist tasks with completed items */}
                {task.hasChecklist &&
                  task.checklist?.some((item) => item.completed) &&
                  (task.status === "in-progress" ||
                    task.status === "pending-review" ||
                    task.status === "in-review") && (
                    <button
                      onClick={handleRejectChecklist}
                      disabled={submitLoading}
                      className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg flex items-center gap-2 transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <HiXCircle className="w-5 h-5" />
                      Request Revision
                    </button>
                  )}

                {/* Only show Edit, Reassign, and Delete when task is NOT completed/cancelled and NOT ready for completion */}
                {task.status !== "completed" &&
                  task.status !== "cancelled" &&
                  task.status !== "pending-review" &&
                  !(
                    task.taskType === "section-based" &&
                    task.sections?.length > 0 &&
                    task.sections.every((s) => s.status === "completed")
                  ) && (
                    <>
                      <button
                        onClick={() =>
                          navigate(`/admin/tasks/edit/${task._id}`)
                        }
                        className="px-4 py-2 bg-amber-600 hover:bg-amber-700 text-white rounded-lg flex items-center gap-2 transition-colors text-sm font-medium"
                      >
                        <HiPencil className="w-4 h-4" />
                        Edit
                      </button>
                      <button
                        onClick={handleOpenAssignModal}
                        className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center gap-2 transition-colors text-sm font-medium"
                      >
                        <HiUserAdd className="w-4 h-4" />
                        {task.assignedTo ? "Reassign" : "Assign"}
                      </button>
                      <button
                        onClick={() => setIsDeleteModalOpen(true)}
                        disabled={submitLoading}
                        className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg flex items-center gap-2 transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <HiTrash className="w-4 h-4" />
                        Delete
                      </button>
                    </>
                  )}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Tabs */}
        <div className="flex gap-2 mb-6 border-b border-border">
          {[
            { id: "details", label: "Details", icon: HiDocumentText },
            {
              id: "sections",
              label:
                task.taskType === "section-based" ? "Sections" : "Checklist",
              icon: HiClipboardList,
            },
            { id: "comments", label: "Comments", icon: HiChat },
            { id: "history", label: "History", icon: HiClock },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-3 border-b-2 transition-colors ${
                activeTab === tab.id
                  ? "border-rose-500 text-rose-500 font-semibold"
                  : "border-transparent text-muted-foreground hover:text-foreground"
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {activeTab === "details" && (
              <>
                {/* Description */}
                <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                  <CardHeader>
                    <CardTitle className="text-foreground">
                      Description
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground whitespace-pre-wrap leading-relaxed">
                      {task.description || "No description provided"}
                    </p>
                  </CardContent>
                </Card>

                {/* Admin Notes */}
                {task.adminNotes && (
                  <Card className="border border-amber-600/40 backdrop-blur-sm bg-amber-600/10">
                    <CardHeader>
                      <CardTitle className="text-amber-400 flex items-center gap-2">
                        <HiDocumentText className="w-5 h-5" />
                        Admin Notes
                      </CardTitle>
                      <CardDescription className="text-amber-300/70">
                        Private notes visible only to admins
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                        {task.adminNotes}
                      </p>
                    </CardContent>
                  </Card>
                )}

                {/* Attachments */}
                {task.attachments && task.attachments.length > 0 && (
                  <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                    <CardHeader>
                      <CardTitle className="text-foreground flex items-center gap-2">
                        <HiDocumentText className="w-5 h-5" />
                        Attachments
                      </CardTitle>
                      <CardDescription>
                        Files attached to this task
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {task.attachments.map((file, index) => (
                          <a
                            key={index}
                            href={file.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center justify-between p-3 rounded-lg border border-border bg-muted/30 hover:bg-muted/50 transition-colors group"
                          >
                            <div className="flex items-center gap-3">
                              {file.fileType === "pdf" ? (
                                <HiDocumentText className="w-5 h-5 text-red-500" />
                              ) : (
                                <HiPhotograph className="w-5 h-5 text-blue-500" />
                              )}
                              <div>
                                <p className="text-sm font-medium text-foreground">
                                  {file.fileName}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {(file.fileSize / 1024).toFixed(1)} KB
                                </p>
                              </div>
                            </div>
                            <HiDownload className="w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors" />
                          </a>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            )}

            {activeTab === "sections" && (
              <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                <CardHeader>
                  <CardTitle className="text-foreground">
                    {task.taskType === "section-based"
                      ? "Task Sections"
                      : "Checklist Items"}
                  </CardTitle>
                  <CardDescription>
                    {task.taskType === "section-based"
                      ? "Sequential steps to complete this task"
                      : "Todo items with reference images"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {task.taskType === "section-based" ? (
                    task.sections && task.sections.length > 0 ? (
                      <div className="space-y-4">
                        {task.sections.map((section, index) => (
                          <div
                            key={section._id || index}
                            className="p-4 rounded-lg border border-border bg-muted/30"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold text-foreground">
                                Section {section.number}: {section.title}
                              </h4>
                              <div className="flex items-center gap-2">
                                <Badge
                                  variant="outline"
                                  className={
                                    section.status === "completed"
                                      ? "bg-green-600/20 text-green-400 border-green-600/30"
                                      : section.status === "needs-revision"
                                      ? "bg-red-600/20 text-red-400 border-red-600/30"
                                      : section.status === "pending-review"
                                      ? "bg-blue-600/20 text-blue-400 border-blue-600/30"
                                      : section.status === "unlocked"
                                      ? "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
                                      : "bg-gray-600/20 text-gray-400 border-gray-600/30"
                                  }
                                >
                                  {section.status || "locked"}
                                </Badge>
                                {section.status === "pending-review" && (
                                  <div className="flex gap-1">
                                    <button
                                      onClick={() =>
                                        handleApproveSection(section.number)
                                      }
                                      disabled={submitLoading}
                                      className="p-1.5 bg-green-600 hover:bg-green-700 text-white rounded transition-colors disabled:opacity-50"
                                      title="Approve section"
                                    >
                                      <HiCheckCircle className="w-4 h-4" />
                                    </button>
                                    <button
                                      onClick={() => {
                                        setSelectedSectionNum(section.number);
                                        setShowSectionRejectModal(true);
                                      }}
                                      disabled={submitLoading}
                                      className="p-1.5 bg-red-600 hover:bg-red-700 text-white rounded transition-colors disabled:opacity-50"
                                      title="Reject section"
                                    >
                                      <HiXCircle className="w-4 h-4" />
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                            {section.description && (
                              <p className="text-sm text-muted-foreground mb-2">
                                {section.description}
                              </p>
                            )}
                            {section.prLink && (
                              <div className="mt-2 space-y-2">
                                <a
                                  href={section.prLink}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-sm text-blue-400 hover:text-blue-300 underline flex items-center gap-1"
                                >
                                  <HiDocumentText className="w-4 h-4" />
                                  View PR Link
                                </a>

                                {/* Show submitted screenshots */}
                                {(() => {
                                  console.log(
                                    "Admin - Section screenshots:",
                                    section.screenshots
                                  );
                                  return (
                                    section.screenshots &&
                                    section.screenshots.length > 0
                                  );
                                })() && (
                                  <div>
                                    <p className="text-xs text-muted-foreground mb-2">
                                      Screenshots ({section.screenshots.length}
                                      ):
                                    </p>
                                    <div className="flex gap-2 flex-wrap">
                                      {section.screenshots.map(
                                        (screenshot, idx) => {
                                          const screenshotUrl =
                                            typeof screenshot === "string"
                                              ? screenshot
                                              : screenshot?.url;
                                          return screenshotUrl ? (
                                            <div
                                              key={idx}
                                              className="relative group"
                                            >
                                              <button
                                                onClick={() =>
                                                  openImageModal(
                                                    section.screenshots
                                                      .map((s) =>
                                                        typeof s === "string"
                                                          ? s
                                                          : s?.url
                                                      )
                                                      .filter(Boolean),
                                                    idx
                                                  )
                                                }
                                                className="block relative focus:outline-none"
                                              >
                                                <img
                                                  src={screenshotUrl}
                                                  alt={`Screenshot ${idx + 1}`}
                                                  className="w-24 h-24 object-cover rounded border-2 border-border hover:border-rose-500 transition-colors"
                                                  onError={(e) => {
                                                    e.target.src =
                                                      "https://via.placeholder.com/96x96?text=Error";
                                                  }}
                                                />
                                                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                                                  <span className="text-white text-xs">
                                                    View
                                                  </span>
                                                </div>
                                              </button>
                                            </div>
                                          ) : null;
                                        }
                                      )}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                            {section.feedback && (
                              <div className="mt-2 p-3 rounded-lg bg-background border border-border">
                                <p className="text-xs font-medium text-muted-foreground mb-1">
                                  Admin Feedback:
                                </p>
                                <p className="text-sm text-foreground">
                                  {section.feedback}
                                </p>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-center text-muted-foreground py-8">
                        No sections defined
                      </p>
                    )
                  ) : task.checklist && task.checklist.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {task.checklist.map((item, index) => (
                        <div
                          key={item._id || index}
                          className="p-4 rounded-lg border border-border bg-muted/30"
                        >
                          <div className="flex items-start gap-3 mb-3">
                            <div
                              className={`w-5 h-5 rounded border-2 flex items-center justify-center mt-0.5 flex-shrink-0 ${
                                item.completed
                                  ? "bg-green-600 border-green-600"
                                  : "border-muted-foreground"
                              }`}
                            >
                              {item.completed && (
                                <HiCheckCircle className="w-4 h-4 text-white" />
                              )}
                            </div>
                            <div className="flex-1">
                              {/* Title - Clickable to expand/collapse description */}
                              <div
                                onClick={() =>
                                  item.description &&
                                  item.title &&
                                  toggleDescription(item._id)
                                }
                                className={`text-sm font-medium ${
                                  item.completed
                                    ? "line-through text-muted-foreground"
                                    : "text-foreground"
                                } ${
                                  item.description && item.title
                                    ? "cursor-pointer hover:text-rose-500 transition-colors"
                                    : ""
                                }`}
                              >
                                <span>
                                  {item.number}.{" "}
                                  {item.title || item.description}
                                </span>
                                {item.description && item.title && (
                                  <svg
                                    className={`inline-block w-4 h-4 ml-1 transition-transform ${
                                      expandedItems[item._id]
                                        ? "rotate-180"
                                        : ""
                                    }`}
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M19 9l-7 7-7-7"
                                    />
                                  </svg>
                                )}
                              </div>

                              {/* Collapsible Description */}
                              {item.description &&
                                item.title &&
                                expandedItems[item._id] && (
                                  <div className="mt-2 p-3 bg-muted/50 rounded-lg border border-border text-sm text-muted-foreground">
                                    {item.description}
                                  </div>
                                )}

                              {/* Reference Images */}
                              {item.referenceImages &&
                              item.referenceImages.length > 0 ? (
                                <div className="mt-3 space-y-2">
                                  <p className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                                    <HiPhotograph className="w-4 h-4" />
                                    Reference Images (
                                    {
                                      item.referenceImages.filter(
                                        (img) => img.url || img.imageUrl
                                      ).length
                                    }
                                    ):
                                  </p>
                                  <div className="grid grid-cols-3 gap-2">
                                    {item.referenceImages
                                      .filter((img) => img.url || img.imageUrl)
                                      .map((img, imgIndex) => {
                                        const imageUrl =
                                          img.url || img.imageUrl;

                                        return (
                                          <button
                                            key={imgIndex}
                                            onClick={() =>
                                              openImageModal(
                                                item.referenceImages
                                                  .map(
                                                    (i) => i.url || i.imageUrl
                                                  )
                                                  .filter(Boolean),
                                                imgIndex
                                              )
                                            }
                                            className="group relative aspect-video rounded-lg overflow-hidden border border-border hover:border-rose-500 transition-colors focus:outline-none focus:ring-2 focus:ring-rose-500"
                                          >
                                            <img
                                              src={imageUrl}
                                              alt={
                                                img.fileName ||
                                                `Reference ${imgIndex + 1}`
                                              }
                                              className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                                              onError={(e) => {
                                                e.target.src =
                                                  "https://via.placeholder.com/400x300?text=Image+Not+Found";
                                              }}
                                            />
                                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                              <span className="text-white text-xs font-medium">
                                                View
                                              </span>
                                            </div>
                                          </button>
                                        );
                                      })}
                                  </div>
                                </div>
                              ) : (
                                <p className="text-xs text-muted-foreground mt-2">
                                  No reference images
                                </p>
                              )}

                              {/* Work History Timeline - Show when completed OR when needs revision (has history) */}
                              {(item.completed ||
                                (item.needsRevision &&
                                  item.screenshots &&
                                  item.screenshots.length > 0)) && (
                                <div className="mt-4 space-y-3">
                                  {/* Original Submission by Talent */}
                                  {item.screenshots &&
                                    item.screenshots.length > 0 && (
                                      <div
                                        className={`p-3 rounded-lg space-y-2 ${
                                          item.needsRevision
                                            ? "bg-yellow-500/10 border border-yellow-500/30"
                                            : "bg-green-500/10 border border-green-500/30"
                                        }`}
                                      >
                                        <p
                                          className={`text-xs font-semibold flex items-center gap-1 ${
                                            item.needsRevision
                                              ? "text-yellow-400"
                                              : "text-green-400"
                                          }`}
                                        >
                                          <HiCheckCircle className="w-4 h-4" />
                                          {item.needsRevision
                                            ? "Talent's Previous Submission"
                                            : "Talent Submitted Work"}
                                        </p>
                                        {item.prLink && (
                                          <div className="p-2 bg-muted/20 rounded">
                                            <p className="text-xs text-muted-foreground mb-1">
                                              PR/Test Link:
                                            </p>
                                            <a
                                              href={item.prLink}
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              className="text-sm text-blue-400 hover:text-blue-300 break-all underline"
                                            >
                                              {item.prLink}
                                            </a>
                                          </div>
                                        )}
                                        {item.completionNote && (
                                          <div className="p-2 bg-muted/20 rounded">
                                            <p className="text-xs text-muted-foreground mb-1">
                                              Note:
                                            </p>
                                            <p className="text-sm text-foreground">
                                              {item.completionNote}
                                            </p>
                                          </div>
                                        )}
                                        <div>
                                          <p className="text-xs text-muted-foreground mb-2">
                                            Screenshots (
                                            {item.screenshots.length}):
                                          </p>
                                          <div className="grid grid-cols-3 gap-3">
                                            {item.screenshots.map(
                                              (screenshot, ssIndex) => {
                                                const screenshotUrl =
                                                  typeof screenshot === "string"
                                                    ? screenshot
                                                    : screenshot.url;
                                                const annotatedUrl =
                                                  typeof screenshot === "object"
                                                    ? screenshot?.annotatedUrl
                                                    : null;
                                                return (
                                                  <div
                                                    key={ssIndex}
                                                    data-screenshot-checklist-index={
                                                      ssIndex
                                                    }
                                                    className="space-y-2 transition-all duration-300"
                                                  >
                                                    <div className="relative group">
                                                      <button
                                                        onClick={() =>
                                                          openImageModal(
                                                            item.screenshots
                                                              .map((s) =>
                                                                typeof s ===
                                                                "string"
                                                                  ? s
                                                                  : s.url
                                                              )
                                                              .filter(Boolean),
                                                            ssIndex
                                                          )
                                                        }
                                                        className={`w-full aspect-video rounded-lg overflow-hidden border-2 transition-colors focus:outline-none focus:ring-2 ${
                                                          item.needsRevision
                                                            ? "border-yellow-500/50 hover:border-yellow-400 focus:ring-yellow-500"
                                                            : "border-green-500/50 hover:border-green-400 focus:ring-green-500"
                                                        }`}
                                                      >
                                                        <img
                                                          src={screenshotUrl}
                                                          alt={`Screenshot ${
                                                            ssIndex + 1
                                                          }`}
                                                          className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                                                          onError={(e) => {
                                                            e.target.src =
                                                              "https://via.placeholder.com/400x300?text=Image+Not+Available";
                                                          }}
                                                        />
                                                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                                          <span className="text-white text-xs font-medium">
                                                            View
                                                          </span>
                                                        </div>
                                                        <div
                                                          className={`absolute top-1 right-1 text-white text-xs px-2 py-0.5 rounded ${
                                                            item.needsRevision
                                                              ? "bg-yellow-600"
                                                              : "bg-green-600"
                                                          }`}
                                                        >
                                                          {new Date(
                                                            screenshot.uploadedAt ||
                                                              Date.now()
                                                          ).toLocaleDateString()}
                                                        </div>
                                                        {annotatedUrl && (
                                                          <div className="absolute -top-1 -left-1 bg-blue-600 text-white rounded-full px-1.5 py-0.5 text-xs font-medium">
                                                            ✏️
                                                          </div>
                                                        )}
                                                      </button>
                                                    </div>
                                                    {annotatedUrl && (
                                                      <div className="relative group">
                                                        <button
                                                          onClick={() =>
                                                            window.open(
                                                              annotatedUrl,
                                                              "_blank"
                                                            )
                                                          }
                                                          className="w-full aspect-video rounded-lg overflow-hidden border-2 border-blue-600 hover:border-blue-500 transition-colors focus:outline-none"
                                                        >
                                                          <img
                                                            src={annotatedUrl}
                                                            alt={`Annotated ${
                                                              ssIndex + 1
                                                            }`}
                                                            className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                                                          />
                                                          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                                            <span className="text-white text-xs font-medium">
                                                              Annotated
                                                            </span>
                                                          </div>
                                                        </button>
                                                      </div>
                                                    )}
                                                    <button
                                                      onClick={() =>
                                                        handleOpenAnnotation(
                                                          screenshotUrl,
                                                          ssIndex,
                                                          null,
                                                          item.number,
                                                          "checklist"
                                                        )
                                                      }
                                                      className="w-full px-2 py-1 bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 text-xs rounded border border-blue-600/30 transition-colors flex items-center justify-center gap-1"
                                                      title="Edit this screenshot"
                                                    >
                                                      <HiPencil className="w-3 h-3" />
                                                      Edit
                                                    </button>
                                                  </div>
                                                );
                                              }
                                            )}
                                          </div>
                                        </div>
                                      </div>
                                    )}

                                  {/* Request Revision Button - only show if completed and not needs-revision */}
                                  {item.completed &&
                                    item.status !== "needs-revision" && (
                                      <button
                                        onClick={() => {
                                          setSelectedChecklistItem(item);
                                          setShowChecklistItemRejectModal(true);
                                        }}
                                        className="w-full px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg flex items-center justify-center gap-2 transition-colors text-sm font-medium"
                                      >
                                        <HiXCircle className="w-4 h-4" />
                                        Request Revision
                                      </button>
                                    )}
                                </div>
                              )}

                              {/* Admin's Revision Request (Timeline Event) */}
                              {item.needsRevision &&
                                item.issues &&
                                item.issues.length > 0 && (
                                  <div className="mt-3 p-3 rounded-lg bg-orange-500/10 border border-orange-500/30 space-y-2">
                                    <p className="text-xs font-semibold text-orange-400 flex items-center gap-1">
                                      <HiExclamation className="w-4 h-4" />
                                      Admin Requested Revision
                                    </p>
                                    {item.revisionNote && (
                                      <div className="p-2 bg-muted/20 rounded">
                                        <p className="text-xs text-muted-foreground mb-1">
                                          Note:
                                        </p>
                                        <p className="text-sm text-foreground italic">
                                          {item.revisionNote}
                                        </p>
                                      </div>
                                    )}
                                    <div className="space-y-2">
                                      {item.issues.map((issue, issueIdx) => (
                                        <div
                                          key={issueIdx}
                                          className="p-2 bg-muted/20 rounded border border-orange-500/20"
                                        >
                                          <div className="flex items-center gap-2 mb-1">
                                            <span className="text-xs text-muted-foreground">
                                              {new Date(
                                                issue.createdAt
                                              ).toLocaleDateString()}
                                            </span>
                                          </div>
                                          <p className="text-sm text-foreground mb-2">
                                            {issue.description}
                                          </p>
                                          {issue.screenshot &&
                                            (issue.screenshot.url ||
                                              typeof issue.screenshot ===
                                                "string") && (
                                              <div>
                                                <p className="text-xs text-muted-foreground mb-1">
                                                  Admin's Screenshot:
                                                </p>
                                                <button
                                                  onClick={() =>
                                                    openImageModal(
                                                      [
                                                        typeof issue.screenshot ===
                                                        "string"
                                                          ? issue.screenshot
                                                          : issue.screenshot
                                                              .url,
                                                      ],
                                                      0
                                                    )
                                                  }
                                                  className="group relative focus:outline-none"
                                                >
                                                  <img
                                                    src={
                                                      typeof issue.screenshot ===
                                                      "string"
                                                        ? issue.screenshot
                                                        : issue.screenshot.url
                                                    }
                                                    alt="Admin's issue screenshot"
                                                    className="w-32 h-32 object-cover rounded-lg border-2 border-orange-500/50 hover:border-orange-400 transition-colors"
                                                    onError={(e) => {
                                                      e.target.src =
                                                        "https://via.placeholder.com/128x128?text=Image+Error";
                                                    }}
                                                  />
                                                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                                                    <span className="text-white text-xs font-medium">
                                                      View
                                                    </span>
                                                  </div>
                                                </button>
                                              </div>
                                            )}
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}

                              {/* Revision History Section */}
                              {item.revisionHistory &&
                                item.revisionHistory.length > 0 && (
                                  <div className="mt-4">
                                    <button
                                      onClick={() =>
                                        toggleRevisionHistory(item.number)
                                      }
                                      className="flex items-center gap-2 text-sm font-medium text-slate-300 hover:text-white transition-colors"
                                    >
                                      <HiChatAlt2 className="w-4 h-4" />
                                      Revision History (
                                      {item.revisionHistory.length})
                                      <svg
                                        className={`w-4 h-4 transition-transform ${
                                          expandedRevisionHistory[item.number]
                                            ? "rotate-180"
                                            : ""
                                        }`}
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M19 9l-7 7-7-7"
                                        />
                                      </svg>
                                    </button>

                                    {expandedRevisionHistory[item.number] && (
                                      <div className="mt-3 space-y-3">
                                        {item.revisionHistory.map(
                                          (entry, idx) => (
                                            <div
                                              key={idx}
                                              className={`p-3 rounded-lg border ${
                                                entry.type === "admin_revision"
                                                  ? "bg-orange-500/10 border-orange-500/30"
                                                  : "bg-blue-500/10 border-blue-500/30"
                                              }`}
                                            >
                                              <div className="flex items-center justify-between mb-2">
                                                <span
                                                  className={`text-xs font-semibold ${
                                                    entry.type ===
                                                    "admin_revision"
                                                      ? "text-orange-400"
                                                      : "text-blue-400"
                                                  }`}
                                                >
                                                  {entry.type ===
                                                  "admin_revision"
                                                    ? "Admin Revision Request"
                                                    : "Talent Response"}
                                                </span>
                                                <span className="text-xs text-slate-400">
                                                  {new Date(
                                                    entry.createdAt
                                                  ).toLocaleDateString(
                                                    "en-US",
                                                    {
                                                      month: "short",
                                                      day: "numeric",
                                                      hour: "2-digit",
                                                      minute: "2-digit",
                                                    }
                                                  )}
                                                </span>
                                              </div>
                                              <p className="text-sm text-slate-300 mb-2">
                                                {entry.comment}
                                              </p>
                                              {entry.images &&
                                                entry.images.length > 0 && (
                                                  <div className="grid grid-cols-3 gap-2 mt-2">
                                                    {entry.images.map(
                                                      (img, imgIdx) => {
                                                        const imageUrl =
                                                          typeof img ===
                                                          "string"
                                                            ? img
                                                            : img.url;
                                                        return (
                                                          <button
                                                            key={imgIdx}
                                                            onClick={() =>
                                                              openImageModal(
                                                                entry.images.map(
                                                                  (i) =>
                                                                    typeof i ===
                                                                    "string"
                                                                      ? i
                                                                      : i.url
                                                                ),
                                                                imgIdx
                                                              )
                                                            }
                                                            className="group relative aspect-video rounded-lg overflow-hidden border border-slate-600 hover:border-slate-400 transition-colors"
                                                          >
                                                            <img
                                                              src={imageUrl}
                                                              alt={
                                                                (typeof img ===
                                                                  "object" &&
                                                                  img.fileName) ||
                                                                `Image ${
                                                                  imgIdx + 1
                                                                }`
                                                              }
                                                              className="w-full h-full object-cover"
                                                            />
                                                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                                              <span className="text-white text-xs">
                                                                View
                                                              </span>
                                                            </div>
                                                          </button>
                                                        );
                                                      }
                                                    )}
                                                  </div>
                                                )}
                                            </div>
                                          )
                                        )}
                                      </div>
                                    )}
                                  </div>
                                )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-muted-foreground py-8">
                      No checklist items
                    </p>
                  )}
                </CardContent>
              </Card>
            )}

            {activeTab === "comments" && (
              <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                <CardHeader>
                  <CardTitle className="text-foreground">
                    Comments & Discussion
                  </CardTitle>
                  <CardDescription>
                    Team collaboration and feedback
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {task.comments && task.comments.length > 0 ? (
                      task.comments.map((comment, index) => (
                        <div
                          key={comment._id || index}
                          className="p-4 rounded-lg border border-border bg-muted/30"
                        >
                          <div className="flex items-center gap-3 mb-3">
                            <div className="w-10 h-10 rounded-full bg-rose-500/20 flex items-center justify-center text-sm font-semibold text-rose-400">
                              {comment.author?.name?.charAt(0).toUpperCase()}
                            </div>
                            <div>
                              <p className="font-semibold text-foreground">
                                {comment.author?.name}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {new Date(comment.createdAt).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <p className="text-sm text-foreground whitespace-pre-wrap leading-relaxed">
                            {comment.text}
                          </p>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-12">
                        <div className="w-16 h-16 rounded-full bg-muted/30 mx-auto mb-4 flex items-center justify-center">
                          <HiChatAlt2 className="w-8 h-8 text-muted-foreground" />
                        </div>
                        <p className="text-muted-foreground">No comments yet</p>
                        <p className="text-sm text-muted-foreground mt-1">
                          Start the conversation below
                        </p>
                      </div>
                    )}

                    {/* Add Comment Form */}
                    <div className="pt-4 border-t border-border">
                      <textarea
                        placeholder="Add a comment..."
                        className="w-full px-4 py-3 rounded-lg border border-input bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-rose-500/20 focus:border-rose-500 resize-none"
                        rows={3}
                      />
                      <button className="mt-3 px-4 py-2 bg-rose-600 hover:bg-rose-700 text-white rounded-lg text-sm font-medium transition-colors">
                        Add Comment
                      </button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {activeTab === "history" && (
              <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
                <CardHeader>
                  <CardTitle className="text-foreground">
                    Activity History
                  </CardTitle>
                  <CardDescription>
                    Timeline of all task activities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Build timeline from all available events */}
                    {(() => {
                      const events = [];

                      // Task Created
                      if (task.createdAt) {
                        events.push({
                          type: "created",
                          title: "Task Created",
                          timestamp: new Date(task.createdAt),
                          description: task.createdBy?.name
                            ? `by ${task.createdBy.name}`
                            : null,
                          color: "bg-green-500",
                        });
                      }

                      // Task Assigned
                      if (task.assignedTo) {
                        events.push({
                          type: "assigned",
                          title: "Task Assigned",
                          timestamp: task.startedAt
                            ? new Date(
                                new Date(task.startedAt).getTime() - 1000
                              )
                            : new Date(task.createdAt),
                          description: `Assigned to ${task.assignedTo.name}`,
                          color: "bg-rose-500",
                        });
                      }

                      // Work Started
                      if (task.startedAt) {
                        events.push({
                          type: "started",
                          title: "Work Started",
                          timestamp: new Date(task.startedAt),
                          description: task.assignedTo?.name
                            ? `by ${task.assignedTo.name}`
                            : null,
                          color: "bg-cyan-500",
                        });
                      }

                      // Checklist item completions
                      if (task.hasChecklist && task.checklist) {
                        task.checklist.forEach((item) => {
                          // Item completed
                          if (item.completedAt) {
                            events.push({
                              type: "item_completed",
                              title: `Checklist Item Completed`,
                              timestamp: new Date(item.completedAt),
                              description: `"${item.title}" marked as complete`,
                              color: "bg-blue-500",
                            });
                          }

                          // Revision requested for item
                          if (item.issues && item.issues.length > 0) {
                            item.issues.forEach((issue) => {
                              if (issue.createdAt) {
                                events.push({
                                  type: "revision_requested",
                                  title: "Revision Requested",
                                  timestamp: new Date(issue.createdAt),
                                  description: `Item "${item.title}": ${issue.description}`,
                                  color: "bg-orange-500",
                                });
                              }
                            });
                          }
                        });
                      }

                      // Submitted for Review (check if prLink exists or status is pending-review/needs-revision/completed)
                      if (
                        task.prLink &&
                        (task.status === "pending-review" ||
                          task.status === "needs-revision" ||
                          task.status === "completed")
                      ) {
                        // Use updatedAt as proxy for submission time if no specific field
                        const submittedTime =
                          task.status === "completed" && task.completedAt
                            ? new Date(
                                new Date(task.completedAt).getTime() - 60000
                              )
                            : new Date(task.updatedAt);
                        events.push({
                          type: "submitted",
                          title: "Submitted for Review",
                          timestamp: submittedTime,
                          description: task.assignedTo?.name
                            ? `${task.assignedTo.name} submitted work for review`
                            : "Work submitted for review",
                          color: "bg-purple-500",
                          prLink: task.prLink,
                        });
                      }

                      // Needs Revision status
                      if (task.status === "needs-revision" && task.feedback) {
                        events.push({
                          type: "needs_revision",
                          title: "Revision Required",
                          timestamp: new Date(task.updatedAt),
                          description: task.feedback,
                          color: "bg-orange-500",
                        });
                      }

                      // Task Completed
                      if (task.status === "completed" && task.completedAt) {
                        events.push({
                          type: "completed",
                          title: "Task Completed ✓",
                          timestamp: new Date(task.completedAt),
                          description: task.approvedBy?.name
                            ? `Approved by ${task.approvedBy.name}`
                            : null,
                          xpAwarded: task.xpValue,
                          talentName: task.assignedTo?.name,
                          color: "bg-emerald-500",
                        });
                      }

                      // Sort events by timestamp (oldest first)
                      events.sort((a, b) => a.timestamp - b.timestamp);

                      return events.map((event, idx) => (
                        <div
                          key={`${event.type}-${idx}`}
                          className={`relative pl-6 ${
                            idx < events.length - 1
                              ? "pb-4 border-l-2 border-border"
                              : "border-l-2 border-border"
                          }`}
                        >
                          <div
                            className={`absolute left-[-5px] top-0 w-3 h-3 rounded-full ${event.color} border-2 border-background`}
                          ></div>
                          <div className="bg-muted/30 p-3 rounded-lg border border-border">
                            <p className="font-semibold text-foreground">
                              {event.title}
                            </p>
                            <p className="text-sm text-muted-foreground mt-1">
                              {event.timestamp.toLocaleString()}
                            </p>
                            {event.description && (
                              <p className="text-xs text-muted-foreground mt-1">
                                {event.description}
                              </p>
                            )}
                            {event.prLink && (
                              <a
                                href={event.prLink}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-400 hover:underline mt-1 block"
                              >
                                View PR/Test Link →
                              </a>
                            )}
                            {event.xpAwarded > 0 && (
                              <p className="text-xs text-green-400 mt-1">
                                +{event.xpAwarded} XP awarded to{" "}
                                {event.talentName}
                              </p>
                            )}
                          </div>
                        </div>
                      ));
                    })()}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Task Info */}
            <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
              <CardHeader>
                <CardTitle className="text-lg text-foreground">
                  Task Information
                </CardTitle>
                <CardDescription>Key task details and metadata</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-3 rounded-lg bg-muted/30 border border-border">
                  <div className="flex items-center gap-2 text-muted-foreground mb-2">
                    <HiCalendar className="w-4 h-4" />
                    <span className="text-xs font-medium">Due Date</span>
                  </div>
                  <p
                    className={`text-sm font-semibold ${
                      dueDate.isOverdue ? "text-red-400" : "text-foreground"
                    }`}
                  >
                    {dueDate.formatted}
                    {dueDate.isOverdue && (
                      <span className="ml-2 text-xs">⚠️ Overdue</span>
                    )}
                  </p>
                </div>

                <div className="p-3 rounded-lg bg-muted/30 border border-border">
                  <div className="flex items-center gap-2 text-muted-foreground mb-2">
                    <HiUser className="w-4 h-4" />
                    <span className="text-xs font-medium">Assigned To</span>
                  </div>
                  {task.assignedTo ? (
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-rose-500/20 flex items-center justify-center text-sm font-semibold text-rose-400">
                        {task.assignedTo.name?.charAt(0).toUpperCase()}
                      </div>
                      <span className="text-sm font-semibold text-foreground">
                        {task.assignedTo.name}
                      </span>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">Unassigned</p>
                  )}
                </div>

                <div className="p-3 rounded-lg bg-muted/30 border border-border">
                  <div className="flex items-center gap-2 text-muted-foreground mb-2">
                    <HiUser className="w-4 h-4" />
                    <span className="text-xs font-medium">Created By</span>
                  </div>
                  {task.createdBy && (
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center text-sm font-semibold text-blue-400">
                        {task.createdBy.name?.charAt(0).toUpperCase()}
                      </div>
                      <span className="text-sm font-semibold text-foreground">
                        {task.createdBy.name}
                      </span>
                    </div>
                  )}
                </div>

                <div className="p-3 rounded-lg bg-muted/30 border border-border">
                  <div className="flex items-center gap-2 text-muted-foreground mb-2">
                    <HiClock className="w-4 h-4" />
                    <span className="text-xs font-medium">Complexity</span>
                  </div>
                  <p className="text-sm font-semibold text-foreground capitalize">
                    {task.complexity}
                  </p>
                </div>

                <div className="p-3 rounded-lg bg-cyan-600/10 border border-cyan-600/30">
                  <div className="flex items-center gap-2 text-cyan-400 mb-2">
                    <HiStar className="w-4 h-4" />
                    <span className="text-xs font-medium">XP Reward</span>
                  </div>
                  <p className="text-lg font-bold text-cyan-300">
                    {task.xpReward || 0} XP
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
              <CardHeader>
                <CardTitle className="text-lg text-foreground">
                  Quick Stats
                </CardTitle>
                <CardDescription>Task progress and activity</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {task.taskType === "section-based" && task.sections && (
                  <div className="p-3 rounded-lg bg-muted/30 border border-border">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-muted-foreground">
                        Sections
                      </span>
                      <span className="text-sm font-semibold text-foreground">
                        {
                          task.sections.filter((s) => s.status === "completed")
                            .length
                        }{" "}
                        / {task.sections.length}
                      </span>
                    </div>
                    <div className="w-full bg-background border border-border rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-rose-500 to-pink-500 h-2 rounded-full transition-all"
                        style={{
                          width: `${Math.round(
                            (task.sections.filter(
                              (s) => s.status === "completed"
                            ).length /
                              task.sections.length) *
                              100
                          )}%`,
                        }}
                      />
                    </div>
                  </div>
                )}
                {task.taskType !== "section-based" && task.checklist && (
                  <div className="p-3 rounded-lg bg-muted/30 border border-border">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-muted-foreground">
                        Checklist
                      </span>
                      <span className="text-sm font-semibold text-foreground">
                        {task.checklist.filter((i) => i.completed).length} /{" "}
                        {task.checklist.length}
                      </span>
                    </div>
                    <div className="w-full bg-background border border-border rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-rose-500 to-pink-500 h-2 rounded-full transition-all"
                        style={{
                          width: `${Math.round(
                            (task.checklist.filter((i) => i.completed).length /
                              task.checklist.length) *
                              100
                          )}%`,
                        }}
                      />
                    </div>
                  </div>
                )}
                <div className="p-3 rounded-lg bg-muted/30 border border-border flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">
                    Comments
                  </span>
                  <span className="text-sm font-semibold text-foreground">
                    {task.comments?.length || 0}
                  </span>
                </div>
                <div className="p-3 rounded-lg bg-muted/30 border border-border flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">
                    Attachments
                  </span>
                  <span className="text-sm font-semibold text-foreground">
                    {task.attachments?.length || 0}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteTask}
        title="Delete Task"
        message="Are you sure you want to delete this task? This action will archive the task and it can be recovered later."
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        loading={submitLoading}
      />

      {/* Assignment Modal */}
      {showAssignModal && (
        <AssignTaskModal
          task={task}
          talents={talents}
          loading={talentsLoading}
          onClose={() => setShowAssignModal(false)}
          onAssign={handleAssignTask}
        />
      )}

      {/* Section Reject Modal */}
      <RejectSectionModal
        isOpen={showSectionRejectModal}
        onClose={() => {
          setShowSectionRejectModal(false);
          setSelectedSectionNum(null);
        }}
        onSubmit={handleRejectSection}
        taskTitle={task?.title || ""}
        sectionNumber={selectedSectionNum}
        sectionData={task?.sections?.find(
          (s) => s.number === selectedSectionNum
        )}
        onEditScreenshot={(screenshotUrl, idx, sectionNum) => {
          handleOpenAnnotation(screenshotUrl, idx, sectionNum, null, "section");
        }}
        loading={submitLoading}
      />

      {/* Checklist Reject Modal */}
      <RejectChecklistModal
        isOpen={showChecklistRejectModal}
        onClose={() => setShowChecklistRejectModal(false)}
        onSubmit={handleSubmitChecklistRejection}
        taskTitle={task?.title || ""}
        checklistItems={task?.checklist || []}
        loading={submitLoading}
      />

      {/* Individual Checklist Item Reject Modal */}
      <RejectSectionModal
        isOpen={showChecklistItemRejectModal}
        onClose={() => {
          setShowChecklistItemRejectModal(false);
          setSelectedChecklistItem(null);
        }}
        onSubmit={(data) => {
          handleRequestItemRevision(selectedChecklistItem.number, data);
        }}
        taskTitle={task?.title || ""}
        sectionNumber={selectedChecklistItem?.number}
        sectionData={{
          screenshots: selectedChecklistItem?.screenshots || [],
          title:
            selectedChecklistItem?.title || selectedChecklistItem?.description,
        }}
        onEditScreenshot={(screenshotUrl, idx) => {
          handleOpenAnnotation(
            screenshotUrl,
            idx,
            null,
            selectedChecklistItem?.number,
            "checklist"
          );
        }}
        loading={submitLoading}
        isChecklistItem={true}
      />

      {/* Image Annotation Modal */}
      <ImageAnnotationModal
        isOpen={annotationModal.isOpen}
        onClose={() =>
          setAnnotationModal({
            isOpen: false,
            imageUrl: null,
            screenshotIndex: null,
            sectionNumber: null,
            checklistItemNumber: null,
            itemType: null,
            fileName: null,
          })
        }
        imageUrl={annotationModal.imageUrl}
        onSave={handleSaveAnnotation}
        fileName={annotationModal.fileName}
      />

      {/* Image Preview Modal */}
      {imageModal.isOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
          onClick={closeImageModal}
        >
          <div
            className="relative max-w-4xl max-h-[90vh] w-full mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button - X at top right */}
            <button
              onClick={closeImageModal}
              className="absolute -top-12 right-0 text-white hover:text-slate-300 transition-colors"
            >
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            {/* Image */}
            <img
              src={imageModal.images[imageModal.currentIndex]}
              alt={`Preview ${imageModal.currentIndex + 1}`}
              className="w-full h-auto max-h-[85vh] object-contain rounded-lg"
              onError={(e) => {
                e.target.src =
                  "https://via.placeholder.com/800x600?text=Image+Not+Found";
              }}
            />

            {/* Navigation buttons */}
            {imageModal.images.length > 1 && (
              <>
                {/* Previous button */}
                <button
                  onClick={prevImage}
                  className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-colors"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>

                {/* Next button */}
                <button
                  onClick={nextImage}
                  className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-colors"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>

                {/* Image counter */}
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full text-sm">
                  {imageModal.currentIndex + 1} / {imageModal.images.length}
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Revision Request Dialog */}
      <Dialog
        open={revisionDialog.isOpen}
        onOpenChange={(open) => {
          if (!open) {
            setRevisionDialog({
              isOpen: false,
              itemNumber: null,
              itemTitle: "",
            });
          }
        }}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="text-orange-400">
              Request Revision for Item #{revisionDialog.itemNumber}
            </DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground">
              {revisionDialog.itemTitle}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Issue Description */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Issue Description <span className="text-orange-500">*</span>
              </label>
              <textarea
                value={
                  itemRevisionData[revisionDialog.itemNumber]?.description || ""
                }
                onChange={(e) =>
                  updateItemRevisionData(
                    revisionDialog.itemNumber,
                    "description",
                    e.target.value
                  )
                }
                placeholder="Describe what needs to be fixed..."
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-orange-500/20 text-sm resize-none"
                rows={4}
              />
            </div>

            {/* Screenshot Upload */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Attach Screenshot (Optional)
              </label>
              {!itemRevisionData[revisionDialog.itemNumber]
                ?.screenshotPreview ? (
                <label className="flex flex-col items-center justify-center gap-2 px-4 py-6 bg-background border-2 border-dashed border-border rounded-lg cursor-pointer hover:border-muted-foreground transition-colors">
                  <Upload className="w-6 h-6 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Upload image to show the issue
                  </span>
                  <span className="text-xs text-muted-foreground">
                    PNG, JPG up to 5MB
                  </span>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) =>
                      handleRevisionScreenshot(
                        revisionDialog.itemNumber,
                        e.target.files[0]
                      )
                    }
                    className="hidden"
                  />
                </label>
              ) : (
                <div className="relative">
                  <img
                    src={
                      itemRevisionData[revisionDialog.itemNumber]
                        .screenshotPreview
                    }
                    alt="Issue screenshot"
                    className="w-full h-48 object-cover rounded-lg border border-border"
                  />
                  <button
                    onClick={() => {
                      updateItemRevisionData(
                        revisionDialog.itemNumber,
                        "screenshot",
                        null
                      );
                      updateItemRevisionData(
                        revisionDialog.itemNumber,
                        "screenshotPreview",
                        null
                      );
                    }}
                    className="absolute top-2 right-2 p-1.5 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Dialog Footer */}
          <div className="flex gap-3 pt-4 border-t border-border/40">
            <button
              onClick={() =>
                setRevisionDialog({
                  isOpen: false,
                  itemNumber: null,
                  itemTitle: "",
                })
              }
              className="flex-1 px-4 py-2 border border-border hover:bg-accent text-foreground rounded-lg transition-colors font-medium"
            >
              Cancel
            </button>
            <button
              onClick={async () => {
                // Call the handler first, then close dialog after completion
                await handleInlineRevision(revisionDialog.itemNumber);
                setRevisionDialog({
                  isOpen: false,
                  itemNumber: null,
                  itemTitle: "",
                });
              }}
              disabled={
                !itemRevisionData[
                  revisionDialog.itemNumber
                ]?.description?.trim()
              }
              className="flex-1 px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Send for Revision
            </button>
          </div>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}
