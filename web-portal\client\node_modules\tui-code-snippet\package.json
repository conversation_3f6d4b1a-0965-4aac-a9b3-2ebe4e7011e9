{"name": "tui-code-snippet", "version": "2.3.3", "description": "TOAST UI Utility: CodeSnippet", "main": "dist/tui-code-snippet.js", "scripts": {"eslint": "eslint \"**/*.js\"", "test": "jest", "bundle": "webpack --mode=production & webpack --mode=production --minify", "transpile": "babel --plugins babel-plugin-add-module-exports ajax/index.mjs --out-file ajax/index.js && babel --plugins @babel/plugin-transform-member-expression-literals ajax/index.js --out-file ajax/index.js", "doc:serve": "tuidoc --serv", "doc": "tuidoc", "note": "tui-note"}, "repository": {"type": "git", "url": "https://github.com/nhn/tui.code-snippet.git"}, "keywords": ["nhn", "tui", "utility", "code-snippet"], "author": "NHN. FE Development Lab <<EMAIL>>", "license": "MIT", "files": ["ajax", "array", "browser", "collection", "customEvents", "defineClass", "domEvent", "<PERSON><PERSON><PERSON><PERSON>", "enum", "formatDate", "inheritance", "object", "request", "string", "tricks", "type"], "devDependencies": {"@babel/cli": "^7.8.3", "@babel/core": "^7.8.3", "@babel/plugin-transform-member-expression-literals": "^7.8.3", "@babel/preset-env": "^7.8.3", "@toast-ui/release-notes": "^2.0.1", "babel-plugin-add-module-exports": "^1.0.2", "eslint": "^6.7.1", "eslint-config-prettier": "^6.7.0", "eslint-config-tui": "^3.0.0", "eslint-loader": "^3.0.2", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-prettier": "^3.1.2", "jest": "^27.0.4", "jest-extended": "^0.11.5", "prettier": "^1.19.1", "webpack": "^4.0.0", "webpack-cli": "^3.3.10"}}