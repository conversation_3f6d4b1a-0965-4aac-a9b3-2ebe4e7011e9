/**
 * Centralized Team Management System Design
 * 
 * This design consolidates all admin/team management into a single, 
 * flexible system that <PERSON> can manage from the admin portal.
 */

// 1. NEW PERMISSION SYSTEM DESIGN
const PERMISSION_MODULES = {
  // Employee Module
  employee: {
    name: 'Employee Management',
    permissions: {
      'employee.dashboard.view': 'View employee dashboard',
      'employee.talents.view': 'View talent profiles',
      'employee.talents.create': 'Create new talents',
      'employee.talents.edit': 'Edit talent profiles', 
      'employee.talents.delete': 'Delete talent profiles',
      'employee.reports.view': 'View reports',
      'employee.reports.manage': 'Manage reports',
      'employee.meetings.view': 'View meetings',
      'employee.meetings.manage': 'Manage meetings',
      'employee.leave.view': 'View leave requests',
      'employee.leave.approve': 'Approve leave requests',
      'employee.tasks.view': 'View tasks',
      'employee.tasks.manage': 'Manage tasks',
      'employee.announcements.view': 'View announcements',
      'employee.announcements.manage': 'Manage announcements',
      'employee.analytics.view': 'View analytics',
      'employee.payments.view': 'View payments (restricted)',
    }
  },
  
  // Hiring Module  
  hiring: {
    name: 'Hiring Management',
    permissions: {
      'hiring.dashboard.view': 'View hiring dashboard',
      'hiring.jobs.view': 'View job postings',
      'hiring.jobs.create': 'Create job postings',
      'hiring.jobs.edit': 'Edit job postings',
      'hiring.jobs.delete': 'Delete job postings',
      'hiring.applications.view': 'View applications',
      'hiring.applications.manage': 'Manage applications',
      'hiring.interviews.view': 'View interviews',
      'hiring.interviews.schedule': 'Schedule interviews',
      'hiring.interviews.manage': 'Manage interviews', 
      'hiring.onboarding.view': 'View onboarding',
      'hiring.onboarding.manage': 'Manage onboarding process',
    }
  },
  
  // Admin Module (Super Restricted)
  admin: {
    name: 'Admin Panel',
    permissions: {
      'admin.panel.view': 'View admin panel',
      'admin.team.view': 'View team members',
      'admin.team.invite': 'Invite new team members',
      'admin.team.edit': 'Edit team member permissions',
      'admin.team.remove': 'Remove team members',
      'admin.system.settings': 'System settings',
      'admin.audit.view': 'View audit logs',
    }
  }
};

// 2. PREDEFINED ROLES
const TEAM_ROLES = {
  hiring_manager: {
    name: 'Hiring Manager',
    description: 'Manages recruitment and hiring process',
    color: '#10b981', // Green
    accessibleTabs: ['hiring'],
    permissions: [
      'hiring.dashboard.view',
      'hiring.jobs.view',
      'hiring.jobs.create', 
      'hiring.jobs.edit',
      'hiring.applications.view',
      'hiring.applications.manage',
      'hiring.interviews.view',
      'hiring.interviews.schedule',
      'hiring.interviews.manage',
      'hiring.onboarding.view',
      'hiring.onboarding.manage',
    ]
  },
  
  employee_manager: {
    name: 'Employee Manager',
    description: 'Manages employee-related activities',
    color: '#3b82f6', // Blue
    accessibleTabs: ['employee'], 
    permissions: [
      'employee.dashboard.view',
      'employee.talents.view',
      'employee.talents.edit',
      'employee.reports.view',
      'employee.reports.manage',
      'employee.meetings.view',
      'employee.meetings.manage',
      'employee.leave.view',
      'employee.leave.approve',
      'employee.tasks.view',
      'employee.tasks.manage',
      'employee.announcements.view',
      'employee.announcements.manage',
      'employee.analytics.view',
    ]
  },
  
  full_admin: {
    name: 'Full Administrator', 
    description: 'Full access to employee and hiring',
    color: '#8b5cf6', // Purple
    accessibleTabs: ['employee', 'hiring'],
    permissions: [
      // All employee permissions
      ...TEAM_ROLES.employee_manager.permissions,
      // All hiring permissions
      ...TEAM_ROLES.hiring_manager.permissions,
      // Additional admin permissions
      'employee.talents.create',
      'employee.talents.delete',
      'hiring.jobs.delete',
    ]
  },
  
  team_lead: {
    name: 'Team Lead',
    description: 'Employee + Hiring view access',
    color: '#f59e0b', // Amber
    accessibleTabs: ['employee', 'hiring'],
    permissions: [
      'employee.dashboard.view',
      'employee.talents.view', 
      'employee.reports.view',
      'employee.meetings.view',
      'employee.tasks.view',
      'employee.analytics.view',
      'hiring.dashboard.view',
      'hiring.jobs.view',
      'hiring.applications.view',
      'hiring.interviews.view',
    ]
  },
  
  super_admin: {
    name: 'Super Admin',
    description: 'Full system access including admin panel',
    color: '#ef4444', // Red
    accessibleTabs: ['employee', 'hiring', 'admin'],
    permissions: [
      // All permissions from other roles
      ...TEAM_ROLES.full_admin.permissions,
      // Admin panel permissions
      'admin.panel.view',
      'admin.team.view', 
      'admin.team.invite',
      'admin.team.edit',
      'admin.team.remove',
      'admin.system.settings',
      'admin.audit.view',
      // Special permissions
      'employee.payments.view',
    ]
  },
  
  custom: {
    name: 'Custom Role',
    description: 'Custom permissions defined by admin',
    color: '#6b7280', // Gray
    accessibleTabs: [],
    permissions: []
  }
};

// 3. TEAM MEMBER MODEL DESIGN
const TeamMemberSchema = {
  // Basic Info
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  passwordHash: { type: String, required: true },
  
  // Role & Permissions
  roleType: { 
    type: String, 
    enum: Object.keys(TEAM_ROLES),
    default: 'custom' 
  },
  accessibleTabs: {
    type: [String],
    enum: ['employee', 'hiring', 'admin'],
    default: []
  },
  permissions: {
    type: [String],
    default: []
  },
  
  // Team Management
  invitedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'TeamMember' 
  },
  teamLead: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TeamMember'
  },
  
  // Status & Settings
  isActive: { type: Boolean, default: true },
  lastLogin: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// 4. PATRICK'S TEAM STRUCTURE
const PATRICK_TEAM_EXAMPLE = {
  patrick: {
    name: 'Patrick',
    role: 'super_admin', // CEO with full access
    accessibleTabs: ['employee', 'hiring', 'admin'],
    canManageTeam: true
  },
  
  julia: {
    name: 'Julia', 
    role: 'full_admin',
    accessibleTabs: ['employee', 'hiring'],
    canManageTeam: false
  },
  
  akula: {
    name: 'Akula',
    role: 'hiring_manager', 
    accessibleTabs: ['hiring'],
    canManageTeam: false
  },
  
  marko: {
    name: 'Marko',
    role: 'custom', // Custom role
    accessibleTabs: ['employee', 'hiring'],
    permissions: [
      'employee.dashboard.view',
      'employee.reports.view',
      'hiring.dashboard.view',
      'hiring.applications.view'
    ],
    canManageTeam: false
  }
};

export {
  PERMISSION_MODULES,
  TEAM_ROLES, 
  TeamMemberSchema,
  PATRICK_TEAM_EXAMPLE
};