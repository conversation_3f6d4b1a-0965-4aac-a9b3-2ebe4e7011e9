import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, useLocation, useNavigate } from "react-router-dom";
import {
  HiHome as HomeIcon,
  HiUsers as UsersIcon,
  HiSpeakerphone as SpeakerphoneIcon,
  HiDocumentText as DocumentReportIcon,
  HiClipboardList as ClipboardListIcon,
  HiLogout as LogoutIcon,
  <PERSON><PERSON><PERSON> as UserIcon,
  HiChartBar as ChartBarIcon,
  HiBell,
  HiSearch,
  HiDocumentText,
  HiClipboardList,
  HiLogout,
  HiBookOpen as BookOpenIcon,
  HiChat as ChatIcon,
  HiVideoCamera,
  HiCalendar,
  HiCurrencyDollar,
  HiShieldCheck,
  HiBriefcase,
  HiUserAdd,
  HiClipboard,
  HiTrendingUp,
  HiPhotograph,
} from "react-icons/hi";
import backgroundImage from "../../images/background.png";
import {
  PanelLeftClose,
  PanelRightClose,
  Menu,
  MessageSquare,
} from "lucide-react";
import { useAuthStore } from "../../store/authStore";
import {
  getUserAccessibleTabs,
  canAccessTab,
  canAccessTalentPortal,
} from "../../utils/rolePermissions";
import {
  hasDetailedPermission,
  PERMISSION_MATRIX,
} from "../../utils/permissionMatrix";
import { ROUTE_PERMISSIONS } from "../../utils/routePermissions";

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "../ui/sidebar";

const AdminLayout = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout, accessToken } = useAuthStore();

  // Check tab access using new permission system
  const canAccessEmployeeTab = (user) => {
    // Check if user has any employee module permissions
    return Object.keys(PERMISSION_MATRIX.employee.features).some(
      (featureKey) => {
        return Object.keys(
          PERMISSION_MATRIX.employee.features[featureKey].actions
        ).some((actionKey) => {
          const permission = `employee.${featureKey}.${actionKey}`;
          return hasDetailedPermission(user, permission);
        });
      }
    );
  };

  const canAccessHiringTab = (user) => {
    // Check if user has any hiring module permissions
    return Object.keys(PERMISSION_MATRIX.hiring.features).some((featureKey) => {
      return Object.keys(
        PERMISSION_MATRIX.hiring.features[featureKey].actions
      ).some((actionKey) => {
        const permission = `hiring.${featureKey}.${actionKey}`;
        return hasDetailedPermission(user, permission);
      });
    });
  };

  const canAccessAdminTab = (user) => {
    // Check if user has any admin module permissions
    return Object.keys(PERMISSION_MATRIX.admin.features).some((featureKey) => {
      return Object.keys(
        PERMISSION_MATRIX.admin.features[featureKey].actions
      ).some((actionKey) => {
        const permission = `admin.${featureKey}.${actionKey}`;
        return hasDetailedPermission(user, permission);
      });
    });
  };

  // Get user's accessible tabs using new permission system
  const getAccessibleTabs = (user) => {
    const tabs = [];
    if (canAccessEmployeeTab(user)) tabs.push("employee");
    if (canAccessHiringTab(user)) tabs.push("hiring");
    if (canAccessAdminTab(user)) tabs.push("admin");
    return tabs;
  };

  const accessibleTabs = getAccessibleTabs(user);

  // Determine view mode based on route and user permissions
  const isHiringSection = location.pathname.startsWith("/admin/hiring");
  const isAdminSection = location.pathname.startsWith("/admin/admin-panel");

  // Smart default viewMode based on user's accessible tabs
  const getDefaultViewMode = () => {
    if (isHiringSection) return "hiring";
    if (isAdminSection) return "admin";

    // If user only has hiring access, default to hiring
    if (accessibleTabs.length === 1 && accessibleTabs[0] === "hiring") {
      return "hiring";
    }

    // If user only has admin access, default to admin
    if (accessibleTabs.length === 1 && accessibleTabs[0] === "admin") {
      return "admin";
    }

    // Otherwise default to employee (most common case)
    return "employee";
  };

  const [viewMode, setViewMode] = useState(getDefaultViewMode());

  // Sidebar collapse state
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  // Avatar and background editable by admin (persisted to localStorage)
  const [avatarUrl, setAvatarUrl] = useState(
    () =>
      localStorage.getItem("admin_avatar") ||
      "https://mynaui.com/avatars/avatar-02.jpg"
  );
  const [bgImage, setBgImage] = useState(
    () => localStorage.getItem("admin_bg") || backgroundImage
  );
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [tempAvatar, setTempAvatar] = useState(null);
  const [tempBg, setTempBg] = useState(null);
  // Mobile sidebar state
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  // Mobile search overlay state
  const [mobileSearchOpen, setMobileSearchOpen] = useState(false);
  const mobileSearchRef = useRef(null);

  // Task action count
  const [taskActionCount, setTaskActionCount] = useState(0);

  // Update view mode when route changes
  useEffect(() => {
    if (location.pathname.startsWith("/admin/hiring")) {
      setViewMode("hiring");
    } else if (location.pathname.startsWith("/admin/admin-panel")) {
      setViewMode("admin");
    } else {
      // Smart routing: redirect users who don't have employee access
      if (!canAccessEmployeeTab(user)) {
        // If user can't access employee section but can access hiring, redirect to hiring
        if (canAccessHiringTab(user)) {
          console.log(
            "🔄 User cannot access employee section, redirecting to hiring"
          );
          navigate("/admin/hiring/dashboard");
          setViewMode("hiring");
          return;
        }
        // If user can't access employee section but can access admin, redirect to admin
        if (canAccessAdminTab(user)) {
          console.log(
            "🔄 User cannot access employee section, redirecting to admin panel"
          );
          navigate("/admin/admin-panel/dashboard");
          setViewMode("admin");
          return;
        }
      }
      setViewMode("employee");
    }
  }, [location.pathname, user, navigate]);

  // Handle initial redirect for users without employee access
  useEffect(() => {
    // Only run this check once when component mounts and user is available
    if (!user) return;

    // If user is on employee dashboard but doesn't have employee access, redirect
    if (
      location.pathname === "/admin/dashboard" &&
      !canAccessEmployeeTab(user)
    ) {
      if (canAccessHiringTab(user)) {
        console.log(
          "🔄 Initial redirect: User cannot access employee dashboard, redirecting to hiring"
        );
        navigate("/admin/hiring/dashboard", { replace: true });
      } else if (canAccessAdminTab(user)) {
        console.log(
          "🔄 Initial redirect: User cannot access employee dashboard, redirecting to admin panel"
        );
        navigate("/admin/admin-panel/dashboard", { replace: true });
      }
    }
  }, [user]); // Only run when user changes (not on every route change)

  // Fetch task action count
  useEffect(() => {
    if (!accessToken || viewMode === "hiring") return;

    const fetchTaskCount = async () => {
      try {
        const response = await fetch(
          `${API_URL}/api/tasks/admin/needs-action?filter=all`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        );
        if (response.ok) {
          const data = await response.json();
          setTaskActionCount(data.counts?.total || 0);
        }
      } catch (error) {
        console.error("Failed to fetch task count:", error);
      }
    };

    fetchTaskCount();
  }, [accessToken, viewMode]);

  // Filter navigation items based on granular permissions

  // Filter navigation items based on granular permissions - using centralized mapping
  const getFilteredNavigation = () => {
    const navigationWithRoutes = [
      { name: "Dashboard", href: "/admin/dashboard", icon: HomeIcon },
      { name: "Talents", href: "/admin/talents", icon: UsersIcon },
      { name: "Leaderboard", href: "/admin/leaderboard", icon: HiTrendingUp },
      {
        name: "Weekly Summaries",
        href: "/admin/weekly-summaries",
        icon: ChartBarIcon,
      },
      {
        name: "Announcements",
        href: "/admin/announcements",
        icon: SpeakerphoneIcon,
      },
      { name: "Notifications", href: "/admin/notifications", icon: HiBell },
      { name: "Meetings", href: "/admin/meetings", icon: HiVideoCamera },
      {
        name: "Attendance",
        href: "/admin/meetings/manage",
        icon: HiClipboardList,
      },
      { name: "Leave", href: "/admin/leave", icon: HiCalendar },
      { name: "Resources", href: "/admin/resources", icon: BookOpenIcon },
      { name: "Documents", href: "/admin/documents", icon: HiDocumentText },
      { name: "Reports", href: "/admin/reports", icon: DocumentReportIcon },
      { name: "Tasks", href: "/admin/tasks", icon: ClipboardListIcon },
      { name: "Check-ups", href: "/admin/checkups", icon: ClipboardListIcon },
      {
        name: "Communication",
        href: "/admin/communication-center",
        icon: MessageSquare,
      },
      {
        name: "Employee Analytics",
        href: "/admin/employee-analytics",
        icon: ChartBarIcon,
      },
      {
        name: "Screenshot Issues",
        href: "/admin/screenshot-discrepancies",
        icon: HiPhotograph,
      },
      {
        name: "AI Feedback Monitor",
        href: "/admin/ai-feedback",
        icon: ChatIcon,
      },
      { name: "Compliance", href: "/admin/compliance", icon: HiCurrencyDollar },
      { name: "Audit Trail", href: "/admin/audit-trail", icon: HiShieldCheck },
      { name: "Payments", href: "/admin/payments", icon: HiCurrencyDollar },
    ];

    // Debug logging for permission filtering
    console.log("🔍 [AdminLayout] Permission Filtering Debug:", {
      userEmail: user?.email,
      userPermissionMode: user?.permissionMode,
      userCustomPermissions: user?.customPermissions,
      userRoleType: user?.roleType,
    });

    // Filter items based on user's permissions using centralized route mapping
    const filteredItems = navigationWithRoutes.filter((item) => {
      // Get required permission from centralized mapping
      const requiredPermission = ROUTE_PERMISSIONS[item.href];

      if (!requiredPermission) {
        console.log(
          `🔍 Navigation item "${item.name}" (${item.href}): ✅ NO PERMISSION REQUIRED`
        );
        return true; // No permission required
      }

      const hasPermission = hasDetailedPermission(user, requiredPermission);
      console.log(
        `🔍 Navigation item "${item.name}" (${
          item.href
        }) [${requiredPermission}]: ${
          hasPermission ? "✅ ALLOWED" : "❌ DENIED"
        }`
      );
      return hasPermission;
    });

    console.log(
      "🔍 [AdminLayout] Filtered navigation items:",
      filteredItems.map((item) => item.name)
    );
    return filteredItems;
  };

  const navigation = getFilteredNavigation();

  // Hiring Navigation with permission filtering using centralized mapping
  const getFilteredHiringNavigation = () => {
    const hiringNavigationWithRoutes = [
      { name: "Dashboard", href: "/admin/hiring/dashboard", icon: HomeIcon },
      { name: "Jobs", href: "/admin/hiring/jobs", icon: HiBriefcase },
      {
        name: "Applications",
        href: "/admin/hiring/applications",
        icon: HiUserAdd,
      },
      {
        name: "Interviews",
        href: "/admin/hiring/interviews",
        icon: HiVideoCamera,
      },
      {
        name: "Onboarding",
        href: "/admin/hiring/onboarding",
        icon: HiClipboard,
      },
    ];

    return hiringNavigationWithRoutes.filter((item) => {
      const requiredPermission = ROUTE_PERMISSIONS[item.href];
      return (
        !requiredPermission || hasDetailedPermission(user, requiredPermission)
      );
    });
  };

  const hiringNavigation = getFilteredHiringNavigation();

  // Admin Panel Navigation with permission filtering using centralized mapping
  const getFilteredAdminNavigation = () => {
    const adminNavigationWithRoutes = [
      {
        name: "Dashboard",
        href: "/admin/admin-panel/dashboard",
        icon: HomeIcon,
      },
      { name: "Talents", href: "/admin/admin-panel/talents", icon: UsersIcon },
      {
        name: "Announcements",
        href: "/admin/admin-panel/announcements",
        icon: SpeakerphoneIcon,
      },
      {
        name: "Payments",
        href: "/admin/admin-panel/payments",
        icon: HiCurrencyDollar,
      },
      {
        name: "Communications",
        href: "/admin/admin-panel/communications",
        icon: MessageSquare,
      },
      {
        name: "Meetings",
        href: "/admin/admin-panel/meetings",
        icon: HiVideoCamera,
      },
      {
        name: "Notifications",
        href: "/admin/admin-panel/notifications",
        icon: HiBell,
      },
      {
        name: "Audit Trail",
        href: "/admin/admin-panel/audit-trail",
        icon: HiShieldCheck,
      },
      {
        name: "Team Management",
        href: "/admin/admin-panel/team-management",
        icon: HiUserAdd,
      },
    ];

    return adminNavigationWithRoutes.filter((item) => {
      const requiredPermission = ROUTE_PERMISSIONS[item.href];
      return (
        !requiredPermission || hasDetailedPermission(user, requiredPermission)
      );
    });
  };

  const adminNavigation = getFilteredAdminNavigation();

  const settingsNavigation = [
    { name: "Reports", href: "/admin/reports", icon: HiDocumentText },
    { name: "Tasks", href: "/admin/tasks", icon: HiClipboardList },
    { name: "Check-ups", href: "/admin/checkups", icon: HiClipboardList },
  ];

  const handleViewModeChange = (mode) => {
    console.log("🔄 [AdminLayout] View mode change:", {
      from: viewMode,
      to: mode,
    });
    setViewMode(mode);
    if (mode === "hiring") {
      console.log("🔄 [AdminLayout] Navigating to hiring dashboard...");
      navigate("/admin/hiring/dashboard");
    } else if (mode === "admin") {
      console.log("🔄 [AdminLayout] Navigating to admin panel dashboard...");
      navigate("/admin/admin-panel/dashboard");
    } else {
      console.log("🔄 [AdminLayout] Navigating to employee dashboard...");
      navigate("/admin/dashboard");
    }
  };

  // Compress data URL images to reduce localStorage size
  const compressDataUrl = (dataUrl, maxWidth = 1200, quality = 0.75) => {
    return new Promise((resolve) => {
      try {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement("canvas");
          let { width, height } = img;
          if (width > maxWidth) {
            const ratio = maxWidth / width;
            width = maxWidth;
            height = height * ratio;
          }
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext("2d");
          ctx.drawImage(img, 0, 0, width, height);
          const compressed = canvas.toDataURL("image/jpeg", quality);
          resolve(compressed);
        };
        img.onerror = () => resolve(dataUrl);
        img.src = dataUrl;
      } catch (err) {
        console.error("compressDataUrl error", err);
        resolve(dataUrl);
      }
    });
  };
  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const isActive = (href) => {
    return (
      location.pathname === href || location.pathname.startsWith(href + "/")
    );
  };

  // Debug logging for admin access
  console.log("🔍 Admin Tab Visibility Check:", {
    userName: user?.name,
    userRoles: user?.roles,
    userType: user?.userType,
    accessibleTabs,
    canAccessAdminTab: canAccessAdminTab(user),
  });

  // Apply talent-style background when in employee, hiring, or admin view for visual parity
  // Previously background was disabled for document management pages; enable it now
  const showBg =
    viewMode === "employee" || viewMode === "hiring" || viewMode === "admin";
  const rootStyle = showBg
    ? {
        backgroundImage: `url(${bgImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        backgroundAttachment: "fixed",
      }
    : {};

  return (
    <div className="min-h-screen flex relative" style={rootStyle}>
      {/* Dark overlay for better contrast when using background image */}
      {showBg && (
        <div className="absolute inset-0 bg-black/40 pointer-events-none" />
      )}

      {/* Radial gradient accents (same as TalentLayout) */}
      {showBg && (
        <>
          <div
            className="absolute top-0 right-0 w-96 h-full pointer-events-none"
            style={{
              background:
                "radial-gradient(ellipse at top right, rgba(59, 130, 246, 0.08), transparent 60%)",
            }}
          />
          <div
            className="absolute top-0 left-0 w-64 h-full pointer-events-none"
            style={{
              background:
                "radial-gradient(ellipse at top left, rgba(99, 102, 241, 0.05), transparent 60%)",
            }}
          />
        </>
      )}
      {/* Mobile Overlay */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-background/80 backdrop-blur-sm z-40 lg:hidden"
          onClick={() => setMobileSidebarOpen(false)}
        />
      )}

      {/* Prodex-style Sidebar */}
      <div
        className={`${
          sidebarCollapsed ? "lg:w-16" : "lg:w-60"
        } fixed lg:sticky top-0 left-0 h-screen flex flex-col border-r border-border/40 bg-card/85 backdrop-blur-xl transition-all duration-300 z-50 w-60 ${
          mobileSidebarOpen
            ? "translate-x-0"
            : "-translate-x-full lg:translate-x-0"
        }`}
      >
        <Sidebar>
          {/* Logo Header */}
          <SidebarHeader>
            <div
              className={`flex items-center w-full ${
                sidebarCollapsed ? "justify-center" : "justify-between"
              }`}
            >
              {!sidebarCollapsed && (
                <span className="text-lg font-semibold text-foreground">
                  {viewMode === "hiring"
                    ? "Hiring Portal"
                    : viewMode === "admin"
                    ? "Admin Panel"
                    : "Admin Dashboard"}
                </span>
              )}
              {/* Desktop Collapse Button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setSidebarCollapsed(!sidebarCollapsed);
                }}
                className={`hidden lg:block p-1.5 text-muted-foreground hover:text-foreground hover:bg-accent rounded-md transition-colors ${
                  sidebarCollapsed ? "" : "ml-auto"
                }`}
                title={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              >
                {sidebarCollapsed ? (
                  <PanelRightClose className="w-5 h-5" />
                ) : (
                  <PanelLeftClose className="w-5 h-5" />
                )}
              </button>
              {/* Mobile Close Button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setMobileSidebarOpen(false);
                }}
                className="lg:hidden p-1.5 text-muted-foreground hover:text-foreground hover:bg-accent rounded-md transition-colors ml-auto"
                title="Close sidebar"
              >
                <PanelLeftClose className="w-5 h-5" />
              </button>
            </div>
          </SidebarHeader>

          {/* Navigation Content */}
          <SidebarContent>
            {/* Main Navigation */}
            <SidebarGroup>
              {!sidebarCollapsed && (
                <SidebarGroupLabel>
                  {viewMode === "hiring"
                    ? "HIRING"
                    : viewMode === "admin"
                    ? "ADMIN"
                    : "MAIN"}
                </SidebarGroupLabel>
              )}
              <SidebarGroupContent>
                <SidebarMenu>
                  {(viewMode === "hiring"
                    ? hiringNavigation
                    : viewMode === "admin"
                    ? adminNavigation
                    : navigation
                  ).map((item) => (
                    <SidebarMenuItem key={item.name}>
                      <Link
                        to={item.href}
                        title={sidebarCollapsed ? item.name : ""}
                        onClick={() => setMobileSidebarOpen(false)}
                      >
                        <SidebarMenuButton isActive={isActive(item.href)}>
                          <item.icon className="h-5 w-5 flex-shrink-0" />
                          {!sidebarCollapsed && (
                            <span className="flex items-center gap-2 flex-1">
                              <span>{item.name}</span>
                              {item.name === "Tasks" && taskActionCount > 0 && (
                                <span className="ml-auto px-2 py-0.5 rounded-full bg-amber-500 text-white text-xs font-semibold">
                                  {taskActionCount}
                                </span>
                              )}
                            </span>
                          )}
                        </SidebarMenuButton>
                      </Link>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            {/* Settings Navigation */}
            {/* <SidebarGroup>
              <SidebarGroupLabel>SETTINGS</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {settingsNavigation.map((item) => (
                    <SidebarMenuItem key={item.name}>
                      <Link to={item.href}>
                        <SidebarMenuButton isActive={isActive(item.href)}>
                          <item.icon className="h-5 w-5 flex-shrink-0" />
                          <span>{item.name}</span>
                        </SidebarMenuButton>
                      </Link>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup> */}
          </SidebarContent>

          {/* Footer */}
          <SidebarFooter>
            {/* User Profile */}
            <div
              className={`flex items-center gap-2 px-3 py-2 ${
                sidebarCollapsed ? "flex-col" : ""
              }`}
            >
              <div className="w-8 h-8 bg-muted/50 rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden">
                <img
                  src={avatarUrl}
                  alt="Admin"
                  className="w-full h-full object-cover"
                />
              </div>
              {!sidebarCollapsed && (
                <>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground truncate">
                      {user?.name || "Admin"}
                    </p>
                    <p className="text-xs text-muted-foreground truncate">
                      {user?.email || "<EMAIL>"}
                    </p>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleLogout();
                    }}
                    className="p-1.5 text-muted-foreground hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20 rounded transition-colors"
                    title="Logout"
                  >
                    <HiLogout className="w-4 h-4" />
                  </button>
                  {/* Settings button next to logout in footer */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSettingsOpen(true);
                    }}
                    className="p-1.5 text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors ml-1"
                    title="Settings"
                  >
                    <HiPhotograph className="w-4 h-4" />
                  </button>
                </>
              )}
              {sidebarCollapsed && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLogout();
                  }}
                  className="p-1.5 text-muted-foreground hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20 rounded transition-colors"
                  title="Logout"
                >
                  <HiLogout className="w-4 h-4" />
                </button>
              )}
            </div>
          </SidebarFooter>
        </Sidebar>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navbar - Mac Style */}
        <header className="bg-card/30 backdrop-blur-xl border-b border-border/40 sticky top-0 z-30">
          <div className="flex items-center justify-between px-3 md:px-4 lg:px-6 py-2 md:py-2">
            {/* Left Side - Mobile Menu + View Mode Toggle */}
            <div className="flex items-center gap-2 md:gap-4">
              {/* Mobile Menu Button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setMobileSidebarOpen(true);
                }}
                className="lg:hidden p-2 text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-lg transition-colors"
                title="Open menu"
              >
                <Menu className="w-5 h-5" />
              </button>

              {/* View Mode Toggle - Only show tabs user has access to */}
              <div className="flex items-center bg-muted rounded-lg p-0.5 md:p-1">
                {canAccessEmployeeTab(user) && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewModeChange("employee");
                    }}
                    className={`px-2 md:px-4 py-1.5 md:py-2 rounded-md text-xs md:text-sm font-medium transition-all ${
                      viewMode === "employee"
                        ? "bg-background text-foreground shadow-sm"
                        : "text-muted-foreground hover:text-foreground"
                    }`}
                  >
                    <div className="flex items-center gap-1 md:gap-2">
                      <UsersIcon className="w-3 h-3 md:w-4 md:h-4" />
                      <span className="hidden sm:inline">Employee</span>
                    </div>
                  </button>
                )}
                {canAccessHiringTab(user) && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewModeChange("hiring");
                    }}
                    className={`px-2 md:px-4 py-1.5 md:py-2 rounded-md text-xs md:text-sm font-medium transition-all ${
                      viewMode === "hiring"
                        ? "bg-background text-foreground shadow-sm"
                        : "text-muted-foreground hover:text-foreground"
                    }`}
                  >
                    <div className="flex items-center gap-1 md:gap-2">
                      <HiBriefcase className="w-3 h-3 md:w-4 md:h-4" />
                      <span className="hidden sm:inline">Hiring</span>
                    </div>
                  </button>
                )}
                {canAccessAdminTab(user) && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewModeChange("admin");
                    }}
                    className={`px-2 md:px-4 py-1.5 md:py-2 rounded-md text-xs md:text-sm font-medium transition-all ${
                      viewMode === "admin"
                        ? "bg-background text-foreground shadow-sm"
                        : "text-muted-foreground hover:text-foreground"
                    }`}
                  >
                    <div className="flex items-center gap-1 md:gap-2">
                      <HiShieldCheck className="w-3 h-3 md:w-4 md:h-4" />
                      <span className="hidden sm:inline">Admin</span>
                    </div>
                  </button>
                )}
              </div>
            </div>

            {/* Right Side - Search, Avatars, Notifications, Profile */}
            <div className="flex items-center gap-2 md:gap-3 lg:gap-4">
              {/* Team Avatars with + button - Hidden on mobile */}
              <div className="hidden md:flex items-center -space-x-2">
                <div className="w-7 h-7 md:w-8 md:h-8 rounded-full bg-muted border-2 border-card flex items-center justify-center">
                  <span className="text-xs text-foreground font-medium">A</span>
                </div>
                <div className="w-7 h-7 md:w-8 md:h-8 rounded-full bg-muted border-2 border-card flex items-center justify-center">
                  <span className="text-xs text-foreground font-medium">B</span>
                </div>
                <button
                  onClick={(e) => e.stopPropagation()}
                  className="w-7 h-7 md:w-8 md:h-8 rounded-full bg-muted border-2 border-card flex items-center justify-center hover:bg-muted/70 transition-colors"
                >
                  <span className="text-sm text-muted-foreground font-medium">
                    +
                  </span>
                </button>
              </div>

              {/* Notification Bell */}
              <button
                onClick={(e) => e.stopPropagation()}
                className="relative p-1.5 md:p-2 text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-lg transition-colors"
              >
                <HiBell className="w-4 h-4 md:w-5 md:h-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-foreground/60 rounded-full"></span>
              </button>

              {/* Search - Hidden on mobile, shown on larger screens */}
              <div className="relative hidden lg:block">
                <HiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search anything"
                  onClick={(e) => e.stopPropagation()}
                  className="pl-10 pr-12 py-1.5 w-48 xl:w-64 border border-border/40 rounded-lg bg-muted/30 text-sm text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-muted/50 focus:border-border transition-all"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground font-medium">
                  ⌘K
                </span>
              </div>

              {/* Search Icon for Mobile */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setMobileSearchOpen(true);
                }}
                aria-label="Open search"
                className="lg:hidden p-1.5 md:p-2 text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-lg transition-colors"
              >
                <HiSearch className="w-4 h-4 md:w-5 md:h-5" />
              </button>

              {/* User Avatar with Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    onClick={(e) => e.stopPropagation()}
                    className="w-7 h-7 md:w-8 md:h-8 rounded-full bg-muted/50 flex items-center justify-center hover:shadow-md transition-all focus:outline-none focus:ring-2 focus:ring-muted/50 overflow-hidden"
                  >
                    <img
                      src={avatarUrl}
                      alt="User"
                      className="w-full h-full object-cover"
                    />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-44">
                  <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium">
                        {user?.name || "Admin"}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {user?.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  {/* Back to Talent Dashboard - Only for users with talent portal access */}
                  {canAccessTalentPortal(user) && (
                    <>
                      <DropdownMenuItem
                        onClick={() => navigate("/talent/dashboard")}
                        className="cursor-pointer"
                      >
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M7 16l-4-4m0 0l4-4m-4 4h18"
                          />
                        </svg>
                        Back to Talent Dashboard
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                    </>
                  )}

                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleLogout();
                    }}
                    className="cursor-pointer text-red-600 focus:text-red-600"
                  >
                    <HiLogout className="w-4 h-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      setSettingsOpen(true);
                    }}
                    className="cursor-pointer"
                  >
                    <HiPhotograph className="w-4 h-4 mr-2" />
                    Settings
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Page Content */}
        {/* Mobile Search Overlay (appears under navbar) */}
        {mobileSearchOpen && (
          <div className="lg:hidden fixed inset-x-0 top-14 z-40 px-4">
            <div
              className="bg-card/95 border border-border/40 rounded-lg p-3 shadow-md backdrop-blur-md"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center gap-2">
                <HiSearch className="w-4 h-4 text-muted-foreground" />
                <input
                  ref={mobileSearchRef}
                  autoFocus
                  type="text"
                  placeholder="Search anything"
                  className="flex-1 pl-1 pr-3 py-2 border-0 bg-transparent text-sm text-foreground placeholder:text-muted-foreground focus:outline-none"
                  onKeyDown={(e) => {
                    if (e.key === "Escape") setMobileSearchOpen(false);
                  }}
                  onClick={(e) => e.stopPropagation()}
                />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setMobileSearchOpen(false);
                  }}
                  className="text-muted-foreground hover:text-foreground p-1 rounded"
                  aria-label="Close search"
                >
                  ✕
                </button>
              </div>
            </div>
          </div>
        )}

        <main className="flex-1 overflow-y-auto">{children}</main>
      </div>
      {/* Settings Modal */}
      {settingsOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center"
          onClick={() => setSettingsOpen(false)}
        >
          <div className="absolute inset-0 bg-black/50" aria-hidden />
          <div
            className="relative bg-card border border-border/40 rounded-lg p-4 w-full max-w-md shadow-lg z-10"
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-lg font-semibold mb-4">Admin Settings</h3>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium mb-2">Avatar Image</p>
                <div className="flex items-center gap-3">
                  <div className="w-14 h-14 rounded-full overflow-hidden bg-muted/50">
                    <img
                      src={tempAvatar || avatarUrl}
                      alt="preview"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={async (e) => {
                      const file = e.target.files?.[0];
                      if (!file) return;
                      const reader = new FileReader();
                      reader.onload = () => setTempAvatar(reader.result);
                      reader.readAsDataURL(file);
                    }}
                  />
                </div>
              </div>

              <div>
                <p className="text-sm font-medium mb-2">Background Image</p>
                <div className="flex items-center gap-3">
                  <div className="w-28 h-16 rounded overflow-hidden bg-muted/50">
                    <img
                      src={tempBg || bgImage}
                      alt="bg-preview"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (!file) return;
                      const reader = new FileReader();
                      reader.onload = () => setTempBg(reader.result);
                      reader.readAsDataURL(file);
                    }}
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <button
                  onClick={() => {
                    setTempAvatar(null);
                    setTempBg(null);
                    setSettingsOpen(false);
                  }}
                  className="px-2 py-1.5 rounded bg-muted/20 text-muted-foreground"
                >
                  Cancel
                </button>
                <button
                  onClick={async () => {
                    // Compress and save avatar
                    try {
                      if (tempAvatar) {
                        const compressedAvatar = await compressDataUrl(
                          tempAvatar,
                          600,
                          0.8
                        );
                        setAvatarUrl(compressedAvatar);
                        try {
                          localStorage.setItem(
                            "admin_avatar",
                            compressedAvatar
                          );
                        } catch (err) {
                          console.warn(
                            "localStorage admin_avatar failed, trying sessionStorage",
                            err
                          );
                          try {
                            sessionStorage.setItem(
                              "admin_avatar",
                              compressedAvatar
                            );
                          } catch (err2) {
                            console.error(
                              "sessionStorage admin_avatar failed",
                              err2
                            );
                            alert(
                              "Unable to save avatar locally (storage quota). Use a smaller image or server-side upload."
                            );
                          }
                        }
                      }

                      if (tempBg) {
                        const compressedBg = await compressDataUrl(
                          tempBg,
                          1400,
                          0.7
                        );
                        setBgImage(compressedBg);
                        try {
                          localStorage.setItem("admin_bg", compressedBg);
                        } catch (err) {
                          console.warn(
                            "localStorage admin_bg failed, trying sessionStorage",
                            err
                          );
                          try {
                            sessionStorage.setItem("admin_bg", compressedBg);
                          } catch (err2) {
                            console.error(
                              "sessionStorage admin_bg failed",
                              err2
                            );
                            alert(
                              "Unable to save background locally (storage quota). Use a smaller image or server-side upload."
                            );
                          }
                        }
                      }
                    } catch (error) {
                      console.error("Error saving settings:", error);
                      alert("Failed to save settings. Try a smaller image.");
                    } finally {
                      setTempAvatar(null);
                      setTempBg(null);
                      setSettingsOpen(false);
                    }
                  }}
                  className="px-2 py-1.5 rounded bg-primary text-white"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminLayout;
