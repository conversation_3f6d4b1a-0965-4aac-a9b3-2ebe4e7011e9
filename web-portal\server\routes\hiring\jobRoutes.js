import express from "express";
import {
  createJob,
  getAllJobs,
  getJobById,
  updateJob,
  publishJob,
  closeJob,
  archiveJob,
  duplicateJob,
  getJobStats,
  getJobBySlug,
  incrementViewCount,
} from "../../controllers/hiring/jobController.js";
import { authenticateToken } from "../../middleware/auth.js";
import { requireAdmin } from "../../middleware/adminAuth.js";
import uploadJobImages, {
  handleMulterError,
} from "../../middleware/uploadJobImages.js";

const router = express.Router();

// ==================== ADMIN ROUTES ====================
// All admin routes require authentication + admin role

// Get job statistics (must be before /:id to avoid route conflict)
router.get("/stats", authenticateToken, requireAdmin, getJobStats);

// CRUD operations
router.post(
  "/",
  authenticateToken,
  requireAdmin,
  uploadJobImages.fields([
    { name: "backgroundImage", maxCount: 1 },
    { name: "bannerImage", maxCount: 1 },
  ]),
  handleMulterError,
  createJob
);
router.get("/", authenticateToken, requireAdmin, getAllJobs);
router.get("/:id", authenticateToken, requireAdmin, getJobById);
router.put("/:id", authenticateToken, requireAdmin, updateJob);

// Status transitions
router.patch("/:id/publish", authenticateToken, requireAdmin, publishJob);
router.patch("/:id/close", authenticateToken, requireAdmin, closeJob);
router.patch("/:id/archive", authenticateToken, requireAdmin, archiveJob);

// Utility operations
router.post("/:id/duplicate", authenticateToken, requireAdmin, duplicateJob);

// ==================== PUBLIC ROUTES ====================
// These routes are for public access (candidates viewing jobs)
// Mounted separately in main server file

export const publicJobRoutes = express.Router();

// Get job by slug (public application page)
publicJobRoutes.get("/apply/:slug", getJobBySlug);

// Increment view count
publicJobRoutes.post("/:slug/view", incrementViewCount);

// Export admin routes as default
export default router;
