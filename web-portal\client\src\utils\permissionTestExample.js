/**
 * Permission System Test Examples
 * Demonstrates how the new granular permission system works
 */

import { hasDetailedPermission, PERMISSION_MATRIX } from './permissionMatrix';

// <PERSON>'s example profile - only has view access to talents
const juliaUser = {
  name: '<PERSON>',
  email: '<EMAIL>',
  roleType: 'custom', // Using custom permissions
  permissionMode: 'custom',
  customPermissions: [
    'employee.talents.view'  // Only this permission
  ]
};

// <PERSON>'s example profile - Super Admin with all access
const patrickUser = {
  name: '<PERSON>',
  email: '<EMAIL>', 
  roleType: 'super_admin',
  permissionMode: 'role' // Using role template
};

// Test function to check what <PERSON> can access
export const testJuliaAccess = () => {
  console.log('\n🔍 Testing Julia\'s Access:');
  console.log('==========================================');
  
  // Test individual permissions
  const permissions = [
    'employee.talents.view',     // ✅ Should have access
    'employee.talents.edit',     // ❌ Should NOT have access
    'employee.leaderboard.view', // ❌ Should NOT have access
    'employee.payments.view',    // ❌ Should NOT have access
    'employee.reports.view',     // ❌ Should NOT have access
    'hiring.jobs.view',          // ❌ Should NOT have access
    'admin.team_management.view' // ❌ Should NOT have access
  ];

  permissions.forEach(permission => {
    const hasAccess = hasDetailedPermission(juliaUser, permission);
    console.log(`${hasAccess ? '✅' : '❌'} ${permission}: ${hasAccess ? 'ALLOWED' : 'DENIED'}`);
  });
  
  // Test what navigation items Julia should see
  console.log('\n🗂️ Navigation Items Julia Should See:');
  console.log('==========================================');
  
  const navigationItems = [
    { name: 'Dashboard', permission: 'employee.talents.view' },
    { name: 'Talents', permission: 'employee.talents.view' },
    { name: 'Leaderboard', permission: 'employee.leaderboard.view' },
    { name: 'Reports', permission: 'employee.reports.view' },
    { name: 'Tasks', permission: 'employee.tasks.view' },
    { name: 'Meetings', permission: 'employee.meetings.view' },
    { name: 'Payments', permission: 'employee.payments.view' }
  ];
  
  const visibleItems = navigationItems.filter(item => 
    hasDetailedPermission(juliaUser, item.permission)
  );
  
  console.log('Visible Navigation Items:', visibleItems.map(item => item.name));
  console.log(`Julia can see ${visibleItems.length} out of ${navigationItems.length} navigation items`);
  
  return {
    totalPermissions: juliaUser.customPermissions.length,
    visibleNavigationItems: visibleItems.length,
    allowedItems: visibleItems.map(item => item.name)
  };
};

// Test function to compare Julia vs Patrick access
export const compareUserAccess = () => {
  console.log('\n👥 Comparing User Access Levels:');
  console.log('==========================================');
  
  const testPermissions = [
    'employee.talents.view',
    'employee.talents.edit', 
    'employee.payments.view',
    'hiring.jobs.create',
    'admin.team_management.edit'
  ];
  
  console.log('Permission\t\t\tJulia\tPatrick');
  console.log('----------------------------------------------------');
  
  testPermissions.forEach(permission => {
    const juliaAccess = hasDetailedPermission(juliaUser, permission);
    const patrickAccess = hasDetailedPermission(patrickUser, permission);
    
    console.log(`${permission}\t${juliaAccess ? '✅' : '❌'}\t${patrickAccess ? '✅' : '❌'}`);
  });
};

// Expected result for Julia:
// ✅ employee.talents.view: ALLOWED  
// ❌ employee.talents.edit: DENIED
// ❌ employee.leaderboard.view: DENIED
// ❌ employee.payments.view: DENIED
// ❌ employee.reports.view: DENIED
// ❌ hiring.jobs.view: DENIED
// ❌ admin.team_management.view: DENIED

// Visible Navigation: Dashboard, Talents (2 items only)

export default {
  testJuliaAccess,
  compareUserAccess,
  juliaUser,
  patrickUser
};