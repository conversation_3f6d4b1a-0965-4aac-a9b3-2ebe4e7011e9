import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import AdminLayout from "../../../components/admin/AdminLayout";
import { useAuthStore } from "../../../store/authStore";
import { getUserRole } from "../../../utils/rolePermissions";
import { Card, CardContent } from "../../../components/ui/card";
import { Badge } from "../../../components/ui/badge";
import {
  HiClock,
  HiEye,
  HiExclamation,
  HiCheckCircle,
  HiPencil,
  HiRefresh,
  HiXCircle,
} from "react-icons/hi";
import { toast } from "sonner";

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";

export default function TasksNeedingAction() {
  const navigate = useNavigate();
  const { user, isInitializing, accessToken } = useAuthStore();
  const [tasks, setTasks] = useState([]);
  const [counts, setCounts] = useState({
    total: 0,
    assignmentRequests: 0,
    inReview: 0,
    needsRevisionResubmitted: 0,
    overdue: 0,
  });
  const [loading, setLoading] = useState(false);
  const [filterType, setFilterType] = useState("all");

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;
    const userRole = getUserRole(user);
    if (!userRole) {
      navigate("/talent/dashboard");
    }
  }, [user, isInitializing, navigate]);

  // Fetch tasks needing action
  useEffect(() => {
    if (accessToken) {
      fetchTasksNeedingAction();
    }
  }, [accessToken, filterType]);

  const fetchTasksNeedingAction = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${API_URL}/api/tasks/admin/needs-action?filter=${filterType}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );
      if (response.ok) {
        const data = await response.json();
        setTasks(data.tasks || []);
        setCounts(
          data.counts || {
            total: 0,
            assignmentRequests: 0,
            inReview: 0,
            needsRevisionResubmitted: 0,
            overdue: 0,
          }
        );
      } else {
        toast.error("Failed to load tasks");
      }
    } catch (error) {
      console.error("Failed to fetch tasks needing action:", error);
      toast.error("Failed to load tasks needing action");
    } finally {
      setLoading(false);
    }
  };

  const handleApproveRequest = async (taskId, e) => {
    e.stopPropagation();
    try {
      const response = await fetch(
        `${API_URL}/api/tasks/${taskId}/approve-request`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );
      if (response.ok) {
        toast.success("Request approved successfully");
        await fetchTasksNeedingAction();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to approve request");
      }
    } catch (error) {
      console.error("Failed to approve request:", error);
      toast.error("Failed to approve request");
    }
  };

  const handleDenyRequest = async (taskId, e) => {
    e.stopPropagation();
    try {
      const response = await fetch(
        `${API_URL}/api/tasks/${taskId}/deny-request`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ reason: "" }),
        }
      );
      if (response.ok) {
        toast.success("Request denied");
        await fetchTasksNeedingAction();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to deny request");
      }
    } catch (error) {
      console.error("Failed to deny request:", error);
      toast.error("Failed to deny request");
    }
  };

  const getActionTypeLabel = (actionType) => {
    switch (actionType) {
      case "assignment-requests":
        return { label: "Assignment Request", icon: HiCheckCircle };
      case "in-review":
        return { label: "For Review", icon: HiEye };
      case "needs-revision-resubmitted":
        return { label: "Revisions", icon: HiPencil };
      case "overdue":
        return { label: "Overdue", icon: HiExclamation };
      default:
        return { label: "Action Needed", icon: HiClock };
    }
  };

  const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return "just now";
    if (diffMins < 60)
      return `${diffMins} minute${diffMins !== 1 ? "s" : ""} ago`;
    if (diffHours < 24)
      return `${diffHours} hour${diffHours !== 1 ? "s" : ""} ago`;
    return `${diffDays} day${diffDays !== 1 ? "s" : ""} ago`;
  };

  const formatDueDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = date - now;
    const diffDays = Math.ceil(diffMs / 86400000);

    if (diffDays < 0)
      return `${Math.abs(diffDays)} day${
        Math.abs(diffDays) !== 1 ? "s" : ""
      } overdue`;
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric" });
  };

  // Group tasks by action type
  const groupedTasks = {
    "in-review": tasks.filter((t) => t.actionType === "in-review"),
    "needs-revision-resubmitted": tasks.filter(
      (t) => t.actionType === "needs-revision-resubmitted"
    ),
    overdue: tasks.filter((t) => t.actionType === "overdue"),
  };

  return (
    <AdminLayout>
      <div className="flex-1 p-8">
        {/* Header with Horizontal Tabs */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-3xl font-bold text-foreground">
                Tasks Needing Action
              </h2>
              <p className="text-muted-foreground mt-1">
                Review and manage tasks that require your attention
              </p>
            </div>
            <button
              onClick={fetchTasksNeedingAction}
              disabled={loading}
              className="px-4 py-2 rounded-lg border border-border bg-card text-foreground hover:bg-muted transition-colors flex items-center gap-2 disabled:opacity-50"
            >
              <HiRefresh
                className={`w-4 h-4 ${loading ? "animate-spin" : ""}`}
              />
              Refresh
            </button>
          </div>

          {/* Horizontal Navigation Tabs */}
          <div className="flex gap-2 border-b border-border mt-4">
            <button
              onClick={() => navigate("/admin/tasks")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Dashboard
            </button>
            <button
              onClick={() => navigate("/admin/tasks/all")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Active Tasks
            </button>
            <button
              onClick={() => navigate("/admin/tasks/stored")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Store
            </button>
            <button
              onClick={() => navigate("/admin/tasks/completed")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Completed
            </button>
            <button
              onClick={() => navigate("/admin/tasks/requests")}
              className="px-4 py-3 border-b-2 border-primary text-primary font-semibold transition-colors flex items-center gap-2"
            >
              Requests
              {counts.total > 0 && (
                <span className="px-2 py-0.5 rounded-full bg-amber-500 text-white text-xs font-semibold">
                  {counts.total}
                </span>
              )}
            </button>
            <button
              onClick={() => navigate("/admin/tasks/create")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Create Task
            </button>
          </div>
        </div>

        {/* Filter Tabs */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex gap-2 flex-wrap">
              <button
                onClick={() => setFilterType("all")}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  filterType === "all"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground hover:bg-muted/80"
                }`}
              >
                All ({counts.total})
              </button>
              <button
                onClick={() => setFilterType("assignment-requests")}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  filterType === "assignment-requests"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground hover:bg-muted/80"
                }`}
              >
                Requests ({counts.assignmentRequests})
              </button>
              <button
                onClick={() => setFilterType("in-review")}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  filterType === "in-review"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground hover:bg-muted/80"
                }`}
              >
                For Review ({counts.inReview})
              </button>
              <button
                onClick={() => setFilterType("needs-revision-resubmitted")}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  filterType === "needs-revision-resubmitted"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground hover:bg-muted/80"
                }`}
              >
                Revisions ({counts.needsRevisionResubmitted})
              </button>
              <button
                onClick={() => setFilterType("overdue")}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  filterType === "overdue"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground hover:bg-muted/80"
                }`}
              >
                Overdue ({counts.overdue})
              </button>
            </div>
          </CardContent>
        </Card>

        {/* Tasks Table */}
        <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
          <CardContent className="p-6">
            {loading ? (
              <div className="flex items-center justify-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              </div>
            ) : tasks.length === 0 ? (
              <div className="text-center py-20">
                <HiCheckCircle className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-foreground mb-2">
                  All Caught Up!
                </h3>
                <p className="text-muted-foreground">
                  No tasks need your attention right now
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {tasks.map((task) => {
                  const { label, icon: Icon } = getActionTypeLabel(
                    task.actionType
                  );
                  return (
                    <div
                      key={task._id}
                      onClick={() => navigate(`/admin/tasks/${task._id}`)}
                      className="p-4 rounded-lg border border-border bg-background hover:bg-muted/50 transition-colors cursor-pointer"
                    >
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex items-center gap-3 flex-1">
                          <Icon className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                          <div className="flex-1">
                            <h4 className="font-semibold text-foreground mb-1">
                              {task.title}
                            </h4>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span>
                                {task.actionType === "assignment-requests"
                                  ? `${
                                      task.requestedBy?.name || "Unknown"
                                    } (requesting)`
                                  : task.assignedTo?.name || "Unassigned"}
                              </span>
                              <span className="flex items-center gap-1">
                                <HiClock className="w-4 h-4" />
                                {task.actionType === "overdue"
                                  ? formatDueDate(task.dueDate)
                                  : formatRelativeTime(task.updatedAt)}
                              </span>
                              {task.category && (
                                <Badge variant="outline">{task.category}</Badge>
                              )}
                              {task.priority && (
                                <Badge variant="outline" className="capitalize">
                                  {task.priority}
                                </Badge>
                              )}
                              <Badge variant="outline" className="capitalize">
                                {label}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        {task.actionType === "assignment-requests" ? (
                          <div className="flex gap-2">
                            <button
                              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
                              onClick={(e) => handleApproveRequest(task._id, e)}
                            >
                              <HiCheckCircle className="w-5 h-5" />
                              Approve
                            </button>
                            <button
                              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
                              onClick={(e) => handleDenyRequest(task._id, e)}
                            >
                              <HiXCircle className="w-5 h-5" />
                              Deny
                            </button>
                          </div>
                        ) : (
                          <button
                            className="px-4 py-2 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg font-medium transition-colors"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/admin/tasks/${task._id}`);
                            }}
                          >
                            View
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
