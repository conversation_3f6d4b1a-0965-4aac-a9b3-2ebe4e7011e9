import React, { useState, useEffect, createContext, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { useXPBreakdown } from '../../hooks/useXPBreakdown';
import { Tooltip, TooltipTrigger, TooltipContent } from '../ui/tooltip';

// Create context to share state between badge and calendar
const XPCalendarContext = createContext();

/**
 * Dynamic Selected Date Badge Component
 * Shows the currently selected date and XP total from context
 * Displays dropdown with breakdown and work details on click
 */
export const TodayXPBadge = () => {
  const context = useContext(XPCalendarContext);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  if (!context) {
    // Fallback if used outside context
    const [todayXP, setTodayXP] = useState(0);
    const [loading, setLoading] = useState(true);
    const [badgeDate, setBadgeDate] = useState(null);
    const [badgeData, setBadgeData] = useState(null);
    const { fetchXPByDate } = useXPBreakdown(30);
    const today = new Date();

    useEffect(() => {
      let mounted = true;

      const loadTodayXP = async () => {
        const todayString = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
        try {
          const data = await fetchXPByDate(todayString);
          if (mounted) {
            setTodayXP(data?.xpBreakdown?.total || 0);
            setBadgeDate(todayString);
            setBadgeData(data);
          }
        } catch (error) {
          console.error('Error fetching today XP:', error);
        } finally {
          if (mounted) setLoading(false);
        }
      };

      // Listener to update badge when mini calendar selects a date
      const handler = (e) => {
        try {
          const detail = e?.detail;
          if (detail) {
            const total = detail.data?.xpBreakdown?.total || 0;
            if (mounted) {
              setTodayXP(total);
              setBadgeDate(detail.date || null);
              setBadgeData(detail.data);
              setLoading(false);
            }
          }
        } catch (err) {
          // ignore
        }
      };

      window.addEventListener('xpCalendarDateSelected', handler);
      loadTodayXP();

      return () => {
        mounted = false;
        window.removeEventListener('xpCalendarDateSelected', handler);
      };
    }, []);

    if (loading) {
      return (
        <div
          className="px-3 py-1.5 rounded-full backdrop-blur-md border animate-pulse"
          style={{
            background: "rgba(255, 255, 255, 0.03)",
            borderColor: "rgba(255, 255, 255, 0.08)",
          }}
        >
          <div className="w-16 h-5 bg-white/6 rounded"></div>
        </div>
      );
    }

    const displayDate = badgeDate ? new Date(badgeDate + 'T00:00:00') : today;

    return (
      <Tooltip open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
        <TooltipTrigger asChild>
          <div
            className="inline-flex items-center px-2.5 py-1 rounded-full backdrop-blur-md border transition-all duration-300 hover:scale-105 cursor-pointer"
            style={{
              background: "rgba(255, 255, 255, 0.03)",
              borderColor: "rgba(255, 255, 255, 0.08)",
              boxShadow: "0 2px 8px rgba(0,0,0,0.12)"
            }}
          >
            <div className="inline-flex items-center gap-2">
              <span className="text-[10px] font-medium" style={{ color: "rgba(255,255,255,0.75)" }}>
                {displayDate.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}
              </span>
              <span className="w-px h-3 bg-white/10 rounded" aria-hidden></span>

              <span className="text-xs font-bold text-white">{todayXP} XP</span>

              <div
                className="w-1 h-1 rounded-full ml-1 animate-pulse bg-white/60"
              />
            </div>
          </div>
        </TooltipTrigger>
        {badgeData && (
          <TooltipContent
            side="bottom"
            align="end"
            sideOffset={8}
            className="w-64 p-4 rounded-2xl border"
            style={{
              // Match notification panel: near-solid dark (#1A1C23) with high opacity
              background: 'rgba(26, 28, 35, 0.96)',
              backdropFilter: 'blur(4px) saturate(105%)',
              borderColor: 'rgba(255, 255, 255, 0.06)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.02)',
            }}
          >
            <div className="space-y-2">
              <h4 className="text-xs font-semibold text-white mb-2">Breakdown:</h4>

              {badgeData.xpBreakdown.checkin > 0 && (
                <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs">✅</span>
                    <span className="text-[10px] text-white">Check-in</span>
                  </div>
                  <span className="text-white text-[10px] font-medium">+{badgeData.xpBreakdown.checkin}</span>
                </div>
              )}

              {badgeData.xpBreakdown.report > 0 && (
                <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs">📝</span>
                    <span className="text-[10px] text-white">Shift Report</span>
                  </div>
                  <span className="text-white text-[10px] font-medium">+{badgeData.xpBreakdown.report}</span>
                </div>
              )}

              {badgeData.xpBreakdown.hoursBonus > 0 && (
                <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs">⏰</span>
                    <span className="text-[10px] text-white">Hours Bonus</span>
                  </div>
                  <span className="text-white text-[10px] font-medium">+{badgeData.xpBreakdown.hoursBonus}</span>
                </div>
              )}

              {badgeData.xpBreakdown.streakBonus > 0 && (
                <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs">🔥</span>
                    <span className="text-[10px] text-white">Streak Bonus</span>
                  </div>
                  <span className="text-white text-[10px] font-medium">+{badgeData.xpBreakdown.streakBonus}</span>
                </div>
              )}

              {badgeData.xpBreakdown.meeting > 0 && (
                <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs">🤝</span>
                    <span className="text-[10px] text-white">Meeting</span>
                  </div>
                  <span className="text-white text-[10px] font-medium">+{badgeData.xpBreakdown.meeting}</span>
                </div>
              )}

              {badgeData.xpBreakdown.badge > 0 && (
                <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs">🏆</span>
                    <span className="text-[10px] text-white">Badges</span>
                  </div>
                  <span className="text-white text-[10px] font-medium">+{badgeData.xpBreakdown.badge}</span>
                </div>
              )}

              {badgeData.workDetails && (
                <div className="pt-2 mt-2 border-t border-white/10">
                  <h4 className="text-[10px] font-medium text-white mb-1.5">Work Details:</h4>
                  <div className="space-y-1 text-[10px]">
                    {badgeData.workDetails.hoursWorked > 0 && (
                      <div className="flex justify-between">
                        <span className="text-white/80">Hours Worked:</span>
                        <span className="text-white font-medium">{badgeData.workDetails.hoursWorked}h</span>
                      </div>
                    )}

                    {badgeData.workDetails.streak > 0 && (
                      <div className="flex justify-between">
                        <span className="text-white/80">Streak Day:</span>
                        <span className="text-white font-medium">{badgeData.workDetails.streak}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </TooltipContent>
        )}
      </Tooltip>
    );
  }

  const { selectedDate, selectedDateData, loading } = context;

  if (!selectedDate || loading) {
    return (
      <div
        className="px-3 py-1.5 rounded-full backdrop-blur-md border animate-pulse"
        style={{
          background: "rgba(255, 255, 255, 0.03)",
          borderColor: "rgba(255, 255, 255, 0.08)",
        }}
      >
        <div className="w-16 h-5 bg-white/6 rounded"></div>
      </div>
    );
  }

  const totalXP = selectedDateData?.xpBreakdown?.total || 0;
  const date = new Date(selectedDate + 'T00:00:00');

  return (
    <Tooltip open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
      <TooltipTrigger asChild>
        <div
          className="inline-flex items-center px-2.5 py-1 rounded-full backdrop-blur-sm border transition-all duration-300 hover:scale-105 cursor-pointer"
          style={{
            background: "rgba(255, 255, 255, 0.03)",
            borderColor: "rgba(255, 255, 255, 0.08)",
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.12)"
          }}
        >
          <div className="inline-flex items-center gap-2">
            <span className="text-[10px] font-medium text-gray-200">
              {date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}
            </span>
            <span className="w-px h-3 bg-white/6 rounded" aria-hidden></span>

            <span className="text-xs font-bold text-gray-100">{totalXP} XP</span>

            <div
              className="w-1 h-1 rounded-full ml-1 animate-pulse bg-white/60"
            />
          </div>
        </div>
      </TooltipTrigger>
      {selectedDateData && (
        <TooltipContent
          side="bottom"
          align="end"
          sideOffset={8}
          className="w-64 p-4 rounded-2xl border"
          style={{
            // Match notification panel: near-solid dark (#1A1C23) with high opacity
            background: 'rgba(26, 28, 35, 0.96)',
            backdropFilter: 'blur(4px) saturate(105%)',
            borderColor: 'rgba(255, 255, 255, 0.06)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.02)',
          }}
        >
          <div className="space-y-2">
            <h4 className="text-xs font-semibold text-white mb-2">Breakdown:</h4>

            {selectedDateData.xpBreakdown.checkin > 0 && (
              <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                <div className="flex items-center space-x-2">
                  <span className="text-xs">✅</span>
                  <span className="text-[10px] text-white">Check-in</span>
                </div>
                <span className="text-white text-[10px] font-medium">+{selectedDateData.xpBreakdown.checkin}</span>
              </div>
            )}

            {selectedDateData.xpBreakdown.report > 0 && (
              <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                <div className="flex items-center space-x-2">
                  <span className="text-xs">📝</span>
                  <span className="text-[10px] text-white">Shift Report</span>
                </div>
                <span className="text-white text-[10px] font-medium">+{selectedDateData.xpBreakdown.report}</span>
              </div>
            )}

            {selectedDateData.xpBreakdown.hoursBonus > 0 && (
              <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                <div className="flex items-center space-x-2">
                  <span className="text-xs">⏰</span>
                  <span className="text-[10px] text-white">Hours Bonus</span>
                </div>
                <span className="text-white text-[10px] font-medium">+{selectedDateData.xpBreakdown.hoursBonus}</span>
              </div>
            )}

            {selectedDateData.xpBreakdown.streakBonus > 0 && (
              <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                <div className="flex items-center space-x-2">
                  <span className="text-xs">🔥</span>
                  <span className="text-[10px] text-white">Streak Bonus</span>
                </div>
                <span className="text-white text-[10px] font-medium">+{selectedDateData.xpBreakdown.streakBonus}</span>
              </div>
            )}

            {selectedDateData.xpBreakdown.meeting > 0 && (
              <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                <div className="flex items-center space-x-2">
                  <span className="text-xs">🤝</span>
                  <span className="text-[10px] text-white">Meeting</span>
                </div>
                <span className="text-white text-[10px] font-medium">+{selectedDateData.xpBreakdown.meeting}</span>
              </div>
            )}

            {selectedDateData.xpBreakdown.badge > 0 && (
              <div className="flex items-center justify-between py-1.5 px-2 rounded-lg border" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.06)' }}>
                <div className="flex items-center space-x-2">
                  <span className="text-xs">🏆</span>
                  <span className="text-[10px] text-white">Badges</span>
                </div>
                <span className="text-white text-[10px] font-medium">+{selectedDateData.xpBreakdown.badge}</span>
              </div>
            )}

            {/* Work Details */}
            {selectedDateData.workDetails && (
              <div className="pt-2 mt-2 border-t border-white/10">
                <h4 className="text-[10px] font-medium text-white mb-1.5">Work Details:</h4>
                <div className="space-y-1 text-[10px]">
                  {selectedDateData.workDetails.hoursWorked > 0 && (
                    <div className="flex justify-between">
                      <span className="text-white/80">Hours Worked:</span>
                      <span className="text-white font-medium">{selectedDateData.workDetails.hoursWorked}h</span>
                    </div>
                  )}
                  {selectedDateData.workDetails.streak > 0 && (
                    <div className="flex justify-between">
                      <span className="text-white/80">Streak Day:</span>
                      <span className="text-white font-medium">{selectedDateData.workDetails.streak}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </TooltipContent>
      )}
    </Tooltip>
  );
};

/**
 * Mini XP Calendar Component for Dashboard Widget
 * Compact view showing current month with XP indicators
 */
const MiniXPCalendar = () => {
  const navigate = useNavigate();
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [allXPData, setAllXPData] = useState({});
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedDateData, setSelectedDateData] = useState(null);
  const { fetchXPByDate, fetchXPByMonth } = useXPBreakdown(30);

  // Portal launch date (Nov 1, 2025)
  const PORTAL_LAUNCH_DATE = new Date(2025, 10, 1);
  const today = new Date();
  const todayString = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

  // Auto-select today's date on mount
  useEffect(() => {
    const loadTodaysData = async () => {
      setSelectedDate(todayString);

      // Use cached data if available for instant display
      const cachedData = allXPData[todayString];
      if (cachedData) {
        setSelectedDateData(cachedData);
        try {
          window.dispatchEvent(new CustomEvent('xpCalendarDateSelected', { detail: { date: todayString, data: cachedData } }));
        } catch (e) {
          // ignore if dispatch fails
        }
        return; // Don't fetch again if we have it
      }

      try {
        const data = await fetchXPByDate(todayString);
        setSelectedDateData(data);
        // Notify other components (badge) about the initial selection
        try {
          window.dispatchEvent(new CustomEvent('xpCalendarDateSelected', { detail: { date: todayString, data } }));
        } catch (e) {
          // ignore if dispatch fails in some environments
        }
      } catch (error) {
        console.error('Error fetching today\'s data:', error);
        setSelectedDateData(null);
      }
    };

    loadTodaysData();
  }, [todayString, fetchXPByDate, allXPData]);

  // Load XP data for the current month - OPTIMIZED: Single API call
  useEffect(() => {
    const loadXPData = async () => {
      setLoading(true);
      try {
        const year = currentMonth.getFullYear();
        const month = currentMonth.getMonth() + 1; // JS months are 0-indexed

        // Fetch entire month's data in ONE API call instead of 30+ individual calls
        const monthData = await fetchXPByMonth(year, month);

        // monthData is already keyed by date strings, ready to use
        setAllXPData(monthData || {});
      } catch (error) {
        console.error('Failed to load XP data:', error);
        setAllXPData({});
      } finally {
        setLoading(false);
      }
    };

    loadXPData();
  }, [currentMonth, fetchXPByMonth]);

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();

    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const current = new Date(startDate);

    for (let i = 0; i < 35; i++) {
      const dayInfo = {
        date: new Date(current),
        isCurrentMonth: current.getMonth() === month,
        isToday: current.toDateString() === today.toDateString(),
        isSunday: current.getDay() === 0,
        isBeforeLaunch: current < PORTAL_LAUNCH_DATE,
        isFuture: current > today,
        dateString: `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, '0')}-${String(current.getDate()).padStart(2, '0')}`
      };

      days.push(dayInfo);
      current.setDate(current.getDate() + 1);
    }

    return days;
  };

  const calendarDays = generateCalendarDays();

  // Get XP indicator
  const getXPIndicator = (dayInfo) => {
    if (dayInfo.isBeforeLaunch || dayInfo.isFuture || !dayInfo.isCurrentMonth || dayInfo.isSunday) {
      return null;
    }

    const dayData = allXPData[dayInfo.dateString];

    if (dayData && dayData.xpBreakdown?.total > 0) {
      const total = dayData.xpBreakdown.total;
      let opacity = 0.3;

      if (total >= 60) {
        opacity = 1;
      } else if (total >= 30) {
        opacity = 0.7;
      } else if (total >= 1) {
        opacity = 0.4;
      }

      return (
        <div
          className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-white"
          style={{ opacity }}
        />
      );
    }

    return null;
  };

  // Handle date click - update selected date and fetch data
  const handleDateClick = async (dayInfo) => {
    if (dayInfo.isBeforeLaunch || dayInfo.isFuture || !dayInfo.isCurrentMonth) {
      return;
    }

    setSelectedDate(dayInfo.dateString);

    try {
      const data = await fetchXPByDate(dayInfo.dateString);
      setSelectedDateData(data);
      // Broadcast the selection so badge can update
      try {
        window.dispatchEvent(new CustomEvent('xpCalendarDateSelected', { detail: { date: dayInfo.dateString, data } }));
      } catch (e) {
        // ignore
      }
    } catch (error) {
      console.error('Error fetching date data:', error);
      setSelectedDateData(null);
    }
  };

  // Get day cell styling
  const getDayCellStyle = (dayInfo) => {
    let baseStyle = "h-7 w-full flex items-center justify-center text-[10px] font-medium transition-all duration-200 rounded-md relative backdrop-blur-sm ";

    if (dayInfo.isBeforeLaunch || !dayInfo.isCurrentMonth) {
      return baseStyle + "text-gray-600 cursor-default";
    }

    if (dayInfo.isFuture) {
      return baseStyle + "text-gray-500 cursor-default";
    }

    if (selectedDate === dayInfo.dateString) {
      return baseStyle + "text-white bg-white/15 border border-white/30 cursor-pointer";
    }

    if (dayInfo.isSunday) {
      return baseStyle + "text-gray-400 cursor-pointer hover:bg-white/5";
    }

    if (dayInfo.isToday) {
      // remove the heavy tailwind shadow-lg so we can apply a smaller custom shadow inline
      return baseStyle + "text-white bg-white/8 font-bold border border-white/15 cursor-pointer hover:bg-white/12";
    }

    return baseStyle + "text-gray-300 hover:bg-white/5 hover:border hover:border-white/10 cursor-pointer";
  };

  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const dayNames = ["S", "M", "T", "W", "T", "F", "S"];

  if (loading) {
    return (
      <XPCalendarContext.Provider value={{ selectedDate, selectedDateData, loading }}>
        <div className="animate-pulse relative p-3">
          {/* Glassmorphism shine effect */}
          <div
            className="absolute top-0 right-0 w-24 h-full pointer-events-none rounded-xl"
            style={{
              background: "radial-gradient(ellipse at right, rgba(255,255,255,0.02), transparent)",
            }}
          ></div>

          {/* Month header skeleton */}
          <div className="flex items-center justify-between mb-3 relative z-10">
            <div className="w-8 h-7 rounded-lg backdrop-blur-sm border" style={{ background: "rgba(255, 255, 255, 0.03)", borderColor: "rgba(255, 255, 255, 0.05)" }}></div>
            <div className="w-28 h-4 rounded backdrop-blur-sm" style={{ background: "rgba(255, 255, 255, 0.05)" }}></div>
            <div className="w-8 h-7 rounded-lg backdrop-blur-sm border" style={{ background: "rgba(255, 255, 255, 0.03)", borderColor: "rgba(255, 255, 255, 0.05)" }}></div>
          </div>

          {/* Day headers skeleton */}
          <div className="grid grid-cols-7 gap-1 mb-2 relative z-10">
            {dayNames.map((_, index) => (
              <div key={index} className="h-5 rounded backdrop-blur-sm" style={{ background: "rgba(255, 255, 255, 0.03)" }}></div>
            ))}
          </div>

          {/* Calendar grid skeleton */}
          <div className="grid grid-cols-7 gap-1 relative z-10">
            {[...Array(35)].map((_, index) => (
              <div key={index} className="h-7 rounded-md backdrop-blur-sm" style={{ background: "rgba(255, 255, 255, 0.05)" }}></div>
            ))}
          </div>

          {/* Legend skeleton */}
          <div className="mt-3 pt-3 border-t relative z-10" style={{ borderColor: "rgba(255, 255, 255, 0.1)" }}>
            <div className="flex justify-center gap-2">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="w-12 h-3 rounded backdrop-blur-sm" style={{ background: "rgba(255, 255, 255, 0.05)" }}></div>
              ))}
            </div>
          </div>
        </div>
      </XPCalendarContext.Provider>
    );
  }

  return (
    <XPCalendarContext.Provider value={{ selectedDate, selectedDateData, loading }}>
      <div className="text-white relative p-4 rounded-2xl border backdrop-blur-xl shadow-md" style={{ background: 'rgba(255,255,255,0.03)', borderColor: 'rgba(255,255,255,0.08)', boxShadow: '0 6px 24px rgba(0,0,0,0.18)', maxHeight: '620px', overflow: 'hidden' }}>
        {/* Glassmorphism shine effect */}
        <div
          className="absolute top-0 right-0 w-24 h-full pointer-events-none rounded-xl"
          style={{
            background: "radial-gradient(ellipse at right, rgba(255,255,255,0.02), transparent)",
          }}
        ></div>

        {/* Content column: keep header/legend fixed and make grid scroll if needed */}
        <div className="flex flex-col h-full">
          {/* Month Navigation */}
          <div className="flex items-center justify-between mb-3 relative z-10">
            <button
              onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1))}
              disabled={currentMonth <= new Date(2025, 10, 1)}
              className="p-1.5 rounded-lg transition-all duration-200 hover:bg-white/5 disabled:opacity-30 disabled:cursor-not-allowed backdrop-blur-sm border hover:border-white/10"
              style={{
                background: "rgba(255, 255, 255, 0.03)",
                borderColor: "rgba(255, 255, 255, 0.05)",
              }}
            >
              <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <h4 className="text-xs font-semibold" style={{ color: "#FFFFFF" }}>
              {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
            </h4>

            <button
              onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1))}
              disabled={currentMonth >= new Date(today.getFullYear(), today.getMonth())}
              className="p-1.5 rounded-lg transition-all duration-200 hover:bg-white/5 disabled:opacity-30 disabled:cursor-not-allowed backdrop-blur-sm border hover:border-white/10"
              style={{
                background: "rgba(255, 255, 255, 0.03)",
                borderColor: "rgba(255, 255, 255, 0.05)",
              }}
            >
              <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Day Headers */}
          <div className="grid grid-cols-7 gap-0.5 mb-2 relative z-10">
            {dayNames.map((day, index) => (
              <div key={`day-${index}-${day}`} className="h-5 flex items-center justify-center text-[9px] font-medium" style={{ color: "rgba(255, 255, 255, 0.5)" }}>
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Grid - allow internal scrolling when space is limited */}
          <div className="relative z-10 flex-1 overflow-auto" style={{ paddingRight: '6px' }}>
            <div className="grid grid-cols-7 gap-px">
              {calendarDays.map((dayInfo, index) => {
                const cellClass = getDayCellStyle(dayInfo);
                const cellStyle = dayInfo.isToday ? { boxShadow: '0 2px 8px rgba(0,0,0,0.12)' } : undefined;

                return (
                  <div
                    key={index}
                    className={cellClass}
                    style={cellStyle}
                    onClick={() => handleDateClick(dayInfo)}
                  >
                    <span>{dayInfo.date.getDate()}</span>

                    {/* XP Activity Indicator */}
                    {getXPIndicator(dayInfo)}

                    {/* Sunday Rest Indicator */}
                    {dayInfo.isSunday && dayInfo.isCurrentMonth && !dayInfo.isBeforeLaunch && (
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 rounded-b-md" style={{ backgroundColor: "rgba(255, 255, 255, 0.15)" }}></div>
                    )}
                  </div>
                );
              })}
            </div>

          </div>

          {/* Compact Legend */}
          <div className="mt-3 pt-3 border-t relative z-10" style={{ borderColor: "rgba(255, 255, 255, 0.08)" }}>
            <div className="flex flex-wrap gap-3 text-[10px] justify-center">
              <div className="flex items-center space-x-1.5">
                <div className="w-2 h-2 bg-white opacity-100 rounded-full shadow-sm"></div>
                <span style={{ color: "rgba(255, 255, 255, 0.85)" }}>High</span>
              </div>
              <div className="flex items-center space-x-1.5">
                <div className="w-2 h-2 bg-white opacity-70 rounded-full shadow-sm"></div>
                <span style={{ color: "rgba(255, 255, 255, 0.85)" }}>Med</span>
              </div>
              <div className="flex items-center space-x-1.5">
                <div className="w-2 h-2 bg-white opacity-40 rounded-full shadow-sm"></div>
                <span style={{ color: "rgba(255, 255, 255, 0.85)" }}>Low</span>
              </div>
              <div className="flex items-center space-x-1.5">
                <div className="w-2 h-2 rounded-full shadow-sm" style={{ backgroundColor: "rgba(255, 255, 255, 0.2)" }}></div>
                <span style={{ color: "rgba(255, 255, 255, 0.85)" }}>Rest</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </XPCalendarContext.Provider>
  );
};

export default MiniXPCalendar;
