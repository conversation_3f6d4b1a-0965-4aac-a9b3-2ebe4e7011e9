import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../store/authStore";
import { getUserRole, hasPermission } from "../utils/rolePermissions";

/**
 * AdminPanelRoute - Protected route wrapper for admin panel sections
 * Allows users with admin access to view admin panel features
 */
const AdminPanelRoute = ({ children }) => {
  const navigate = useNavigate();
  const { user, isInitializing } = useAuthStore();

  useEffect(() => {
    // Wait for auth initialization
    if (isInitializing) return;

    console.log('🔍 [AdminPanelRoute] Checking access for user:', {
      email: user?.email,
      userType: user?.userType,
      roleType: user?.roleType,
      name: user?.name
    });

    // Check if user has any admin role
    const userRole = getUserRole(user);
    console.log('🔍 [AdminPanelRoute] User role result:', userRole);
    
    if (!userRole) {
      console.log('❌ [AdminPanelRoute] No admin role found - redirecting to talent dashboard');
      navigate("/talent/dashboard");
      return;
    }

    // Check if user has admin panel permissions
    const hasAdminPanelPermission = hasPermission(user, 'admin.panel.view');
    console.log('🔍 [AdminPanelRoute] Admin panel permission check:', hasAdminPanelPermission);
    
    if (!hasAdminPanelPermission) {
      console.log('❌ [AdminPanelRoute] User does not have admin panel permissions - redirecting to main dashboard');
      navigate("/admin/dashboard");
      return;
    }

    console.log('✅ [AdminPanelRoute] Admin panel access granted');
  }, [user, navigate, isInitializing]);

  // Show loading while auth is initializing
  if (isInitializing) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Check if user has admin panel access
  const userRole = getUserRole(user);
  
  if (!userRole || !hasPermission(user, 'admin.panel.view')) {
    return null;
  }

  return children;
};

export default AdminPanelRoute;