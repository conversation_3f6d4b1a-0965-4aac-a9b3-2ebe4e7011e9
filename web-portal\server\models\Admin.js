import mongoose from 'mongoose';

const adminSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
    },
    password: {
      type: String,
      required: true,
    },
    passwordHash: {
      type: String,
      required: false, // Legacy field, keeping for compatibility
    },
    role: {
      type: String,
      default: 'admin',
      enum: ['admin', 'super_admin'],
    },
    permissions: {
      type: [String],
      default: ['manage_talents', 'manage_batches', 'manage_reports', 'manage_announcements'],
    },
    roleType: {
      type: String,
      enum: ['super_admin', 'full_admin', 'hiring_manager', 'employee_manager', 'team_lead'],
      default: null, // null means use legacy permission system
    },
    permissionMode: {
      type: String,
      enum: ['role', 'custom'],
      default: 'role', // 'role' uses roleType, 'custom' uses customPermissions
    },
    customPermissions: {
      type: [String],
      default: [], // Custom feature permissions like ['talents', 'payments', 'meetings']
    },
    linkedTalentId: {
      type: String,
      default: null, // Reference to original talent record if migrated
    },
    canAccessTalentPortal: {
      type: Boolean,
      default: false, // Special flag for CEO-level users
    },
    migratedFrom: {
      type: String,
      enum: ['talent', 'manual_creation'],
      default: 'manual_creation',
    },
    migrationDate: {
      type: Date,
      default: null,
    },
    canViewPayments: {
      type: Boolean,
      default: false, // Only super_admin can view payments
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    mustChangePassword: {
      type: Boolean,
      default: false, // Force password change on first login
    },
    lastLogin: {
      type: Date,
      default: null,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin',
      default: null, // Which admin created this user
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin',
      default: null, // Which admin last updated this user
    },
    deletedAt: {
      type: Date,
      default: null, // Soft delete timestamp
    },
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin',
      default: null, // Which admin deleted this user
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.model('Admin', adminSchema);
