import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import { getUserRole } from '../../utils/rolePermissions';
import AdminLayout from '../../components/admin/AdminLayout';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

export default function BulkUpload() {
  const navigate = useNavigate();
  const { accessToken, user, isInitializing } = useAuthStore();
  const [batches, setBatches] = useState([]);
  const [file, setFile] = useState(null);
  const [batchId, setBatchId] = useState('');
  const [loading, setLoading] = useState(false);
  const [jobId, setJobId] = useState(null);
  const [jobStatus, setJobStatus] = useState(null);
  const [error, setError] = useState(null);

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;
    
    const userRole = getUserRole(user);
    if (!userRole) {
      navigate('/talent/dashboard');
    }
  }, [user, navigate, isInitializing]);

  // Fetch batches
  useEffect(() => {
    fetchBatches();
  }, []);

  // Poll job status
  useEffect(() => {
    if (jobId) {
      const interval = setInterval(() => {
        fetchJobStatus();
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [jobId]);

  async function fetchBatches() {
    try {
      const response = await fetch(`${API_URL}/admin/batches`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setBatches(data.batches);
      }
    } catch (err) {
      console.error('Failed to fetch batches:', err);
    }
  }

  async function fetchJobStatus() {
    try {
      const response = await fetch(`${API_URL}/admin/bulk-import/${jobId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setJobStatus(data);

        // Stop polling if job is completed or failed
        if (data.status === 'completed' || data.status === 'failed') {
          setJobId(null);
        }
      }
    } catch (err) {
      console.error('Failed to fetch job status:', err);
    }
  }

  async function handleSubmit(e) {
    e.preventDefault();
    
    if (!file) {
      setError('Please select a CSV file');
      return;
    }

    setLoading(true);
    setError(null);
    setJobStatus(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      if (batchId) {
        formData.append('batchId', batchId);
      }

      const response = await fetch(`${API_URL}/admin/talents/bulk`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        credentials: 'include',
        body: formData
      });

      const data = await response.json();

      if (response.ok) {
        setJobId(data.jobId);
        setFile(null);
      } else {
        setError(data.error || 'Failed to upload file');
      }
    } catch (err) {
      setError('Failed to upload file. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  }

  function downloadSampleCSV() {
    const csvContent = 'name,email,companyEmail,group,designation,employmentType\nJohn Doe,<EMAIL>,<EMAIL>,core,Frontend Developer,Internship Ongoing\nJane Smith,<EMAIL>,<EMAIL>,growth,Backend Developer,Full Time Developer';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'sample-talents.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  }

  const userRole = getUserRole(user);
  if (isInitializing || !userRole) {
    return null;
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto space-y-4">
        {/* Back Button */}
        <button
          onClick={() => navigate('/admin/talents')}
          className="text-blue-400 hover:text-blue-300 flex items-center gap-2 transition"
        >
          ← Back to Talents
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-4 lg:gap-4">
          {/* Upload Form */}
          <div className="bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 backdrop-blur-sm rounded-lg p-4 md:p-6 lg:p-8 border border-border/40">
            <h2 className="text-lg md:text-xl font-semibold text-white mb-4 md:mb-6">Upload CSV File</h2>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* File Input */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  CSV File *
                </label>
                <input
                  type="file"
                  accept=".csv"
                  onChange={(e) => setFile(e.target.files[0])}
                  className="w-full px-4 py-3 bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 border border-border/40 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:bg-blue-600 file:text-white file:cursor-pointer hover:file:bg-blue-700"
                />
                {file && (
                  <p className="mt-2 text-sm text-gray-400">
                    Selected: {file.name} ({(file.size / 1024).toFixed(2)} KB)
                  </p>
                )}
              </div>

              {/* Batch */}
              <div>
                <label htmlFor="batchId" className="block text-sm font-medium text-gray-300 mb-2">
                  Assign Batch (Optional)
                </label>
                <select
                  id="batchId"
                  value={batchId}
                  onChange={(e) => setBatchId(e.target.value)}
                  className="w-full px-4 py-3 bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 border border-border/40 rounded-lg text-white focus:outline-none focus:border-blue-500 transition"
                >
                  <option value="">No Batch</option>
                  {batches.map((batch) => (
                    <option key={batch._id} value={batch.batchId}>
                      {batch.name} ({batch.batchId})
                    </option>
                  ))}
                </select>
              </div>

              {/* Error */}
              {error && (
                <div className="bg-red-500/20 border border-red-500 text-red-400 px-4 py-3 rounded-lg">
                  {error}
                </div>
              )}

              {/* Submit */}
              <button
                type="submit"
                disabled={loading || !file}
                className="w-full py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105 disabled:scale-100"
              >
                {loading ? (
                  <span className="flex items-center justify-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-solid border-white border-r-transparent"></div>
                    Uploading...
                  </span>
                ) : (
                  'Upload & Process'
                )}
              </button>
            </form>

            {/* Sample CSV */}
            <div className="mt-6 pt-6 border-t border-border/40">
              <button
                onClick={downloadSampleCSV}
                className="text-blue-400 hover:text-blue-300 text-sm flex items-center gap-2"
              >
                📥 Download Sample CSV Template
              </button>
            </div>
          </div>

          {/* Instructions & Status */}
          <div className="space-y-4">
            {/* Instructions */}
            <div className="bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 backdrop-blur-sm rounded-lg p-4 border border-border/40">
              <h2 className="text-xl font-semibold text-white mb-4">CSV Format</h2>
              <p className="text-sm text-gray-400 mb-4">
                Your CSV file should have the following columns:
              </p>
              <div className="bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 rounded p-4 font-mono text-sm text-gray-300 mb-4 overflow-x-auto">
                name,email,companyEmail,group,designation,employmentType<br />
                John Doe,<EMAIL>,<EMAIL>,core,Frontend Developer,intern<br />
                Jane Smith,<EMAIL>,<EMAIL>,growth,Backend Developer,fulltime
              </div>
              <ul className="text-sm text-gray-400 space-y-2">
                <li>• <strong>name</strong>: Full name of the talent (required)</li>
                <li>• <strong>email</strong>: Personal email address (required, must be unique)</li>
                <li>• <strong>companyEmail</strong>: Company email for Jibble (optional but recommended)</li>
                <li>• <strong>group</strong>: core or growth (optional)</li>
                <li>• <strong>designation</strong>: Job title (optional)</li>
                <li>• <strong>employmentType</strong>: "Internship Ongoing", "Internship Completed", "Full Time Developer", "Project Manager", or "Recruiting" (optional, defaults to "Internship Ongoing")</li>
                <li>• Talent IDs will be auto-generated</li>
                <li>• Invitations will be sent automatically</li>
              </ul>
            </div>

            {/* Job Status */}
            {jobStatus && (
              <div className="bg-card/90 backdrop-blur supports-[backdrop-filter]:bg-card/70 backdrop-blur-sm rounded-lg p-4 border border-border/40">
                <h2 className="text-xl font-semibold text-white mb-4">Upload Status</h2>
                
                <div className="space-y-4">
                  {/* Status Badge */}
                  <div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      jobStatus.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                      jobStatus.status === 'failed' ? 'bg-red-500/20 text-red-400' :
                      'bg-yellow-500/20 text-yellow-400'
                    }`}>
                      {jobStatus.status.toUpperCase()}
                    </span>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 rounded p-4 text-center">
                      <div className="text-xl font-bold text-blue-400">{jobStatus.totalRows}</div>
                      <div className="text-xs text-gray-400 mt-1">Total Rows</div>
                    </div>
                    <div className="bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 rounded p-4 text-center">
                      <div className="text-xl font-bold text-green-400">{jobStatus.createdCount}</div>
                      <div className="text-xs text-gray-400 mt-1">Created</div>
                    </div>
                    <div className="bg-card/85 backdrop-blur supports-[backdrop-filter]:bg-card/65 rounded p-4 text-center">
                      <div className="text-xl font-bold text-red-400">{jobStatus.skippedCount}</div>
                      <div className="text-xs text-gray-400 mt-1">Skipped</div>
                    </div>
                  </div>

                  {/* Errors */}
                  {jobStatus.errors && jobStatus.errors.length > 0 && (
                    <div>
                      <h3 className="text-sm font-semibold text-gray-300 mb-2">Errors:</h3>
                      <div className="bg-red-500/10 border border-red-500/30 rounded p-4 max-h-48 overflow-y-auto">
                        {jobStatus.errors.map((error, index) => (
                          <div key={index} className="text-sm text-red-400 mb-2">
                            Row {error.row}: {error.email} - {error.reason}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Success Message */}
                  {jobStatus.status === 'completed' && (
                    <div className="bg-green-500/20 border border-green-500 text-green-400 px-4 py-3 rounded-lg text-sm">
                      ✅ Successfully created {jobStatus.createdCount} talents! Invitations have been sent.
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
