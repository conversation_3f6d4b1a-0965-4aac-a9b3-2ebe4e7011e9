import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { hasDetailedPermission } from '../utils/permissionMatrix';
import { getRoutePermission } from '../utils/routePermissions';

/**
 * Permission-based route guard
 * Prevents users from accessing routes they don't have permission for
 */
export const PermissionGuard = ({ 
  children, 
  permission, // Optional - if not provided, auto-detects from current route
  fallbackPath = '/admin/dashboard',
  showAccessDenied = false 
}) => {
  const { user, isAuthenticated } = useAuthStore();
  const location = useLocation();

  // If not authenticated, let ProtectedRoute handle the redirect
  if (!isAuthenticated || !user) {
    return children;
  }

  // Auto-detect permission from route if not explicitly provided
  const requiredPermission = permission || getRoutePermission(location.pathname);
  
  // If no permission required for this route, allow access
  if (!requiredPermission) {
    console.log(`🔓 No permission required for route: ${location.pathname}`);
    return children;
  }

  // Check if user has required permission
  const hasAccess = hasDetailedPermission(user, requiredPermission);

  if (!hasAccess) {
    console.log(`🚫 Access denied: User ${user.email} lacks permission "${requiredPermission}" for route ${location.pathname}`);
    
    if (showAccessDenied) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md mx-auto text-center p-6">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <svg 
                className="h-6 w-6 text-red-600" 
                fill="none" 
                viewBox="0 0 24 24" 
                strokeWidth="1.5" 
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" 
                />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-4">
              You don't have permission to access this page.
            </p>
            <p className="text-sm text-gray-500 mb-6">
              Required permission: <code className="bg-gray-100 px-2 py-1 rounded text-red-600">{requiredPermission}</code>
            </p>
            <button 
              onClick={() => window.history.back()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      );
    }

    // Redirect to fallback path
    return <Navigate to={fallbackPath} replace />;
  }

  console.log(`✅ Access granted: User ${user.email} has permission "${requiredPermission}" for route ${location.pathname}`);
  return children;
};

/**
 * Higher-order component for creating permission-protected routes
 */
export const withPermission = (Component, permission, options = {}) => {
  return (props) => (
    <PermissionGuard permission={permission} {...options}>
      <Component {...props} />
    </PermissionGuard>
  );
};

export default PermissionGuard;