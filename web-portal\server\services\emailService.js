import { sendEmail } from '../config/email.js';
import { adminCreatedEmail } from '../utils/emailTemplates.js';

/**
 * Send welcome email to newly created admin
 * @param {string} email - Admin email address
 * @param {string} name - Admin name
 * @param {string} tempPassword - Temporary password
 * @param {string} roleType - Admin role type
 * @param {string} loginUrl - Optional custom login URL
 */
export const sendAdminCreationEmail = async (email, name, tempPassword, roleType, loginUrl) => {
  try {
    const emailHtml = adminCreatedEmail(name, tempPassword, roleType, loginUrl);
    
    await sendEmail({
      to: email,
      subject: 'Admin Account Created - ModelSuite Talent Portal',
      html: emailHtml,
      text: `Hi ${name}, an admin account has been created for you. Your temporary password is: ${tempPassword}. Please log in and change your password immediately.`
    });
    
    console.log(`✅ Admin creation email sent to ${email}`);
  } catch (error) {
    console.error(`❌ Failed to send admin creation email to ${email}:`, error);
    throw error;
  }
};