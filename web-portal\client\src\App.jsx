import { useEffect, useRef } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { ProtectedRoute } from "./components/ProtectedRoute";
import PermissionGuard from "./components/PermissionGuard";
import AdminPanelRoute from "./components/AdminPanelRoute";
import { ThemeProvider } from "./contexts/ThemeContext";
import { Toaster } from "sonner";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import Certificates from "./pages/Certificates";
import Profile from "./pages/Profile";
import Support from "./pages/Support";

import { useNotificationStore } from "./store/notificationStore";
import { useChatStore } from "./store/chatStore";
import { onMessageListener } from "./config/firebaseConfig";
import RulesAndPolicies from "./pages/RulesAndPolicies";
import ComplianceHistory from "./pages/ComplianceHistory";
import CertificateDetails from "./pages/CertificateDetails";
import DailyReport from "./pages/report/DailyReport";
import ShiftReports from "./pages/report/ShiftReports";
import LeaderboardPage from "./pages/leaderboard/LeaderboardPage";
import ResourcesPage from "./pages/ResourcesPage";
import PaymentHistory from "./pages/PaymentHistory";
import TalentAnnouncements from "./pages/TalentAnnouncements";
import { useAuthStore } from "./store/authStore";
import AdminDashboard from "./pages/admin/AdminDashboard";
import AdminLeaderboard from "./pages/admin/AdminLeaderboard";
import TalentXPDetails from "./pages/admin/TalentXPDetails";
import AdminWeeklySummaries from "./pages/admin/WeeklySummaries";
import AdminAnnouncements from "./pages/admin/AdminAnnouncements";
import AdminNotifications from "./pages/admin/AdminNotifications";
import AdminMeetings from "./pages/admin/AdminMeetings";
import HiringDashboard from "./pages/admin/hiring/HiringDashboard";
import JobsList from "./pages/admin/hiring/JobsList";
import CreateJob from "./pages/admin/hiring/CreateJob";
import EditJob from "./pages/admin/hiring/EditJob";
import ApplicationsList from "./pages/admin/hiring/ApplicationsList";
import ApplicationDetails from "./pages/admin/hiring/ApplicationDetails";
import ExtendOffer from "./pages/admin/hiring/ExtendOffer";
import InterviewsList from "./pages/admin/hiring/InterviewsList";
import OnboardingList from "./pages/admin/hiring/OnboardingList";
import OnboardingDetails from "./pages/admin/hiring/OnboardingDetails";
import JobApplication from "./pages/public/JobApplication";
import OnboardingForm from "./pages/OnboardingForm";
import AcceptOffer from "./pages/hiring/AcceptOffer";
import DeclineOffer from "./pages/hiring/DeclineOffer";
import AdminLeave from "./pages/admin/AdminLeave";
import AdminResourcesEnhanced from "./pages/admin/AdminResourcesEnhanced";
import AdminDocumentManagement from "./pages/admin/AdminDocumentManagement";
import DocumentEdit from "./pages/admin/DocumentEdit";
import AdminReports from "./pages/admin/AdminReports";
import TalentReportsDetail from "./pages/admin/TalentReportsDetail";
import AdminCheckups from "./pages/admin/AdminCheckups";
import CommunicationCenter from "./pages/admin/CommunicationCenter";
import TalentsList from "./pages/admin/TalentsList";
import CreateTalent from "./pages/admin/CreateTalent";
import BulkUpload from "./pages/admin/BulkUpload";
import EditTalent from "./pages/admin/EditTalent";
import EmployeeAnalytics from "./pages/admin/EmployeeAnalytics";
import SimpleDownload from "./pages/SimpleDownload";
import AIFeedbackMonitor from "./pages/admin/AIFeedbackMonitor";
import AuditTrail from "./pages/admin/AuditTrail";
import TalentLeave from "./pages/TalentLeave";
import TalentNotifications from "./pages/TalentNotifications";
import TalentDocuments from "./pages/TalentDocuments";
import TalentMeetingHistory from "./pages/TalentMeetingHistory";
import TasksDashboard from "./pages/admin/tasks/TasksDashboard";
import AllTasks from "./pages/admin/tasks/AllTasks";
import AdminCompletedTasks from "./pages/admin/tasks/CompletedTasks";
import StoredTasks from "./pages/admin/tasks/StoredTasks";
import ArchivedTasks from "./pages/admin/tasks/ArchivedTasks";
import AdminPendingRequests from "./pages/admin/tasks/PendingRequests";
import CreateTask from "./pages/admin/tasks/CreateTask";
import EditTask from "./pages/admin/tasks/EditTask";
import TaskDetails from "./pages/admin/TaskDetails";
import TalentTasksDashboard from "./pages/talent/tasks/TasksDashboard";
import AvailableTasks from "./pages/talent/tasks/AvailableTasks";
import MyQueue from "./pages/talent/tasks/MyQueue";
import TalentPendingRequests from "./pages/talent/tasks/PendingRequests";
import TalentTaskDetails from "./pages/talent/tasks/TalentTaskDetails";
import TalentCompletedTasks from "./pages/talent/tasks/CompletedTasks";

import MeetingAttendance from "./pages/admin/MeetingAttendance";
import AttendanceManagement from "./pages/admin/AttendanceManagement";
import NotificationTest from "./pages/NotificationTest";
import AttendanceManagementNew from "./pages/admin/AttendanceManagementNew";
import AttendanceDebug from "./pages/admin/AttendanceDebug";
import ComplianceDashboard from "./pages/admin/ComplianceDashboard";
import AdminPaymentDashboard from "./pages/admin/AdminPaymentDashboard";
import AIFeedback from "./pages/AIFeedback";
import TalentLayout from "./components/TalentLayout";
import SaturdaySpinTest from "./pages/SaturdaySpinTest";
import Chat from "./pages/Chat";
import XPCalendarPage from "./pages/XPCalendarPage";
import MeetingLog from "./pages/MeetingLog";
import ScreenshotDiscrepancyList from "./pages/admin/ScreenshotDiscrepancyList";
import TalentDiscrepancyDetail from "./pages/admin/TalentDiscrepancyDetail";
import AdminClaimsDashboard from "./pages/admin/AdminClaimsDashboard";
import MyDiscrepancies from "./pages/MyDiscrepancies";
import AdminPanelDashboard from "./pages/admin/admin-panel/AdminPanelDashboard";
import TeamManagement from "./pages/admin/admin-panel/TeamManagement";

function App() {
  const { refreshToken, isAuthenticated, accessToken, isInitializing, user } =
    useAuthStore();
  const { initialize: initializeNotifications, addNotification } =
    useNotificationStore();
  const { initializeSocket, disconnectSocket } = useChatStore();

  // ✅ Add missing refs
  const notificationInitializedRef = useRef(false);
  const socketInitializedRef = useRef(false);

  // ✅ On app start or refresh, call refresh API once
  useEffect(() => {
    const tryRefresh = async () => {
      console.log("🚀 App starting - attempting token refresh...");
      try {
        await refreshToken();
        console.log("✅ Token refreshed successfully on app start");
      } catch (err) {
        console.warn("⚠️ Token refresh failed on app start:", err.message);
        // This is normal if user wasn't logged in or refresh token expired
      }
    };

    // Only try refresh once on app mount
    tryRefresh();
  }, []); // Empty dependency array - only run once

  // Pre-fetch latest announcement when authenticated
  useEffect(() => {
    let cancelled = false;
    const fetchLatestAnnouncement = async () => {
      if (!accessToken) return; // Skip if no token

      try {
        const url = `${
          import.meta.env.VITE_API_URL || "http://localhost:5000"
        }/api/announcements?page=1&limit=1`;
        const options = {
          credentials: "include",
          headers: { Authorization: `Bearer ${accessToken}` },
        };
        const res = await fetch(url, options);
        if (!res.ok) return;
        const data = await res.json();
        if (cancelled) return;
        if (data.announcements && data.announcements.length > 0) {
          // Store on window so Dashboard can pick it up immediately
          try {
            window.__LATEST_ANNOUNCEMENT__ = data.announcements[0];
          } catch (e) {
            // ignore in non-window env
          }
        }
      } catch (err) {
        console.debug("Early announcement fetch failed", err);
      }
    };

    fetchLatestAnnouncement();

    return () => {
      cancelled = true;
    };
  }, [accessToken]);

  // 🔔 Initialize push notifications when user is authenticated
  // NOTE: This ONLY sets up background push notifications (when tab is closed)
  // Service worker handles showing Windows notifications automatically
  useEffect(() => {
    // 🔒 Prevent multiple initializations (React StrictMode calls effects twice)
    if (notificationInitializedRef.current && socketInitializedRef.current) {
      return;
    }

    if (isAuthenticated && accessToken && user?.userId) {
      // Initialize notifications
      if (!notificationInitializedRef.current) {
        console.log("🔔 Initializing background push notifications...");
        notificationInitializedRef.current = true;

        initializeNotifications(accessToken).then((success) => {
          if (success) {
            console.log(
              "✅ Background push notifications initialized successfully"
            );
            console.log(
              "📱 You will receive Windows notifications when the tab is closed"
            );
          } else {
            console.error("❌ Push notification initialization failed");
            notificationInitializedRef.current = false;
          }
        });
      }

      // Initialize socket connection for real-time features and online status
      if (!socketInitializedRef.current) {
        console.log("🔌 Initializing global socket connection...");
        socketInitializedRef.current = true;
        
        initializeSocket(user.userId);
        console.log("✅ Socket initialized globally - user will show as online on all tabs");
      }
    } else if (!isAuthenticated) {
      // Reset refs and disconnect socket when user logs out
      notificationInitializedRef.current = false;
      if (socketInitializedRef.current) {
        console.log("🔌 User logged out - disconnecting socket");
        disconnectSocket();
        socketInitializedRef.current = false;
      }
    }
  }, [isAuthenticated, accessToken, user?.userId, initializeSocket, disconnectSocket]);

  return (
    <ThemeProvider>
      <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <Toaster
          position="top-right"
          richColors
          closeButton
          duration={4000}
          toastOptions={{
            style: {
              background: "hsl(var(--card))",
              color: "hsl(var(--foreground))",
              border: "1px solid hsl(var(--border))",
            },
          }}
        />
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/download" element={<SimpleDownload />} />
          <Route path="/notification-test" element={<NotificationTest />} />
          <Route path="/saturday-spin-test" element={<SaturdaySpinTest />} />
          <Route path="/apply/:slug" element={<JobApplication />} />
          <Route path="/onboarding" element={<OnboardingForm />} />
          <Route path="/offer/accept" element={<AcceptOffer />} />
          <Route path="/offer/decline" element={<DeclineOffer />} />

          {/* Talent Routes - All wrapped in TalentLayout */}
          <Route
            path="/talent"
            element={
              <ProtectedRoute>
                <TalentLayout />
              </ProtectedRoute>
            }
          >
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="profile" element={<Profile />} />
            <Route
              path="messages"
              element={<Navigate to="/talent/chat" replace />}
            />
            <Route path="support" element={<Support />} />
            <Route path="rules" element={<RulesAndPolicies />} />
            <Route path="compliance-history" element={<ComplianceHistory />} />
            <Route path="certificates" element={<Certificates />} />
            <Route path="certificate" element={<CertificateDetails />} />
            <Route path="report/daily" element={<DailyReport />} />
            <Route path="report/history" element={<ShiftReports />} />
            <Route path="leaderboard" element={<LeaderboardPage />} />
            <Route path="resources" element={<ResourcesPage />} />
            <Route path="documents" element={<TalentDocuments />} />
            <Route path="leave" element={<TalentLeave />} />
            <Route path="notifications" element={<TalentNotifications />} />
            <Route path="meetings" element={<TalentMeetingHistory />} />
            <Route path="payments" element={<PaymentHistory />} />
            <Route path="announcements" element={<TalentAnnouncements />} />
            <Route path="ai-feedback" element={<AIFeedback />} />
            <Route path="xp-calendar" element={<XPCalendarPage />} />
            <Route path="chat" element={<Chat />} />

            <Route path="meeting-log" element={<MeetingLog />} />

            {/* Talent Task Management Routes */}
            <Route path="tasks" element={<TalentTasksDashboard />} />
            <Route path="tasks/available" element={<AvailableTasks />} />
            <Route path="tasks/queue" element={<MyQueue />} />
            <Route path="tasks/pending" element={<TalentPendingRequests />} />
            <Route path="tasks/completed" element={<TalentCompletedTasks />} />
            <Route path="tasks/:id" element={<TalentTaskDetails />} />
            <Route path="my-discrepancies" element={<MyDiscrepancies />} />
          </Route>

          {/* Admin Routes */}
          <Route
            path="/admin/dashboard"
            element={
              <ProtectedRoute>
                <AdminDashboard />
              </ProtectedRoute>
            }
          />

          {/* Admin Panel Routes - Restricted Access */}
          <Route
            path="/admin/admin-panel/dashboard"
            element={
              <ProtectedRoute>
                <AdminPanelRoute>
                  <AdminPanelDashboard />
                </AdminPanelRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/admin-panel/talents"
            element={
              <ProtectedRoute>
                <AdminPanelRoute>
                  <TalentsList />
                </AdminPanelRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/admin-panel/announcements"
            element={
              <ProtectedRoute>
                <AdminPanelRoute>
                  <AdminAnnouncements />
                </AdminPanelRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/admin-panel/payments"
            element={
              <ProtectedRoute>
                <AdminPanelRoute>
                  <AdminPaymentDashboard />
                </AdminPanelRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/admin-panel/communications"
            element={
              <ProtectedRoute>
                <AdminPanelRoute>
                  <CommunicationCenter />
                </AdminPanelRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/admin-panel/meetings"
            element={
              <ProtectedRoute>
                <AdminPanelRoute>
                  <AdminMeetings />
                </AdminPanelRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/admin-panel/notifications"
            element={
              <ProtectedRoute>
                <AdminPanelRoute>
                  <AdminNotifications />
                </AdminPanelRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/admin-panel/audit-trail"
            element={
              <ProtectedRoute>
                <AdminPanelRoute>
                  <AuditTrail />
                </AdminPanelRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/admin-panel/team-management"
            element={
              <ProtectedRoute>
                <AdminPanelRoute>
                  <TeamManagement />
                </AdminPanelRoute>
              </ProtectedRoute>
            }
          />

          {/* Leaderboard Routes */}
          <Route
            path="/admin/leaderboard"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminLeaderboard />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/leaderboard/:talentId"
            element={
              <ProtectedRoute>
                <TalentXPDetails />
              </ProtectedRoute>
            }
          />

          {/* Hiring Routes */}
          <Route
            path="/admin/hiring/dashboard"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <HiringDashboard />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/hiring/jobs"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <JobsList />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/hiring/jobs/create"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <CreateJob />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/hiring/jobs/:id/edit"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <EditJob />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/hiring/applications"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <ApplicationsList />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/hiring/applications/:id/extend-offer"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <ExtendOffer />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/hiring/applications/:id"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <ApplicationDetails />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/hiring/interviews"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <InterviewsList />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/hiring/onboarding"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <OnboardingList />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/hiring/onboarding/:id"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <OnboardingDetails />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/weekly-summaries"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminWeeklySummaries />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/talents/*"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <TalentsList />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/talents/create"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <CreateTalent />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/talents/bulk"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <BulkUpload />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/talents/:talentId/edit"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <EditTalent />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/announcements"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminAnnouncements />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/notifications"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminNotifications />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/meetings"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminMeetings />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/meetings/attendance"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <MeetingAttendance />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/meetings/manage"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AttendanceManagementNew />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/debug"
            element={
              <ProtectedRoute>
                <AttendanceDebug />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/resources"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminResourcesEnhanced />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/documents"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminDocumentManagement />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/documents/:documentId/edit"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <DocumentEdit />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/reports"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminReports />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/reports/:talentId"
            element={
              <ProtectedRoute>
                <TalentReportsDetail />
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/checkups"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminCheckups />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/communication-center"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <CommunicationCenter />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/leave"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminLeave />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/tasks"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminTasks />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/tasks/stored"
            element={
              <ProtectedRoute>
                <StoredTasks />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/tasks/completed"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminCompletedTasks />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/tasks/archived"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <ArchivedTasks />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/tasks/requests"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminPendingRequests />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/tasks/create"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <CreateTask />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/tasks/edit/:id"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <EditTask />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/tasks/:id"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <TaskDetails />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/employee-analytics"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <EmployeeAnalytics />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/compliance"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <ComplianceDashboard />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/audit-trail"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AuditTrail />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/admin-panel/payments"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminPaymentDashboard />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/ai-feedback"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AIFeedbackMonitor />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/screenshot-discrepancies"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <ScreenshotDiscrepancyList />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/screenshot-discrepancies/:talentId"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <TalentDiscrepancyDetail />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/claims-dashboard"
            element={
              <ProtectedRoute>
                <PermissionGuard>
                  <AdminClaimsDashboard />
                </PermissionGuard>
              </ProtectedRoute>
            }
          />

          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;
