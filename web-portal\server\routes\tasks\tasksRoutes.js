import express from "express";
import {
  getAvailableTasks,
  getMyQueueTasks,
  getArchivedTasks,
  requestTask,
  approveTaskRequest,
  denyTaskRequest,
  createTask,
  getTaskById,
  getPendingRequests,
  getMyPendingRequests,
  completeTask,
  adminCompleteTask,
  cancelTaskRequest,
  // Phase 3: Section Management
  submitSection,
  approveSection,
  rejectSection,
  // Phase 3: Checklist Management
  completeChecklistItem,
  submitChecklistTask,
  // Phase 4: Deadline Detection
  getOverdueTasks,
  getDueTodayTasks,
  getDueThisWeekTasks,
  // Admin Management
  updateTask,
  deleteTask,
  assignTask,
  reassignTask,
  updateAdminNotes,
  getTaskStats,
  getAllTasks,
  // Bulk Operations
  bulkArchiveTasks,
  bulkReassignTasks,
  bulkDeleteTasks,
  bulkRestoreTasks,
  // Talent Stats
  getTalentStats,
  // Employee View
  getEmployeeTaskSummary,
  getTasksByEmployee,
  // Admin Action Items
  getTasksNeedingAction,
  // Attachment Download
  downloadTaskAttachment,
  // Activate Stored Task
  activateStoredTask,
  // Checklist Item Revisions
  requestChecklistItemRevision,
  respondToChecklistItemRevision,
  uploadRevisionImage,
  uploadAnnotatedScreenshot,
} from "../../controllers/tasks/taskController.js";
import {
  getTaskDetails,
  toggleChecklistItem,
  addComment,
  updateTaskStatus,
} from "../../controllers/tasks/taskWorkController.js";
import {
  authenticateToken,
  requireActiveStatus,
} from "../../middleware/auth.js";
import { requireAdmin } from "../../middleware/adminAuth.js";
import {
  validate,
  validateQuery,
  createTaskSchema,
  taskFiltersSchema,
  denyRequestSchema,
  // Phase 3 validators
  submitSectionSchema,
  rejectSectionSchema,
  completeChecklistItemSchema,
  submitChecklistTaskSchema,
} from "../../validators/taskValidators.js";
import uploadTaskAttachments, {
  handleMulterError,
} from "../../middleware/uploadTaskAttachments.js";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);
router.use(requireActiveStatus);

// ===========================
// Public Task Routes (Talent + Admin)
// ===========================

// Get talent statistics
router.get("/stats", getTalentStats);

// Get available tasks (Inbox)
router.get("/available", validateQuery(taskFiltersSchema), getAvailableTasks);

// Get my queue tasks (In Progress + Needs Revision)
router.get("/my-queue", getMyQueueTasks);

// Get my pending requests (Talent)
router.get("/my-pending-requests", getMyPendingRequests);

// Get archived/completed tasks
router.get("/archive", getArchivedTasks);

// Phase 4: Deadline filters
router.get("/overdue", getOverdueTasks);
router.get("/due-today", getDueTodayTasks);
router.get("/due-this-week", getDueThisWeekTasks);

// Get task by ID
router.get("/:id", getTaskById);

// Get task details for work (with sections, checklist, comments)
router.get("/:id/details", getTaskDetails);

// Request to work on a task (Talent)
router.post("/:id/request", requestTask);

// Toggle checklist item completion (Talent)
router.post("/:id/checklist/:itemId/toggle", toggleChecklistItem);

// Add comment to task (Talent + Admin)
router.post("/:id/comments", addComment);

// Update task status (Talent)
router.put("/:id/status", updateTaskStatus);

// Complete task (Talent)
router.post("/:id/complete", completeTask);

// Cancel task request (Talent)
router.delete("/:id/cancel-request", cancelTaskRequest);

// ===========================
// Admin-Only Routes
// ===========================

// Create new task (with file uploads)
router.post(
  "/",
  requireAdmin,
  uploadTaskAttachments.any(), // Accept any field names for checklist images
  handleMulterError,
  validate(createTaskSchema),
  createTask
);

// Get pending requests (Admin dashboard)
router.get("/admin/pending-requests", requireAdmin, getPendingRequests);

// Approve task request
router.post("/:id/approve-request", requireAdmin, approveTaskRequest);

// Deny task request
router.post(
  "/:id/deny-request",
  requireAdmin,
  validate(denyRequestSchema),
  denyTaskRequest
);

// Admin completes task (for pending-review tasks)
router.post("/:id/admin-complete", requireAdmin, adminCompleteTask);

// Update task details (with file uploads)
router.put(
  "/:id",
  requireAdmin,
  uploadTaskAttachments.any(), // Accept any field names for new attachments and checklist images
  handleMulterError,
  updateTask
);

// Delete/Archive task
router.delete("/:id", requireAdmin, deleteTask);

// Direct assign task to talent
router.post("/:id/assign", requireAdmin, assignTask);

// Reassign task to different talent
router.post("/:id/reassign", requireAdmin, reassignTask);

// Update admin notes
router.put("/:id/admin-notes", requireAdmin, updateAdminNotes);

// Activate stored task
router.put("/:id/activate", requireAdmin, activateStoredTask);

// Get task statistics
router.get("/admin/stats", requireAdmin, getTaskStats);

// Get all tasks with advanced filters
router.get("/admin/all", requireAdmin, getAllTasks);

// Get tasks needing admin action
router.get("/admin/needs-action", requireAdmin, getTasksNeedingAction);

// Get employee task summary (for By Employee view)
router.get("/admin/employee-summary", requireAdmin, getEmployeeTaskSummary);

// Get tasks for specific employee
router.get("/admin/by-employee/:talentId", requireAdmin, getTasksByEmployee);

// ===========================
// Phase 3: Section Management Routes
// ===========================

// Submit section work with PR link (Talent)
router.post(
  "/:id/sections/:sectionNum/submit",
  uploadTaskAttachments.array("screenshots", 5), // Accept up to 5 screenshots
  handleMulterError,
  validate(submitSectionSchema),
  submitSection
);

// Approve section (Admin)
router.post("/:id/sections/:sectionNum/approve", requireAdmin, approveSection);

// Reject section with feedback and issues (Admin)
// Accepts multipart/form-data with screenshot uploads
router.post(
  "/:id/sections/:sectionNum/reject",
  requireAdmin,
  uploadTaskAttachments.array("screenshots", 10), // Allow up to 10 screenshots
  handleMulterError,
  rejectSection
);

// ===========================
// Phase 3: Checklist Management Routes
// ===========================

// Mark checklist item complete (Talent) with optional screenshot uploads
router.put(
  "/:id/checklist/:itemNum/complete",
  uploadTaskAttachments.array("screenshots", 5), // Accept up to 5 screenshots
  handleMulterError,
  validate(completeChecklistItemSchema),
  completeChecklistItem
);

// Submit entire checklist task for review (Talent)
router.post(
  "/:id/checklist/submit",
  validate(submitChecklistTaskSchema),
  submitChecklistTask
);

// ===========================
// Bulk Operations (Admin Only)
// ===========================

// Bulk archive tasks
router.post("/bulk/archive", requireAdmin, bulkArchiveTasks);

// Bulk reassign tasks
router.post("/bulk/reassign", requireAdmin, bulkReassignTasks);

// Bulk permanently delete tasks
router.post("/bulk/delete", requireAdmin, bulkDeleteTasks);

// Bulk restore archived tasks
router.post("/bulk/restore", requireAdmin, bulkRestoreTasks);

// ===========================
// Phase 4: Test Deadline Notifications (Admin Only)
// ===========================
router.post("/test/deadline-notifications", requireAdmin, async (req, res) => {
  try {
    const dailyDeadlineNotificationJob = req.app.get(
      "dailyDeadlineNotificationJob"
    );
    if (!dailyDeadlineNotificationJob) {
      return res.status(500).json({ error: "Cron job not initialized" });
    }

    await dailyDeadlineNotificationJob.runNow();
    res.json({
      success: true,
      message: "Deadline notifications job triggered successfully",
    });
  } catch (error) {
    console.error("Test deadline notifications error:", error);
    res.status(500).json({ error: "Failed to trigger notifications" });
  }
});

// ===========================
// Download Task Attachment
// ===========================
router.get("/:taskId/attachment/:attachmentId", downloadTaskAttachment);

// ===========================
// Checklist Item Revisions
// ===========================
// Upload image for checklist item revision
router.post(
  "/upload-revision-image",
  uploadTaskAttachments.single("image"),
  handleMulterError,
  uploadRevisionImage
);

// Admin request revision on individual checklist item
router.post(
  "/:taskId/checklist/:itemNumber/revision",
  requireAdmin,
  uploadTaskAttachments.any(),
  handleMulterError,
  requestChecklistItemRevision
);

// Talent respond to checklist item revision
router.post(
  "/:taskId/checklist/:itemNumber/respond",
  respondToChecklistItemRevision
);

// ===========================
// Image Annotation (Admin Only)
// ===========================
// Upload annotated screenshot linked to original
router.post(
  "/:taskId/annotate-screenshot",
  requireAdmin,
  uploadTaskAttachments.single("annotatedImage"),
  handleMulterError,
  uploadAnnotatedScreenshot
);

export default router;
