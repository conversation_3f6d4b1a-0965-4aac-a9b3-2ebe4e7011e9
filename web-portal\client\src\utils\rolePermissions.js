/**
 * Centralized Permission System
 * Handles all admin/talent permission logic in one place
 */

// Predefined roles with specific access patterns
export const TEAM_ROLES = {
  super_admin: {
    name: 'Super Admin',
    description: 'Full system access - CEO level',
    color: '#ef4444',
    accessibleTabs: ['employee', 'hiring', 'admin'],
    canAccessTalentPortal: true, // <PERSON> can access talent portal too
    canManageTeam: true,
    permissions: [
      // Employee permissions
      'employee.dashboard.view', 'employee.talents.view', 'employee.talents.create',
      'employee.talents.edit', 'employee.talents.delete', 'employee.reports.view',
      'employee.reports.manage', 'employee.meetings.view', 'employee.meetings.manage',
      'employee.leave.view', 'employee.leave.approve', 'employee.tasks.view',
      'employee.tasks.manage', 'employee.announcements.view', 'employee.announcements.manage',
      'employee.analytics.view', 'employee.payments.view',
      
      // Hiring permissions  
      'hiring.dashboard.view', 'hiring.jobs.view', 'hiring.jobs.create',
      'hiring.jobs.edit', 'hiring.jobs.delete', 'hiring.applications.view',
      'hiring.applications.manage', 'hiring.interviews.view', 'hiring.interviews.schedule',
      'hiring.interviews.manage', 'hiring.onboarding.view', 'hiring.onboarding.manage',
      
      // Admin panel permissions
      'admin.panel.view', 'admin.team.view', 'admin.team.invite',
      'admin.team.edit', 'admin.team.remove', 'admin.system.settings', 'admin.audit.view',
      
      // Admin management permissions (super admin only)
      'admin.management.view', 'admin.management.create', 'admin.management.edit',
      'admin.management.delete', 'admin.management.*'
    ]
  },

  full_admin: {
    name: 'Full Administrator',
    description: 'Full access to employee and hiring modules',
    color: '#8b5cf6',
    accessibleTabs: ['employee', 'hiring'],
    canAccessTalentPortal: false,
    canManageTeam: false,
    permissions: [
      // Employee permissions
      'employee.dashboard.view', 'employee.talents.view', 'employee.talents.create',
      'employee.talents.edit', 'employee.reports.view', 'employee.reports.manage',
      'employee.meetings.view', 'employee.meetings.manage', 'employee.leave.view',
      'employee.leave.approve', 'employee.tasks.view', 'employee.tasks.manage',
      'employee.announcements.view', 'employee.announcements.manage', 'employee.analytics.view',
      
      // Hiring permissions
      'hiring.dashboard.view', 'hiring.jobs.view', 'hiring.jobs.create',
      'hiring.jobs.edit', 'hiring.applications.view', 'hiring.applications.manage',
      'hiring.interviews.view', 'hiring.interviews.schedule', 'hiring.interviews.manage',
      'hiring.onboarding.view', 'hiring.onboarding.manage'
    ]
  },

  hiring_manager: {
    name: 'Hiring Manager',
    description: 'Manages recruitment and hiring process only',
    color: '#10b981',
    accessibleTabs: ['hiring'],
    canAccessTalentPortal: false,
    canManageTeam: false,
    permissions: [
      'hiring.dashboard.view', 'hiring.jobs.view', 'hiring.jobs.create',
      'hiring.jobs.edit', 'hiring.applications.view', 'hiring.applications.manage',
      'hiring.interviews.view', 'hiring.interviews.schedule', 'hiring.interviews.manage',
      'hiring.onboarding.view', 'hiring.onboarding.manage'
    ]
  },

  employee_manager: {
    name: 'Employee Manager',
    description: 'Manages employee-related activities only',
    color: '#3b82f6',
    accessibleTabs: ['employee'],
    canAccessTalentPortal: false,
    canManageTeam: false,
    permissions: [
      'employee.dashboard.view', 'employee.talents.view', 'employee.talents.edit',
      'employee.reports.view', 'employee.reports.manage', 'employee.meetings.view',
      'employee.meetings.manage', 'employee.leave.view', 'employee.leave.approve',
      'employee.tasks.view', 'employee.tasks.manage', 'employee.announcements.view',
      'employee.announcements.manage', 'employee.analytics.view'
    ]
  },

  team_lead: {
    name: 'Team Lead',
    description: 'View access to employee and hiring modules',
    color: '#f59e0b',
    accessibleTabs: ['employee', 'hiring'],
    canAccessTalentPortal: false,
    canManageTeam: false,
    permissions: [
      'employee.dashboard.view', 'employee.talents.view', 'employee.reports.view',
      'employee.meetings.view', 'employee.tasks.view', 'employee.analytics.view',
      'hiring.dashboard.view', 'hiring.jobs.view', 'hiring.applications.view',
      'hiring.interviews.view'
    ]
  }
};

/**
 * Get user's role configuration
 * @param {Object} user - User object from auth store
 * @returns {Object} - Role configuration or null
 */
export const getUserRole = (user) => {
  if (!user) return null;
  
  // Debug logging (development only)
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 [getUserRole] User:', user.email, 'Type:', user.userType, 'Role:', user.roleType, 'PermissionMode:', user.permissionMode);
  }
  
  // 1. Check for database-driven roles (PRIMARY SYSTEM)
  
  // For Admin users with custom permissions
  if (user.userType === 'admin' && user.permissionMode === 'custom' && Array.isArray(user.customPermissions)) {
    return {
      name: 'Custom Permissions',
      description: `Custom access to ${user.customPermissions.length} features`,
      color: '#6366f1',
      accessibleTabs: ['employee'], // Default to employee tab for custom permissions
      canAccessTalentPortal: user.canAccessTalentPortal || false,
      canManageTeam: user.customPermissions.includes('admin_management'),
      permissions: user.customPermissions.map(feature => `custom.${feature}`), // Prefix with custom
      customPermissions: user.customPermissions,
      isCustom: true
    };
  }
  
  // For Admin users with roleType field (standard roles)
  if (user.userType === 'admin' && user.roleType && TEAM_ROLES[user.roleType]) {
    return TEAM_ROLES[user.roleType];
  }
  
  // For Talent users with admin privileges (like Patrick before migration)
  if (user.userType === 'talent' && user.adminRoleType && TEAM_ROLES[user.adminRoleType]) {
    return TEAM_ROLES[user.adminRoleType];
  }
  
  // 2. Legacy CEO role detection for talent users (Patrick fallback)
  if (user.userType === 'talent' && user.roles?.includes('CEO')) {
    console.log('CEO role detected for:', user.email);
    return TEAM_ROLES.super_admin;
  }
  
  // 3. Default fallback for admin users without roleType (should not happen in production)
  if (user.userType === 'admin') {
    console.warn('Admin user without roleType detected:', user.email);
    return TEAM_ROLES.employee_manager;
  }
  
  return null;
};

/**
 * Check if user can access a specific tab
 * @param {Object} user - User object from auth store
 * @param {string} tabName - Tab name: 'employee', 'hiring', 'admin'
 * @returns {boolean}
 */
export const canAccessTab = (user, tabName) => {
  const role = getUserRole(user);
  if (!role) return false;
  
  return role.accessibleTabs.includes(tabName);
};

/**
 * Check if user has specific permission
 * @param {Object} user - User object from auth store
 * @param {string} permission - Permission string like 'employee.talents.view'
 * @returns {boolean}
 */
export const hasPermission = (user, permission) => {
  const role = getUserRole(user);
  if (!role) return false;
  
  return role.permissions.includes(permission);
};

/**
 * Get user's accessible tabs
 * @param {Object} user - User object from auth store
 * @returns {string[]} - Array of accessible tab names
 */
export const getUserAccessibleTabs = (user) => {
  const role = getUserRole(user);
  return role ? role.accessibleTabs : [];
};

/**
 * Check if user can access talent portal (for dual access like Patrick)
 * @param {Object} user - User object from auth store
 * @returns {boolean}
 */
export const canAccessTalentPortal = (user) => {
  const role = getUserRole(user);
  if (!role) return false;
  
  // Check role-based access (super_admin has this by default)
  if (role.canAccessTalentPortal) {
    return true;
  }
  
  // Check database flag for specific users (like migrated Patrick)
  if (user.canAccessTalentPortal === true) {
    return true;
  }
  
  return false;
};

/**
 * Check if user has access to a specific feature
 * @param {Object} user - User object from auth store
 * @param {string} feature - Feature name like 'talents', 'payments', 'meetings'
 * @returns {boolean}
 */
export const hasFeatureAccess = (user, feature) => {
  const role = getUserRole(user);
  if (!role) return false;
  
  // For custom permission users, check their custom permissions
  if (role.isCustom && role.customPermissions) {
    return role.customPermissions.includes(feature);
  }
  
  // For role-based users, use the existing permission system
  // Map features to permission patterns
  const featurePermissionMap = {
    talents: 'employee.talents.view',
    leaderboard: 'employee.analytics.view', 
    payments: 'employee.payments.view',
    meetings: 'employee.meetings.view',
    reports: 'employee.reports.view',
    analytics: 'employee.analytics.view',
    tasks: 'employee.tasks.view',
    announcements: 'employee.announcements.view',
    leave: 'employee.leave.view',
    communication: 'employee.communication.view',
    documents: 'employee.documents.view',
    compliance: 'employee.compliance.view'
  };
  
  const permission = featurePermissionMap[feature];
  return permission ? hasPermission(user, permission) : false;
};

/**
 * Check if user can manage team members
 * @param {Object} user - User object from auth store
 * @returns {boolean}
 */
export const canManageTeam = (user) => {
  const role = getUserRole(user);
  return role ? role.canManageTeam : false;
};

/**
 * Get role display info for UI
 * @param {Object} user - User object from auth store
 * @returns {Object} - Role display information
 */
export const getRoleDisplayInfo = (user) => {
  const role = getUserRole(user);
  if (!role) return { name: 'No Role', color: '#6b7280', description: 'No permissions assigned' };
  
  return {
    name: role.name,
    color: role.color,
    description: role.description
  };
};

/**
 * Legacy compatibility - hasAdminAccess function
 * @param {Object} user - User object from auth store
 * @returns {boolean}
 */
export const hasAdminAccess = (user) => {
  const role = getUserRole(user);
  return role !== null; // If they have any role, they have some admin access
};

/**
 * Legacy compatibility - canViewPayments function
 * @param {Object} user - User object from auth store
 * @returns {boolean}
 */
export const canViewPayments = (user) => {
  return hasPermission(user, 'employee.payments.view') || hasFeatureAccess(user, 'payments');
};

export default {
  TEAM_ROLES,
  getUserRole,
  canAccessTab,
  hasPermission,
  hasFeatureAccess,
  getUserAccessibleTabs,
  canAccessTalentPortal,
  canManageTeam,
  getRoleDisplayInfo,
  hasAdminAccess,
  canViewPayments
};