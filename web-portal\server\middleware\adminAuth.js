export function requireAdmin(req, res, next) {
  // Check if user is authenticated (set by auth middleware)
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // Check if user has admin access (either userType is admin OR talent with admin roles)
  const hasAdminAccess = req.user.userType === 'admin' || 
    (req.user.userType === 'talent' && req.user.roles && req.user.roles.some(role => 
      ['Admin', 'Super-Admin', 'CEO', 'Talent-Portal-Manager'].includes(role)
    ));

  if (!hasAdminAccess) {
    return res.status(403).json({ error: 'Admin access required' });
  }

  next();
}

// Middleware to require super admin role
export const requireSuperAdmin = async (req, res, next) => {
  try {
    const { user } = req;
    
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }
    
    // Check if user is admin with super_admin role
    if (user.userType !== 'admin' || user.roleType !== 'super_admin') {
      return res.status(403).json({ 
        error: 'Super admin access required',
        userRole: user.roleType,
        userType: user.userType
      });
    }
    
    next();
  } catch (error) {
    console.error('Super admin middleware error:', error);
    res.status(500).json({ error: 'Authorization check failed' });
  }
};
