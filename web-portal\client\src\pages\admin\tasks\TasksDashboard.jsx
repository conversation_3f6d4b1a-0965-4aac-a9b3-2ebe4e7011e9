import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import AdminLayout from "../../../components/admin/AdminLayout";
import { useAuthStore } from "../../../store/authStore";
import { useAdminTaskStore } from "../../../store/adminTaskStore";
import { getUserRole } from "../../../utils/rolePermissions";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../components/ui/card";
import {
  HiClipboardList,
  HiClock,
  HiExclamationCircle,
  HiCheckCircle,
  HiArrowSmUp,
} from "react-icons/hi";

export default function TasksDashboard() {
  const navigate = useNavigate();
  const { user, isInitializing, accessToken } = useAuthStore();
  const { stats, loading, fetchStats } = useAdminTaskStore();
  const [selectedProject, setSelectedProject] = useState("all");

  // Check admin access
  useEffect(() => {
    if (isInitializing) return;
    const userRole = getUserRole(user);
    if (!userRole) {
      navigate("/talent/dashboard");
    }
  }, [user, isInitializing, navigate]);

  // Fetch stats
  useEffect(() => {
    const userRole = getUserRole(user);
    if (userRole && accessToken) {
      fetchStats(selectedProject);
    }
  }, [user, selectedProject, accessToken]);

  return (
    <AdminLayout>
      <div className="flex-1 p-6">
        {/* Header with Horizontal Tabs */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Task Management
              </h1>
              <p className="text-muted-foreground mt-1">
                Manage tasks, assignments, and review submissions
              </p>
            </div>
            <div className="flex items-center gap-3">
              <select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="px-4 py-2 rounded-lg border border-border bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="all">All Projects</option>
                <option value="Talent Portal">Talent Portal</option>
                <option value="ModelSuite">ModelSuite</option>
              </select>
            </div>
          </div>

          {/* Horizontal Navigation Tabs */}
          <div className="flex gap-2 border-b border-border mt-4">
            <button
              onClick={() => navigate("/admin/tasks")}
              className="px-4 py-3 border-b-2 border-primary text-primary font-semibold transition-colors"
            >
              Dashboard
            </button>
            <button
              onClick={() => navigate("/admin/tasks/all")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Active Tasks
            </button>
            <button
              onClick={() => navigate("/admin/tasks/stored")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Store
            </button>
            <button
              onClick={() => navigate("/admin/tasks/completed")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Completed
            </button>
            <button
              onClick={() => navigate("/admin/tasks/requests")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Requests
            </button>
            <button
              onClick={() => navigate("/admin/tasks/create")}
              className="px-4 py-3 border-b-2 border-transparent text-muted-foreground hover:text-foreground transition-colors"
            >
              Create Task
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        {loading ? (
          <div className="flex items-center justify-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Total Tasks */}
            <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Tasks
                </CardTitle>
                <HiClipboardList className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.totalTasks || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {stats?.talentPortalTasks || 0} Portal •{" "}
                  {stats?.modelSuiteTasks || 0} ModelSuite
                </p>
              </CardContent>
            </Card>

            {/* In Progress */}
            <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  In Progress
                </CardTitle>
                <HiArrowSmUp className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.byStatus?.["in-progress"] || 0}
                </div>
                <p className="text-xs text-muted-foreground">Active tasks</p>
              </CardContent>
            </Card>

            {/* Completed */}
            <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                <HiCheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.byStatus?.completed || 0}
                </div>
                <p className="text-xs text-muted-foreground">All time</p>
              </CardContent>
            </Card>

            {/* Overdue */}
            <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overdue</CardTitle>
                <HiExclamationCircle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.overdueTasks || 0}
                </div>
                <p className="text-xs text-red-600">
                  {stats?.overdueTasks > 0 ? "Needs attention" : "On track"}
                </p>
              </CardContent>
            </Card>

            {/* Completion Rate */}
            <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Completion Rate
                </CardTitle>
                <HiCheckCircle className="h-4 w-4 text-emerald-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.completionRate?.toFixed(1) || 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {stats?.completedThisMonth || 0} in 30d
                </p>
              </CardContent>
            </Card>

            {/* Due This Week */}
            <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Due This Week
                </CardTitle>
                <HiClock className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.dueThisWeek || 0}
                </div>
                <p className="text-xs text-muted-foreground">This week</p>
              </CardContent>
            </Card>

            {/* Needs Revision */}
            <Card className="border border-border/40 backdrop-blur-sm bg-card/95">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Needs Revision
                </CardTitle>
                <HiExclamationCircle className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.byStatus?.["needs-revision"] || 0}
                </div>
                <p className="text-xs text-muted-foreground">Needs work</p>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
