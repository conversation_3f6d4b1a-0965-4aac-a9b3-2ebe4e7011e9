import mongoose from "mongoose";

const talentSchema = new mongoose.Schema(
  {
    talentId: { type: String, required: true, unique: true, trim: true },
    name: { type: String, required: true, trim: true },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
    }, // Personal email
    companyEmail: {
      type: String,
      unique: true,
      sparse: true, // This allows multiple null/undefined values
      lowercase: true,
      trim: true,
      // No default value - field will be undefined if not provided
    }, // Company email for Jibble
    jibbleEmployeeId: {
      type: String,
      default: null,
      trim: true,
    }, // Cached Jibble employee ID for quick lookups
    lastJibbleClockInTime: {
      type: Date,
      default: null,
    }, // Track last Jibble clock-in time for 3-hour check-up feature
    passwordHash: { type: String, required: true },
    status: {
      type: String,
      enum: ["invited", "active", "suspended", "alumni"],
      default: "active",
    },
    roles: {
      type: [String],
      enum: [
        "Talent",
        "Frontend-Manager",
        "Backend-Manager",
        "Project-Manager",
        "CEO",
        "Talent-Portal-Manager",
        "Admin",
        "Super-Admin",
      ], // Added Admin roles
      default: ["Talent"],
    },
    adminPermissions: {
      type: [String],
      default: [],
      // Will contain admin permissions like ['manage_talents', 'manage_batches', etc.]
    },
    adminRoleType: {
      type: String,
      enum: ['super_admin', 'full_admin', 'hiring_manager', 'employee_manager', 'team_lead'],
      default: null, // null means no admin role, only for talents with admin access like Patrick
    },
    canViewPayments: {
      type: Boolean,
      default: false,
    },
    batchId: {
      type: String,
      default: null,
    },
    joinedAt: {
      type: Date,
      default: null,
    },
    xp: {
      type: Number,
      default: 0,
    },
    level: {
      type: Number,
      default: 0,
    },
    streak: {
      type: Number,
      default: 0,
    },
    longestStreak: {
      type: Number,
      default: 0,
    },
    lastReportDate: {
      type: Date,
    },
    badges: {
      type: [String], // Achievement badges (streak, compliance, etc.)
      default: [],
    },
    levelBadges: {
      type: [String], // Level-based badges (badge_1, badge_2, etc.)
      default: [],
    },
    currentCheckinId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Checkin",
      default: null,
    },
    profilePicture: {
      type: String,
      default: null,
    },
    phone: {
      type: String,
      default: null,
    },
    group: {
      type: String,
      enum: ["core", "growth"],
      default: null,
    },
    assignedProject: {
      type: String,
      enum: {
        values: ["Talent Portal", "ModelSuite"],
        message:
          "Assigned project must be either 'Talent Portal' or 'ModelSuite'",
      },
      required: [true, "Assigned project is required"],
      index: true,
    },
    hasFullAccess: {
      type: Boolean,
      default: false,
      index: true,
    },
    designation: {
      type: String,
      enum: [
        "Frontend Developer",
        "Backend Developer",
        "Full Stack Developer",
        "Software Engineer",
        "UI/UX Designer",
        "Project Manager",
        "CEO",
        "CTO",
        "Product Manager",
        "DevOps Engineer",
        "QA Engineer",
        "Data Scientist",
        "Mobile Developer",
        "Other",
      ],
      default: null,
    },
    country: {
      type: String,
      default: "India", // Default to India since most talents are from there
      trim: true,
    },
    timezone: {
      type: String,
      default: "Asia/Kolkata", // Default timezone for India
      trim: true,
    },
    // 🔔 Push Notification Fields - Enhanced with device management
    fcmTokens: [
      {
        token: {
          type: String,
          required: true,
        },
        platform: {
          type: String,
          enum: ["web", "android", "ios", "desktop"],
          default: "web",
        },
        deviceId: {
          type: String,
          required: true,
          index: true,
        },
        userAgent: {
          type: String,
          default: "",
        },
        lastUsed: {
          type: Date,
          default: Date.now,
        },
        isActive: {
          type: Boolean,
          default: true,
        },
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    notificationSettings: {
      enabled: { type: Boolean, default: true },
      messages: { type: Boolean, default: true },
    },
    // Payment tracking fields
    accumulatedBalance: {
      type: Number,
      default: 0,
      min: 0,
    },
    totalEarned: {
      type: Number,
      default: 0,
      min: 0,
    },
    totalPaid: {
      type: Number,
      default: 0,
      min: 0,
    },
    lastPaymentDate: {
      type: Date,
      default: null,
    },
    certificateEligible: {
      type: Boolean,
      default: false,
    },
    // Rules and Terms acceptance
    hasAcceptedRules: {
      type: Boolean,
      default: false,
    },
    rulesAcceptedAt: {
      type: Date,
      default: null,
    },
    rulesVersion: {
      type: String,
      default: null, // Track which version of rules they accepted
    },
    // Saturday Spin viewing tracking
    viewedSaturdaySpins: [
      {
        eventId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "SaturdaySpinEvent",
          required: true,
        },
        viewedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    // Employment type and required hours
    employmentType: {
      type: String,
      enum: [
        "Internship Ongoing",
        "Internship Completed",
        "Full Time Developer",
        "Project Manager",
        "Recruiting",
      ],
      default: "Internship Ongoing",
    },
    requiredDailyHours: {
      type: Number,
      default: function () {
        return this.employmentType && this.employmentType.includes("Internship")
          ? 4
          : 7;
      },
    },
  },
  { timestamps: true }
);

export default mongoose.model("Talent", talentSchema);
