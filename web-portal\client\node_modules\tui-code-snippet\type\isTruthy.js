/**
 * @fileoverview Check whether the given variable is truthy or not.
 * <AUTHOR> FE Development Lab <<EMAIL>>
 */

'use strict';

var isExisty = require('./isExisty');

/**
 * Check whether the given variable is truthy or not.
 * If the given variable is not null or not undefined or not false, returns true.
 * (It regards 0 as true)
 * @param {*} obj - Target for checking
 * @returns {boolean} Is truthy?
 * @memberof module:type
 */
function isTruthy(obj) {
  return isExisty(obj) && obj !== false;
}

module.exports = isTruthy;
