import { Outlet, useLocation, Navigate } from "react-router-dom";
import { useEffect } from "react";
import Navbar from "./Navbar";
import backgroundImage from "../images/background.png";
import { useChatStore } from "../store/chatStore";
import { useCheckupStore } from "../store/checkupStore";
import { useAuthStore } from "../store/authStore";
import { hasDetailedPermission } from "../utils/permissionMatrix";
import CheckupModal from "./CheckupModal";

export default function TalentLayout() {
  const location = useLocation();
  const { isFullscreen } = useChatStore();
  const { user } = useAuthStore();
  const { startTimer, stopTimer } = useCheckupStore();
  const isChatRoute = location.pathname.includes("/chat");

  // Check if user should have access to talent portal
  // Only allow if user is a talent OR if they're an admin with canAccessTalentPortal permission
  if (user && user.userType === 'admin') {
    // Allow super admins with talent portal access (like <PERSON>)
    if (user.canAccessTalentPortal === true) {
      // Super admin can access talent portal - skip the redirect
    } else {
      // Regular admins should be redirected to admin dashboard
      const hasAnyAdminPermission = hasDetailedPermission(user, 'admin', 'dashboard', 'view') ||
        hasDetailedPermission(user, 'employee', 'analytics', 'view') ||
        hasDetailedPermission(user, 'employee', 'tasks', 'view') ||
        hasDetailedPermission(user, 'employee', 'talents', 'view') ||
        hasDetailedPermission(user, 'hiring', 'applications', 'view') ||
        hasDetailedPermission(user, 'admin', 'system', 'view');
      
      if (hasAnyAdminPermission) {
        return <Navigate to="/admin/dashboard" replace />;
      }
    }
  }

  // Start timer when user logs in and has clock-in time
  useEffect(() => {
    if (user?.lastJibbleClockInTime) {
      console.log("⏰ Starting 3-hour check-up timer for user:", user.name);
      // Stop any existing timer first to prevent race conditions
      stopTimer();
      startTimer(user.lastJibbleClockInTime);
    } else {
      // Stop timer if user logs out or clock-in time is removed
      stopTimer();
    }

    // Cleanup on unmount or logout
    return () => {
      stopTimer();
    };
  }, [user?.lastJibbleClockInTime, startTimer, stopTimer]);

  // Automatically determine active tab based on current route
  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes("/dashboard")) return "Dashboard";
    if (path.includes("/certificates")) return "Certificates";
    if (path.includes("/certificate")) return "Certificates";
    if (path.includes("/tasks")) return "Tasks";
    if (path.includes("/leaderboard")) return "Leaderboard";
    if (path.includes("/resources")) return "Resources";
    if (path.includes("/documents")) return "Documents";
    if (path.includes("/leave")) return "Leave";
    if (path.includes("/payments")) return "Payments";
    if (path.includes("/rules")) return "Rules";
    if (path.includes("/profile")) return "Profile";
    if (path.includes("/support")) return "Support";
    if (path.includes("/announcements")) return "Announcements";
    if (path.includes("/report")) return "Reports";
    if (path.includes("/messages")) return "Messages";
    if (path.includes("/chat")) return "Chat";
    if (path.includes("/discrepancies")) return "Discrepancies";
    return "Dashboard"; // Default
  };

  return (
    <div
      className="h-screen flex flex-col relative overflow-hidden"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        backgroundAttachment: "fixed",
      }}
    >
      {/* Dark overlay for better contrast */}
      <div className="absolute inset-0 bg-black/40 pointer-events-none"></div>

      {/* Main background gradient overlays */}
      <div
        className="absolute top-0 right-0 w-96 h-full pointer-events-none"
        style={{
          background:
            "radial-gradient(ellipse at top right, rgba(59, 130, 246, 0.08), transparent 60%)",
        }}
      ></div>
      <div
        className="absolute top-0 left-0 w-64 h-full pointer-events-none"
        style={{
          background:
            "radial-gradient(ellipse at top left, rgba(99, 102, 241, 0.05), transparent 60%)",
        }}
      ></div>

      {/* Navbar - Hidden in fullscreen mode */}
      {!(isChatRoute && isFullscreen) && <Navbar activeTab={getActiveTab()} />}

      {/* 3-Hour Check-up Modal */}
      <CheckupModal />

      {/* Page Content - Scrollable */}
      <div className="flex-1 overflow-y-auto relative">
        <Outlet />
      </div>
    </div>
  );
}
