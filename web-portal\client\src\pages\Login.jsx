import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { getUserRole } from '../utils/rolePermissions';
import { Eye, EyeOff } from 'lucide-react';
import logoImage from '../images/logo.png';
import loginBg from '../images/background.png';
import GlowingDotsBackground from '../components/GlowingDotsBackground';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const videoRef = useRef(null);
  const loadingVideoRef = useRef(null);
  const navigate = useNavigate();

  const { login, loading, error, clearError, isAuthenticated, user, isInitializing } = useAuthStore();

  // Ensure video plays on component mount
  useEffect(() => {
    const ensureVideoPlays = async (video) => {
      if (!video) return;

      try {
        video.currentTime = 0;
        video.muted = true;
        video.playsInline = true;
        video.load();

        await new Promise((resolve) => {
          if (video.readyState >= 3) {
            resolve();
          } else {
            video.addEventListener('canplay', resolve, { once: true });
          }
        });

        await video.play();
      } catch (err) {
        setTimeout(() => {
          video.play().catch(() => { });
        }, 100);
      }
    };

    ensureVideoPlays(loadingVideoRef.current);
    ensureVideoPlays(videoRef.current);
  }, []);

  // Redirect if user is already authenticated
  useEffect(() => {
    if (!isInitializing && isAuthenticated && user) {
      // Redirect based on role system
      const userRole = getUserRole(user);
      if (userRole) {
        navigate('/admin/dashboard', { replace: true });
      } else {
        navigate('/talent/dashboard', { replace: true });
      }
    }
  }, [isAuthenticated, user, isInitializing, navigate]);

  // Show loading while checking authentication
  if (isInitializing) {
    return (
      <GlowingDotsBackground
        diameter={80}
        className="rounded-xl flex items-center justify-center h-full w-full"
        imageSrc={loginBg}
        glowColor={'rgba(255,255,255,0.06)'}
      /* Previous video props (commented as requested):
      videoSrc={["/login_video/02-optimized-1wCes4P1.webm", "/login_video/Login-optimized.webm"]}
      videoRef={loadingVideoRef}
      */
      >
        {/* Loading Spinner */}
        <div
          className="inline-block animate-spin rounded-full h-12 w-12 border-4 border-t-transparent pointer-events-auto"
          style={{
            borderColor: 'rgba(96, 165, 250, 0.3)',
            borderTopColor: 'transparent',
          }}
        />
      </GlowingDotsBackground>
    );
  }

  const validateEmail = (email) => {
    return email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    clearError();

    // Basic validation
    if (!email || !password) {
      return;
    }

    if (!validateEmail(email)) {
      return;
    }

    if (password.length < 6) {
      return;
    }

    try {
      const data = await login(email, password);
      
      // Redirect based on role system
      if (getUserRole(data.profile)) {
        navigate('/admin/dashboard');
      } else {
        navigate('/talent/dashboard');
      }
    } catch (err) {
      // Error is handled in the store
      console.error('Login failed:', err);
    }
  };

  return (
    <GlowingDotsBackground
      diameter={80}
      className="rounded-xl flex items-center justify-center h-full w-full"
      imageSrc={loginBg}
      glowColor={'rgba(255,255,255,0.06)'}
    /* Previous video props (commented as requested):
    videoSrc={["/login_video/02-optimized-1wCes4P1.webm", "/login_video/Login-optimized.webm"]}
    videoRef={videoRef}
    */
    >
      {/* Main Content */}
      <div className="relative z-30 flex flex-col items-center text-white w-full max-w-md px-4 pointer-events-auto">
        {/* Logo */}
        <div className="text-center mb-1">
          <img
            src={logoImage}
            alt="ModelSuite Talents"
            className="mx-auto w-[400px] h-auto max-w-full"
          />
        </div>

        {/* Login Card with Glass Effect */}
        <div className="w-full max-w-md backdrop-blur-xl bg-white/10 border border-white/20 rounded-xl shadow-2xl p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Email Input */}
            <div>
              <input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 rounded-lg bg-white/5 border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-white/40 transition-all backdrop-blur-sm hover:bg-white/10"
                disabled={loading}
              />
            </div>

            {/* Password Input */}
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 pr-12 rounded-lg bg-white/5 border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-white/40 transition-all backdrop-blur-sm hover:bg-white/10"
                disabled={loading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-white/60 hover:text-white/90 transition-colors"
              >
                {showPassword ? (
                  <EyeOff className="w-5 h-5" />
                ) : (
                  <Eye className="w-5 h-5" />
                )}
              </button>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-500/20 border border-red-500/30 text-red-200 text-sm py-2 px-3 rounded-lg backdrop-blur-sm">
                {error}
              </div>
            )}

            {/* Sign In Button - apply dashed/mac-like styling (keep text & behavior) */}
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 px-4 py-2 has-[&gt;svg]:px-3 w-full h-12 border-2 border-dashed border-white/30 hover:border-white/60 backdrop-blur-sm bg-white/5 hover:bg-white/10 text-white font-semibold"
            >
              {loading ? (
                <span className="flex items-center justify-center gap-2">
                  <span className="inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Signing in...
                </span>
              ) : (
                'Sign in'
              )}
            </button>
          </form>
        </div>
      </div>
    </GlowingDotsBackground>
  );
}
