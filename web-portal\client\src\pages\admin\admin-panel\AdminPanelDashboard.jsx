import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../../store/authStore";
import { useDashboardStore } from "../../../store/dashboardStore";
import { getUserRole, hasPermission } from "../../../utils/rolePermissions";
import AdminLayout from "../../../components/admin/AdminLayout";
import StatsCards from "../../../components/dashboard-homepage/StatsCards";
import RecentTalents from "../../../components/dashboard-homepage/RecentTalents";
import NoReportDashboard from "../../../components/dashboard-homepage/NoReportDashboard";
import MeetingsSection from "../../../components/dashboard-homepage/MeetingsSection";
import JibbleOnlineStatus from "../../../components/dashboard-homepage/JibbleOnlineStatus";

export default function AdminPanelDashboard() {
  const navigate = useNavigate();
  const { user, accessToken, isInitializing } = useAuthStore();
  const {
    stats,
    recentTalents,
    noReportTalents,
    upcomingMeetings,
    jibbleStatus,
    loading,
    error,
    initializeAdminDashboard,
  } = useDashboardStore();

  // Check admin access - Wait for auth initialization before checking
  useEffect(() => {
    console.log('🔍 [AdminPanelDashboard] Access check:', {
      isInitializing,
      hasUser: !!user,
      userType: user?.userType,
      roles: user?.roles,
      userRole: user ? getUserRole(user) : null,
      hasAdminPanel: user ? hasPermission(user, 'admin.panel.view') : false
    });

    // Don't check access while still initializing
    if (isInitializing) {
      console.log('⏳ [AdminPanelDashboard] Waiting for auth initialization...');
      return;
    }
    
    // After initialization, check if user has admin panel access
    if (!hasPermission(user, 'admin.panel.view')) {
      console.log('❌ [AdminPanelDashboard] Admin panel access denied - redirecting to talent dashboard');
      navigate("/talent/dashboard");
    } else {
      console.log('✅ [AdminPanelDashboard] Admin panel access granted');
    }
  }, [user, navigate, isInitializing]);

  // Fetch dashboard stats
  useEffect(() => {
    if (hasPermission(user, 'admin.panel.view') && accessToken) {
      initializeAdminDashboard(accessToken);
    }
  }, [user, accessToken]);

  // Show loading while auth is initializing
  if (isInitializing) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  // Don't render if user doesn't have admin panel access
  if (!hasPermission(user, 'admin.panel.view')) return null;

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex-1 space-y-4 p-4">
        {/* Header */}
        
        {/* Stats Cards - Row 1 */}
        <StatsCards stats={stats} />

        {/* Main Grid Layout - Use auto-rows-min for natural height */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 auto-rows-min">
          {/* Row 2, Col 1-2: Recent Talents */}
          <div className="lg:col-span-2">
            <div className="h-full">
              <RecentTalents />
            </div>
          </div>

          {/* Row 2, Col 3: Meetings */}
          <div className="lg:col-span-1">
            <div className="h-full">
              <MeetingsSection />
            </div>
          </div>

          {/* Row 2-3, Col 4: Jibble - Spans 2 rows vertically */}
          <div className="lg:col-span-1 lg:row-span-2">
            <div className="h-full">
              <JibbleOnlineStatus />
            </div>
          </div>

          {/* Row 3, Col 1-3: No Report Dashboard */}
          <div className="lg:col-span-3">
            <div className="h-full">
              <NoReportDashboard />
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}