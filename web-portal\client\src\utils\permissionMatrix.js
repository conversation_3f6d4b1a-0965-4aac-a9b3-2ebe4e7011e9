/**
 * Production-Level Permission Matrix System
 * Centralized permission management for all modules and actions
 */

// All available modules and their actions
export const PERMISSION_MATRIX = {
  // EMPLOYEE MANAGEMENT MODULE
  employee: {
    name: 'Employee Management',
    icon: 'Users',
    features: {
      talents: {
        name: 'Talent Management',
        description: 'Manage talent profiles and information',
        actions: {
          view: 'View talent profiles and data',
          create: 'Add new talents to the system',
          edit: 'Update talent information and profiles',
          delete: 'Remove talents from the system'
        }
      },
      leaderboard: {
        name: 'Leaderboard & Rankings',
        description: 'View talent rankings and XP statistics',
        actions: {
          view: 'View leaderboard and rankings',
          export: 'Export leaderboard data'
        }
      },
      payments: {
        name: 'Payment Management',
        description: 'Handle payment processing and approvals',
        actions: {
          view: 'View payment records and history',
          process: 'Process new payments',
          approve: 'Approve pending payments',
          export: 'Export payment reports'
        }
      },
      reports: {
        name: 'Reports & Analytics',
        description: 'Generate and manage various reports',
        actions: {
          view: 'View existing reports',
          generate: 'Create new reports',
          export: 'Export report data',
          schedule: 'Schedule automated reports'
        }
      },
      analytics: {
        name: 'Employee Analytics',
        description: 'View detailed analytics and insights',
        actions: {
          view: 'View analytics dashboards',
          export: 'Export analytics data',
          advanced: 'Access advanced analytics features'
        }
      },
      tasks: {
        name: 'Task Management',
        description: 'Assign and manage tasks',
        actions: {
          view: 'View tasks and assignments',
          create: 'Create new tasks',
          assign: 'Assign tasks to talents',
          edit: 'Edit existing tasks',
          delete: 'Remove tasks',
          approve: 'Approve task submissions'
        }
      },
      leave: {
        name: 'Leave Management',
        description: 'Handle leave requests and approvals',
        actions: {
          view: 'View leave requests',
          approve: 'Approve leave requests',
          reject: 'Reject leave requests',
          edit: 'Edit leave details',
          calendar: 'View leave calendar'
        }
      },
      meetings: {
        name: 'Meeting Management',
        description: 'Schedule and manage meetings',
        actions: {
          view: 'View meetings and schedules',
          schedule: 'Schedule new meetings',
          edit: 'Edit meeting details',
          cancel: 'Cancel meetings',
          manage_attendees: 'Manage meeting attendees'
        }
      },
      compliance: {
        name: 'Compliance & Attendance',
        description: 'Monitor compliance and attendance',
        actions: {
          view: 'View compliance reports',
          manage: 'Manage compliance settings',
          approve: 'Approve compliance submissions',
          export: 'Export compliance data'
        }
      },
      announcements: {
        name: 'Announcements',
        description: 'Create and manage announcements',
        actions: {
          view: 'View announcements',
          create: 'Create new announcements',
          edit: 'Edit existing announcements',
          delete: 'Remove announcements',
          publish: 'Publish/unpublish announcements'
        }
      },
      documents: {
        name: 'Document Management',
        description: 'Manage document library',
        actions: {
          view: 'View documents',
          upload: 'Upload new documents',
          edit: 'Edit document details',
          delete: 'Remove documents',
          organize: 'Organize document structure'
        }
      }
    }
  },

  // HIRING MANAGEMENT MODULE  
  hiring: {
    name: 'Hiring Management',
    icon: 'Briefcase',
    features: {
      jobs: {
        name: 'Job Management',
        description: 'Create and manage job postings',
        actions: {
          view: 'View job postings',
          create: 'Create new job postings',
          edit: 'Edit job details',
          delete: 'Remove job postings',
          publish: 'Publish/unpublish jobs',
          clone: 'Duplicate existing jobs'
        }
      },
      applications: {
        name: 'Application Management', 
        description: 'Review and manage job applications',
        actions: {
          view: 'View applications',
          review: 'Review application details',
          shortlist: 'Shortlist candidates',
          reject: 'Reject applications',
          contact: 'Contact applicants',
          export: 'Export application data'
        }
      },
      interviews: {
        name: 'Interview Management',
        description: 'Schedule and manage interviews',
        actions: {
          view: 'View interview schedules',
          schedule: 'Schedule new interviews',
          edit: 'Edit interview details',
          cancel: 'Cancel interviews',
          conduct: 'Conduct interviews',
          evaluate: 'Evaluate candidates'
        }
      },
      onboarding: {
        name: 'Onboarding Process',
        description: 'Manage new hire onboarding',
        actions: {
          view: 'View onboarding status',
          manage: 'Manage onboarding process',
          approve: 'Approve onboarding steps',
          documents: 'Handle onboarding documents'
        }
      }
    }
  },

  // ADMIN PANEL MODULE
  admin: {
    name: 'Admin Panel',
    icon: 'Shield',
    features: {
      team_management: {
        name: 'Team Management',
        description: 'Manage admin team members',
        actions: {
          view: 'View team members',
          invite: 'Invite new admins',
          edit: 'Edit admin permissions',
          remove: 'Remove team members',
          roles: 'Manage admin roles'
        }
      },
      communications: {
        name: 'Communication Center',
        description: 'Send and manage communications',
        actions: {
          view: 'View communications',
          send: 'Send new communications',
          broadcast: 'Send broadcast messages',
          templates: 'Manage message templates'
        }
      },
      system_settings: {
        name: 'System Settings',
        description: 'Configure system-wide settings',
        actions: {
          view: 'View system settings',
          edit: 'Edit system configuration',
          backup: 'Create system backups',
          logs: 'View system logs'
        }
      },
      audit_trail: {
        name: 'Audit Trail',
        description: 'View system audit logs',
        actions: {
          view: 'View audit logs',
          export: 'Export audit data',
          search: 'Search audit records'
        }
      }
    }
  }
};

// Pre-defined role templates with comprehensive permissions
export const COMPREHENSIVE_ROLES = {
  super_admin: {
    name: 'Super Administrator',
    description: 'Complete system access - CEO level',
    color: '#ef4444',
    permissions: generateFullPermissions() // All permissions
  },
  
  full_admin: {
    name: 'Full Administrator', 
    description: 'Full employee + hiring access (no admin management)',
    color: '#8b5cf6',
    permissions: [
      // Full Employee Module Access
      ...generateModulePermissions('employee'),
      // Full Hiring Module Access  
      ...generateModulePermissions('hiring'),
      // Limited Admin Access (no team management)
      'admin.communications.view', 'admin.communications.send', 'admin.communications.broadcast',
      'admin.audit_trail.view', 'admin.audit_trail.export'
    ]
  },

  hiring_manager: {
    name: 'Hiring Manager',
    description: 'Complete hiring process management',
    color: '#10b981', 
    permissions: [
      // Full Hiring Module
      ...generateModulePermissions('hiring'),
      // Basic Employee View Access
      'employee.talents.view', 'employee.reports.view', 'employee.analytics.view'
    ]
  },

  employee_manager: {
    name: 'Employee Manager',
    description: 'Complete employee operations management', 
    color: '#3b82f6',
    permissions: [
      // Full Employee Module
      ...generateModulePermissions('employee'),
      // Basic Hiring View Access
      'hiring.jobs.view', 'hiring.applications.view'
    ]
  },

  hr_specialist: {
    name: 'HR Specialist',
    description: 'HR-focused access across both modules',
    color: '#f59e0b',
    permissions: [
      // Employee HR Functions
      'employee.talents.view', 'employee.talents.edit',
      'employee.leave.view', 'employee.leave.approve', 'employee.leave.reject',
      'employee.meetings.view', 'employee.meetings.schedule', 'employee.meetings.edit',
      'employee.compliance.view', 'employee.compliance.manage',
      'employee.announcements.view', 'employee.announcements.create', 'employee.announcements.edit',
      
      // Hiring HR Functions
      'hiring.applications.view', 'hiring.applications.review', 'hiring.applications.shortlist',
      'hiring.interviews.view', 'hiring.interviews.schedule', 'hiring.interviews.edit',
      'hiring.onboarding.view', 'hiring.onboarding.manage', 'hiring.onboarding.approve'
    ]
  },

  finance_admin: {
    name: 'Finance Administrator',
    description: 'Financial operations and reporting access',
    color: '#06b6d4',
    permissions: [
      // Financial Functions
      'employee.payments.view', 'employee.payments.process', 'employee.payments.approve', 'employee.payments.export',
      'employee.reports.view', 'employee.reports.generate', 'employee.reports.export',
      'employee.analytics.view', 'employee.analytics.export',
      'employee.compliance.view', 'employee.compliance.export',
      
      // Basic View Access
      'employee.talents.view', 'employee.leaderboard.view'
    ]
  },

  team_lead: {
    name: 'Team Lead',
    description: 'Limited team coordination access',
    color: '#f59e0b',
    permissions: [
      // View-only Employee Access
      'employee.talents.view', 'employee.leaderboard.view', 'employee.reports.view',
      'employee.analytics.view', 'employee.meetings.view', 'employee.announcements.view',
      
      // Task Management
      'employee.tasks.view', 'employee.tasks.create', 'employee.tasks.assign', 'employee.tasks.edit',
      
      // Basic Hiring View
      'hiring.jobs.view', 'hiring.applications.view'
    ]
  }
};

// Helper function to generate all permissions for a module
function generateModulePermissions(moduleName) {
  const permissions = [];
  const module = PERMISSION_MATRIX[moduleName];
  
  if (module) {
    Object.keys(module.features).forEach(featureKey => {
      const feature = module.features[featureKey];
      Object.keys(feature.actions).forEach(actionKey => {
        permissions.push(`${moduleName}.${featureKey}.${actionKey}`);
      });
    });
  }
  
  return permissions;
}

// Helper function to generate all possible permissions
function generateFullPermissions() {
  const allPermissions = [];
  
  Object.keys(PERMISSION_MATRIX).forEach(moduleKey => {
    allPermissions.push(...generateModulePermissions(moduleKey));
  });
  
  return allPermissions;
}

/**
 * Check if user has specific permission
 * @param {Object} user - User object
 * @param {string} permission - Permission in format 'module.feature.action'
 * @returns {boolean}
 */
export const hasDetailedPermission = (user, permission) => {
  if (!user) return false;
  
  // Super admin always has access
  if (user.roleType === 'super_admin') return true;
  
  // Check custom permissions mode
  if (user.permissionMode === 'custom' && Array.isArray(user.customPermissions)) {
    return user.customPermissions.includes(permission);
  }
  
  // Check role-based permissions
  if (user.roleType && COMPREHENSIVE_ROLES[user.roleType]) {
    return COMPREHENSIVE_ROLES[user.roleType].permissions.includes(permission);
  }
  
  return false;
};

/**
 * Get all permissions for a user
 * @param {Object} user - User object
 * @returns {string[]} - Array of permission strings
 */
export const getUserPermissions = (user) => {
  if (!user) return [];
  
  if (user.permissionMode === 'custom' && Array.isArray(user.customPermissions)) {
    return user.customPermissions;
  }
  
  if (user.roleType && COMPREHENSIVE_ROLES[user.roleType]) {
    return COMPREHENSIVE_ROLES[user.roleType].permissions;
  }
  
  return [];
};

/**
 * Check module access level for user
 * @param {Object} user - User object  
 * @param {string} module - Module name (employee, hiring, admin)
 * @returns {Object} - Access levels by feature
 */
export const getModuleAccess = (user, module) => {
  const userPermissions = getUserPermissions(user);
  const access = {};
  
  if (PERMISSION_MATRIX[module]) {
    Object.keys(PERMISSION_MATRIX[module].features).forEach(featureKey => {
      access[featureKey] = {};
      const feature = PERMISSION_MATRIX[module].features[featureKey];
      
      Object.keys(feature.actions).forEach(actionKey => {
        const permission = `${module}.${featureKey}.${actionKey}`;
        access[featureKey][actionKey] = userPermissions.includes(permission);
      });
    });
  }
  
  return access;
};

export default {
  PERMISSION_MATRIX,
  COMPREHENSIVE_ROLES,
  hasDetailedPermission,
  getUserPermissions,
  getModuleAccess
};