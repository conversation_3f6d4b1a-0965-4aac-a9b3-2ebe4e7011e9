/**
 * Centralized Route-Permission Mapping
 * Maps routes to their required permissions from permissionMatrix.js
 */

// Route permissions mapping - single source of truth
export const ROUTE_PERMISSIONS = {
  // Employee Module Routes
  '/admin/dashboard': 'employee.talents.view', // Basic access requirement
  '/admin/talents': 'employee.talents.view',
  '/admin/talents/*': 'employee.talents.view',
  '/admin/leaderboard': 'employee.leaderboard.view',
  '/admin/reports': 'employee.reports.view',
  '/admin/reports/*': 'employee.reports.view',
  '/admin/payments': 'employee.payments.view',
  '/admin/tasks': 'employee.tasks.view',
  '/admin/tasks/*': 'employee.tasks.view',
  '/admin/meetings': 'employee.meetings.view',
  '/admin/meetings/*': 'employee.meetings.view',
  '/admin/meetings/manage': 'employee.meetings.view',
  '/admin/announcements': 'employee.announcements.view',
  '/admin/notifications': 'employee.announcements.view',
  '/admin/leave': 'employee.leave.view',
  '/admin/documents': 'employee.documents.view',
  '/admin/documents/*': 'employee.documents.view',
  '/admin/resources': 'employee.documents.view',
  '/admin/checkups': 'employee.compliance.view',
  '/admin/compliance': 'employee.compliance.view',
  '/admin/employee-analytics': 'employee.analytics.view',
  '/admin/screenshot-discrepancies': 'employee.compliance.view',
  '/admin/screenshot-discrepancies/*': 'employee.compliance.view',
  '/admin/ai-feedback': 'employee.reports.view',
  '/admin/weekly-summaries': 'employee.reports.view',
  
  // Hiring Module Routes
  '/admin/hiring/dashboard': 'hiring.jobs.view',
  '/admin/hiring/jobs': 'hiring.jobs.view',
  '/admin/hiring/jobs/*': 'hiring.jobs.view',
  '/admin/hiring/applications': 'hiring.applications.view',
  '/admin/hiring/applications/*': 'hiring.applications.view',
  '/admin/hiring/interviews': 'hiring.interviews.view',
  '/admin/hiring/interviews/*': 'hiring.interviews.view',
  '/admin/hiring/onboarding': 'hiring.onboarding.view',
  '/admin/hiring/onboarding/*': 'hiring.onboarding.view',
  
  // Admin Panel Routes
  '/admin/admin-panel/dashboard': 'admin.communications.view',
  '/admin/admin-panel/talents': 'employee.talents.view',
  '/admin/admin-panel/announcements': 'employee.announcements.view',
  '/admin/admin-panel/payments': 'employee.payments.view',
  '/admin/admin-panel/communications': 'admin.communications.view',
  '/admin/admin-panel/meetings': 'employee.meetings.view',
  '/admin/admin-panel/notifications': 'admin.communications.view',
  '/admin/admin-panel/audit-trail': 'admin.audit_trail.view',
  '/admin/admin-panel/team-management': 'admin.team_management.view',
  
  // Communication & Audit
  '/admin/communication-center': 'admin.communications.view',
  '/admin/audit-trail': 'admin.audit_trail.view',
  '/admin/claims-dashboard': 'employee.compliance.view'
};

/**
 * Get required permission for a route path
 * Supports exact matches and wildcard patterns
 */
export const getRoutePermission = (routePath) => {
  // First try exact match
  if (ROUTE_PERMISSIONS[routePath]) {
    return ROUTE_PERMISSIONS[routePath];
  }
  
  // Then try wildcard matches
  for (const [pattern, permission] of Object.entries(ROUTE_PERMISSIONS)) {
    if (pattern.endsWith('/*')) {
      const basePath = pattern.slice(0, -2); // Remove /*
      if (routePath.startsWith(basePath)) {
        return permission;
      }
    }
  }
  
  return null; // No permission required
};

/**
 * Check if a route requires permission
 */
export const isProtectedRoute = (routePath) => {
  return getRoutePermission(routePath) !== null;
};

/**
 * Get all routes that require a specific permission
 */
export const getRoutesByPermission = (permission) => {
  return Object.entries(ROUTE_PERMISSIONS)
    .filter(([route, perm]) => perm === permission)
    .map(([route]) => route);
};

/**
 * Validate that all routes have valid permissions
 * (for development/testing)
 */
export const validateRoutePermissions = () => {
  const invalidRoutes = [];
  
  for (const [route, permission] of Object.entries(ROUTE_PERMISSIONS)) {
    // Basic validation - permission should follow module.feature.action format
    const parts = permission.split('.');
    if (parts.length !== 3) {
      invalidRoutes.push({ route, permission, error: 'Invalid format' });
    }
  }
  
  return invalidRoutes;
};

export default {
  ROUTE_PERMISSIONS,
  getRoutePermission,
  isProtectedRoute,
  getRoutesByPermission,
  validateRoutePermissions
};