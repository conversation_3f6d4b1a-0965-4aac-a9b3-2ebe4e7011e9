import { useNavigate } from 'react-router-dom';
import useNotificationStore from '../../store/notificationStore';

// Icon mapping for notification types
const getNotificationIcon = (type) => {
  const icons = {
    task_assigned: '📋',
    task_due_soon: '⏰',
    task_completed: '✅',
    task_commented: '💬',
    report_reminder: '📝',
    report_approved: '✓',
    report_rejected: '✗',
    checkin_reminder: '👋',
    checkin_streak: '🔥',
    badge_earned: '🏆',
    xp_milestone: '⭐',
    admin_message: '📢',
    security_alert: '🔒',
    system_update: '🔄',
  };
  return icons[type] || '🔔';
};

// Priority color mapping - macOS style
const getPriorityColor = (priority) => {
  const colors = {
    urgent: 'rgba(255, 255, 255, 0.12)',
    high: 'rgba(255, 255, 255, 0.1)',
    normal: 'rgba(255, 255, 255, 0.08)',
    low: 'rgba(255, 255, 255, 0.06)',
  };
  return colors[priority] || colors.normal;
};

// Format time ago
const formatTimeAgo = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
};

export default function NotificationItem({ notification, isLast, onClose }) {
  const navigate = useNavigate();
  const { markNotificationAsRead } = useNotificationStore();

  const handleActionClick = async (e) => {
    e.stopPropagation();

    // Mark as read if unread
    if (!notification.isRead) {
      await markNotificationAsRead(notification._id);
    }

    // Navigate to action URL if provided
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
      onClose && onClose();
    }
  };

  const handleCardClick = async () => {
    // Mark as read if unread
    if (!notification.isRead) {
      await markNotificationAsRead(notification._id);
    }

    // Navigate to action URL if provided
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
      onClose && onClose();
    }
  };

  return (
    <div
      onClick={handleCardClick}
      className="rounded-xl p-2.5 backdrop-blur-xl border transition-all duration-200 hover:scale-[1.005] mb-1.5 relative cursor-pointer"
      style={{
        background: notification.isRead
          ? 'rgba(255, 255, 255, 0.03)'
          : 'rgba(255, 255, 255, 0.07)',
        borderColor: notification.isRead
          ? 'rgba(255, 255, 255, 0.06)'
          : 'rgba(255, 255, 255, 0.12)',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.28)',
      }}
    >
      {/* Date Badge and Action Button - Top Right */}
      <div className="absolute top-2 right-2 flex items-center gap-2">
        <span
          className="text-[9px] font-medium tracking-wide"
          style={{ color: '#94A3B8' }}
        >
          {formatTimeAgo(notification.createdAt)}
        </span>
        {!notification.isRead && (
          <span
            className="flex-shrink-0 w-1.5 h-1.5 rounded-full"
            style={{ background: '#94A3B8' }}
          ></span>
        )}
        {notification.actionUrl && (
          <div
            className="w-5 h-5 rounded-full flex items-center justify-center transition-all duration-200"
            style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%)',
              border: '1px solid rgba(255, 255, 255, 0.15)',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)',
            }}
          >
            <svg
              className="w-3 h-3"
              style={{ color: '#E2E8F0' }}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              strokeWidth="2.5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        )}
      </div>

      <div className="flex items-start gap-3 pr-12">
        {/* Icon - Enhanced macOS style */}
        <div
          className="flex-shrink-0 w-10 h-10 rounded-xl flex items-center justify-center text-lg relative overflow-hidden"
          style={{
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
          }}
        >
          <div className="relative z-10">
            {getNotificationIcon(notification.type)}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0 pt-0.5">
          <h4
            className="text-xs font-semibold leading-snug mb-1.5"
            style={{
              color: notification.isRead ? '#CBD5E1' : '#F8FAFC',
              letterSpacing: '-0.02em',
            }}
          >
            {notification.title}
          </h4>

          <p
            className="text-[11px] leading-relaxed line-clamp-2"
            style={{ color: '#94A3B8' }}
          >
            {notification.message}
          </p>
        </div>
      </div>
    </div>
  );
}
