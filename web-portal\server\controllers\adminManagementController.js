import Admin from '../models/Admin.js';
import Talent from '../models/Talent.js';
import bcrypt from 'bcrypt';
import { generatePassword } from '../utils/passwordGenerator.js';
import { sendAdminCreationEmail } from '../services/emailService.js';

// Available roles configuration - Updated comprehensive system
const AVAILABLE_ROLES = {
  super_admin: {
    name: 'Super Administrator',
    description: 'Complete system access - CEO level',
    permissions: ['*'] // Wildcard for all permissions
  },
  full_admin: {
    name: 'Full Administrator', 
    description: 'Full employee + hiring access (no admin management)',
    permissions: [
      // Employee permissions
      'employee.*',
      // Hiring permissions  
      'hiring.*',
      // Limited admin permissions
      'admin.communications.*', 'admin.audit_trail.*'
    ]
  },
  hiring_manager: {
    name: 'Hiring Manager',
    description: 'Complete hiring process management',
    permissions: [
      'hiring.*', 
      'employee.talents.view', 'employee.reports.view', 'employee.analytics.view'
    ]
  },
  employee_manager: {
    name: 'Employee Manager', 
    description: 'Complete employee operations management',
    permissions: [
      'employee.*', 
      'hiring.jobs.view', 'hiring.applications.view'
    ]
  },
  hr_specialist: {
    name: 'HR Specialist',
    description: 'HR-focused access across both modules',
    permissions: [
      'employee.talents.view', 'employee.talents.edit',
      'employee.leave.*', 'employee.meetings.*', 'employee.compliance.*',
      'employee.announcements.*',
      'hiring.applications.*', 'hiring.interviews.*', 'hiring.onboarding.*'
    ]
  },
  finance_admin: {
    name: 'Finance Administrator',
    description: 'Financial operations and reporting access',
    permissions: [
      'employee.payments.*', 'employee.reports.*', 'employee.analytics.*',
      'employee.compliance.view', 'employee.compliance.export',
      'employee.talents.view', 'employee.leaderboard.view'
    ]
  },
  team_lead: {
    name: 'Team Lead',
    description: 'Limited team coordination access',
    permissions: [
      'employee.talents.view', 'employee.leaderboard.view', 'employee.reports.view',
      'employee.analytics.view', 'employee.meetings.view', 'employee.announcements.view',
      'employee.tasks.*',
      'hiring.jobs.view', 'hiring.applications.view'
    ]
  }
};

// GET /api/admin/management/roles
export const getAvailableRoles = async (req, res) => {
  try {
    res.json({
      success: true,
      roles: AVAILABLE_ROLES
    });
  } catch (error) {
    console.error('Error fetching available roles:', error);
    res.status(500).json({ error: 'Failed to fetch available roles' });
  }
};

// GET /api/admin/management/admins
export const getAllAdmins = async (req, res) => {
  try {
    const { page = 1, limit = 20, roleType, status, search } = req.query;
    
    // Build query
    let query = {};
    
    if (roleType && roleType !== 'all') {
      query.roleType = roleType;
    }
    
    if (status && status !== 'all') {
      query.isActive = status === 'active';
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Get paginated results
    const admins = await Admin.find(query)
      .select('-password')
      .populate('linkedTalentId', 'talentId name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .lean();
    
    const total = await Admin.countDocuments(query);
    
    // Add role information and permission details
    const adminsWithRoleInfo = admins.map(admin => ({
      ...admin,
      roleInfo: admin.roleType ? AVAILABLE_ROLES[admin.roleType] : null,
      // Include permission mode and custom permissions for frontend
      permissionMode: admin.permissionMode || 'role',
      customPermissions: admin.customPermissions || []
    }));
    
    res.json({
      success: true,
      data: {
        admins: adminsWithRoleInfo,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching admins:', error);
    res.status(500).json({ error: 'Failed to fetch admin users' });
  }
};

// GET /api/admin/management/admins/:id
export const getAdminDetails = async (req, res) => {
  try {
    const { id } = req.params;
    
    const admin = await Admin.findById(id)
      .select('-password')
      .populate('linkedTalentId', 'talentId name email roles')
      .lean();
    
    if (!admin) {
      return res.status(404).json({ error: 'Admin not found' });
    }
    
    // Add role information
    const adminWithRoleInfo = {
      ...admin,
      roleInfo: AVAILABLE_ROLES[admin.roleType] || null
    };
    
    res.json({
      success: true,
      data: adminWithRoleInfo
    });
  } catch (error) {
    console.error('Error fetching admin details:', error);
    res.status(500).json({ error: 'Failed to fetch admin details' });
  }
};

// POST /api/admin/management/admins
export const createAdmin = async (req, res) => {
  try {
    const { name, email, roleType, canAccessTalentPortal = false, sendWelcomeEmail = true } = req.body;
    
    // Validation
    if (!name || !email || !roleType) {
      return res.status(400).json({ 
        error: 'Name, email, and role type are required' 
      });
    }
    
    if (!AVAILABLE_ROLES[roleType]) {
      return res.status(400).json({ 
        error: 'Invalid role type',
        availableRoles: Object.keys(AVAILABLE_ROLES)
      });
    }
    
    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ email });
    if (existingAdmin) {
      return res.status(400).json({ error: 'Admin with this email already exists' });
    }
    
    // Generate temporary password
    const tempPassword = generatePassword();
    const hashedPassword = await bcrypt.hash(tempPassword, 12);
    
    // Create admin
    const admin = new Admin({
      name,
      email,
      password: hashedPassword,
      roleType,
      canAccessTalentPortal,
      isActive: true,
      mustChangePassword: true,
      createdBy: req.user._id
    });
    
    await admin.save();
    
    // Send welcome email if requested
    if (sendWelcomeEmail) {
      try {
        await sendAdminCreationEmail(email, name, tempPassword, roleType);
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
        // Don't fail the admin creation if email fails
      }
    }
    
    // Return admin without password
    const adminResponse = await Admin.findById(admin._id)
      .select('-password')
      .lean();
    
    res.status(201).json({
      success: true,
      data: {
        admin: {
          ...adminResponse,
          roleInfo: AVAILABLE_ROLES[adminResponse.roleType]
        },
        tempPassword: sendWelcomeEmail ? null : tempPassword // Only return password if email wasn't sent
      },
      message: `Admin created successfully. ${sendWelcomeEmail ? 'Welcome email sent.' : 'Temporary password provided.'}`
    });
  } catch (error) {
    console.error('Error creating admin:', error);
    res.status(500).json({ error: 'Failed to create admin user' });
  }
};

// PUT /api/admin/management/admins/:id/role
export const updateAdminRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { roleType, canAccessTalentPortal, permissionMode, customPermissions } = req.body;
    
    console.log('🔧 Admin role update request:', { 
      adminId: id, 
      roleType, 
      permissionMode, 
      customPermissions,
      canAccessTalentPortal 
    });
    
    // Validate permission mode
    if (!permissionMode || !['role', 'custom'].includes(permissionMode)) {
      return res.status(400).json({ 
        error: 'Invalid permission mode. Must be "role" or "custom"' 
      });
    }
    
    // Validate based on permission mode
    if (permissionMode === 'role') {
      if (!roleType || !AVAILABLE_ROLES[roleType]) {
        return res.status(400).json({ 
          error: 'Invalid role type for role-based permissions',
          availableRoles: Object.keys(AVAILABLE_ROLES)
        });
      }
    } else if (permissionMode === 'custom') {
      if (!Array.isArray(customPermissions) || customPermissions.length === 0) {
        return res.status(400).json({ 
          error: 'Custom permissions array is required for custom permission mode' 
        });
      }
      
      // Validate custom permission values
      const validPermissionPattern = /^(employee|hiring|admin)\.[a-z_]+\.(view|create|edit|delete|approve|manage|process|export|schedule|assign|publish|review|shortlist|reject|contact|conduct|evaluate|invite|remove|roles|send|broadcast|templates|backup|logs|search|\*)$/;
      const invalidPermissions = customPermissions.filter(p => !validPermissionPattern.test(p));
      if (invalidPermissions.length > 0) {
        return res.status(400).json({ 
          error: `Invalid custom permissions format: ${invalidPermissions.join(', ')}`,
          expectedFormat: 'module.feature.action (e.g., employee.talents.view, hiring.jobs.create)'
        });
      }
    }
    
    // Prevent self-demotion from super_admin (only for role mode)
    if (permissionMode === 'role' && 
        req.user._id.toString() === id && 
        req.user.roleType === 'super_admin' && 
        roleType !== 'super_admin') {
      return res.status(400).json({ 
        error: 'Cannot remove your own super admin privileges' 
      });
    }
    
    // Prepare update data
    const updateData = {
      permissionMode,
      ...(canAccessTalentPortal !== undefined && { canAccessTalentPortal }),
      updatedBy: req.user._id,
      updatedAt: new Date()
    };
    
    if (permissionMode === 'role') {
      updateData.roleType = roleType;
      updateData.customPermissions = []; // Clear custom permissions when using role mode
    } else {
      updateData.customPermissions = customPermissions;
      updateData.roleType = null; // Clear role when using custom mode
    }
    
    const admin = await Admin.findByIdAndUpdate(id, updateData, { new: true })
      .select('-password')
      .lean();
    
    if (!admin) {
      return res.status(404).json({ error: 'Admin not found' });
    }
    
    res.json({
      success: true,
      data: {
        ...admin,
        roleInfo: admin.roleType ? AVAILABLE_ROLES[admin.roleType] : null
      },
      message: `Admin permissions updated successfully (${permissionMode} mode)`
    });
  } catch (error) {
    console.error('Error updating admin role:', error);
    res.status(500).json({ error: 'Failed to update admin permissions' });
  }
};

// PUT /api/admin/management/admins/:id/status
export const toggleAdminStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;
    
    // Prevent self-deactivation
    if (req.user._id.toString() === id && !isActive) {
      return res.status(400).json({ 
        error: 'Cannot deactivate your own account' 
      });
    }
    
    const admin = await Admin.findByIdAndUpdate(
      id,
      { 
        isActive,
        updatedBy: req.user._id,
        updatedAt: new Date()
      },
      { new: true }
    ).select('-password').lean();
    
    if (!admin) {
      return res.status(404).json({ error: 'Admin not found' });
    }
    
    res.json({
      success: true,
      data: {
        ...admin,
        roleInfo: AVAILABLE_ROLES[admin.roleType]
      },
      message: `Admin ${isActive ? 'activated' : 'deactivated'} successfully`
    });
  } catch (error) {
    console.error('Error updating admin status:', error);
    res.status(500).json({ error: 'Failed to update admin status' });
  }
};

// DELETE /api/admin/management/admins/:id
export const deleteAdmin = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Prevent self-deletion
    if (req.user._id.toString() === id) {
      return res.status(400).json({ 
        error: 'Cannot delete your own account' 
      });
    }
    
    // Soft delete by deactivating
    const admin = await Admin.findByIdAndUpdate(
      id,
      { 
        isActive: false,
        deletedAt: new Date(),
        deletedBy: req.user._id
      },
      { new: true }
    ).select('-password').lean();
    
    if (!admin) {
      return res.status(404).json({ error: 'Admin not found' });
    }
    
    res.json({
      success: true,
      message: 'Admin deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting admin:', error);
    res.status(500).json({ error: 'Failed to delete admin' });
  }
};