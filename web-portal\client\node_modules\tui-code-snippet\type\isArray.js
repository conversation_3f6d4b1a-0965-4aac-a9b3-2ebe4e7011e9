/**
 * @fileoverview Check whether the given variable is an instance of <PERSON>rray or not.
 * <AUTHOR> FE Development Lab <<EMAIL>>
 */

'use strict';

/**
 * Check whether the given variable is an instance of <PERSON>rray or not.
 * If the given variable is an instance of Array, return true.
 * @param {*} obj - Target for checking
 * @returns {boolean} Is array instance?
 * @memberof module:type
 */
function isArray(obj) {
  return obj instanceof Array;
}

module.exports = isArray;
